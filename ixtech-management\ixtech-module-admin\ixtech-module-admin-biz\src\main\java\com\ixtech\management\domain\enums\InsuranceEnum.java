package com.ixtech.management.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum InsuranceEnum {
    /**
     * 1-满油取还
     */
    BASIC("基本险",1),

    /**
     * 2-同油量取还
     */
    ALLPAY("全额险",3),

    SUPER_ALL_PAY("综合险",4);


    private final String name;
    private final int code;

    private static final Map<Integer, InsuranceEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(InsuranceEnum::getCode, Function.identity()));

    private static final Map<String, InsuranceEnum> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(InsuranceEnum::getName, Function.identity()));

    InsuranceEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 对应的枚举，找不到返回null
     */
    public static InsuranceEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据名称获取枚举
     * @param name 名称
     * @return 对应的枚举，找不到返回null
     */
    public static InsuranceEnum getByName(String name) {
        return NAME_MAP.get(name);
    }

    /**
     * 检查编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    public static boolean containsCode(Integer code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 检查名称是否存在
     * @param name 名称
     * @return 是否存在
     */
    public static boolean containsName(String name) {
        return NAME_MAP.containsKey(name);
    }

    /**
     * 获取所有编码
     * @return 编码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(values()).map(InsuranceEnum::getCode).toArray(Integer[]::new);
    }

    /**
     * 获取所有名称
     * @return 名称数组
     */
    public static String[] getAllNames() {
        return Arrays.stream(values()).map(InsuranceEnum::getName).toArray(String[]::new);
    }

    @Override
    public String toString() {
        return "InsuranceEnum{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
