package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 租赁地点信息
 *
 * @author: Phili
 * @date： 2025/3/31
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehRentLocInfoResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 子章节列表
  */
 private List<SubSection> subSection;

 /**
  * 标题
  */
 private String title;

 /**
  * 类型
  */
 private String type;

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class SubSection {

  /**
   * 列表项集合
   */
  private List<ListItem> listItem;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class ListItem {

  /**
   * 内容
   */
  private String content;

  /**
   * 列表项编号
   */
  private Integer listItem;
 }
}
