<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.JipinzucheCarListMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.JipinzucheCarList">
            <id property="id" column="id" />
            <result property="active" column="active" />
            <result property="deleted" column="deleted" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="stockid" column="stockid" />
            <result property="platecode" column="platecode" />
            <result property="color" column="color" />
            <result property="mileage" column="mileage" />
            <result property="maintain" column="maintain" />
            <result property="enginecode" column="enginecode" />
            <result property="framecode" column="framecode" />
            <result property="fuelmodel" column="fuelmodel" />
            <result property="time" column="time" />
            <result property="ip" column="ip" />
            <result property="mid" column="mid" />
            <result property="ordertimes" column="ordertimes" />
            <result property="status" column="status" />
            <result property="gpsImei" column="gps_imei" />
            <result property="accident" column="accident" />
            <result property="dayprice" column="dayprice" />
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,stockid,
        platecode,color,mileage,maintain,enginecode,
        framecode,fuelmodel,time,ip,mid,
        ordertimes,status,gps_imei,accident,dayprice
    </sql>
    <sql id="Base_delete">
        and active = 1 and deleted = 0
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
            from jipinzuche_car_list
            where id = #{id}
        <include refid="Base_delete" />
    </select>
    <select id="selectByStockId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_list
        where stockid = #{id}
        <include refid="Base_delete" />
    </select>
</mapper>
