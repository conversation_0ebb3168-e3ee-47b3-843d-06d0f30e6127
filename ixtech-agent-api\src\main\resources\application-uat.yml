server:
  port: 16001
  #servlet:
  #   context-path:

spring:
  application:
    name: agentapi
  datasource:
    url: *************************************************************************************************************************************************************
    username: ixtech_mgt
    password: R1gfh!34LkA124(7Fg
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 10
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 10000
      validation-query: SELECT 1
      validation-query-timeout: 5
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      remove-abandoned: true
      remove-abandoned-timeout: 60
      log-abandoned: true

  # feign相关配置
  cloud:
    discovery:
      client:
        simple:
          instances:
            ordersrv: # 服务名
              - uri: http://************:15001
                metadata:
                  instanceId: instance-1
            productsrv: # 服务名
              - uri: http://************:15000
                metadata:
                  instanceId: instance-1
            vendorbasicsrv: # 服务名
              - uri: http://************:15002
                metadata:
                  instanceId: instance-1

    loadbalancer:
      enabled: true # 启用 LoadBalancer

config:
  redis:
    host: ************
    port: 6379
    password: Cz#nyson/3Qp
    index: 0
    timeout: 3000
    maxWait: -1
    maxIdle: 60
    minIdle: 20
    MaxWaitMillis: 10000
    MaxTotal: 60
    TestOnBorrow: true
    UsePool: true


mybatis:
  mapper-locations: classpath*:mybatis/mapper/*.xml
  # 目的是为了省略resultType里的代码量
  #type-aliases-package: com.ixtech.global.repo.entity
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  config: classpath:logback-spring.xml # 指定 Logback 配置文件
  level:
    com.ixtech.global.repo.mapper: debug
    com.ixtech.global.**.client: DEBUG # 设置 Feign 客户端的日志级别为 DEBUG

#feign:
#  client:
#    config:
#      productsrv: # 与 @FeignClient 的 name 一致
#        loggerLevel: FULL # 设置 Feign 日志级别为 FULL
#      productsrv: # 与 @FeignClient 的 name 一致
#        loggerLevel: FULL # 设置 Feign 日志级别为 FULL

#api:
#  auth:
#    credentials:
#      zzc: d04f4c4ad62b93f4d7c025acd7651114
#      qeeq: 9a2173c783f219028dab755a31e82c89

whitelist:
  enabled: false
  ips:
    - 127.0.0.1
    - *************
    - **************
    - *************
    - *************
    - **************
    - *************
    - *************
    - *************
    - *************
    - ***************
    - ***************
    - *************

    # 租租车
    - ***********
    - ***********
    - **********
    - ************
    - *************
    - ************
    - *************
    - *************
    - ************
    - ************
    - *************
    - *************
    - ************
    - ************
    - ************
  exclude-paths:
    - /public/**
    - /health

management:
  endpoints:
    web:
      exposure:
        include: prometheus,metrics
    prometheus:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}