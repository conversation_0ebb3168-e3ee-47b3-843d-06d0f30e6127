package com.ixtech.management.repo.repository;

import com.ixtech.management.integration.internal.req.OrderQueryReq;
import com.ixtech.management.repo.entity.JipinzucheCarList;
import com.ixtech.management.repo.entity.JipinzucheCarOrder;
import com.ixtech.management.repo.entity.JipinzucheCarOrderPO;
import com.ixtech.management.repo.mapper.JipinzucheCarListMapper;
import com.ixtech.management.repo.mapper.JipinzucheCarOrderMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class JipinzucheCarOrderRepository {

    @Resource
    private JipinzucheCarOrderMapper jipinzucheCarOrderMapper;

    public int insert(JipinzucheCarOrderPO carOrder) {
        return jipinzucheCarOrderMapper.insertSelective(carOrder);
    }

    public JipinzucheCarOrderPO selectById(Long id) {
        return jipinzucheCarOrderMapper.selectById(id);
    }

    public JipinzucheCarOrder selectBySourceOrderCode(String sourceOrdercode) {
        return jipinzucheCarOrderMapper.selectBySourceOrderCode(sourceOrdercode);
    }

    public Long selectCountBySourceOrderCode(String sourceOrdercode) {
        return jipinzucheCarOrderMapper.selectCountBySourceOrderCode(sourceOrdercode);
    }

    public JipinzucheCarOrderPO selectBySourceAndSourceOrderCode(OrderQueryReq orderQueryReq) {
        return jipinzucheCarOrderMapper.selectBySourceAndSourceOrderCode(orderQueryReq);
    }
}
