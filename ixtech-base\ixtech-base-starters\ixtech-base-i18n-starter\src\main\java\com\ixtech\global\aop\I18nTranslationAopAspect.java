package com.ixtech.global.aop;

import com.ixtech.global.I18nTranslationExecutor;
import com.ixtech.global.annotation.I18nField;
import com.ixtech.global.constant.I18nConstants;
import com.ixtech.global.interceptor.LanguageContextHolder;
import com.ixtech.global.util.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

/**
 * 国际化自动翻译AOP
 * 解析方法返回参数中的@I18nField字段（翻译key），获取翻译内容，设置到绑定的字段上
 *
 * <AUTHOR> hu
 * @date 2025/6/10 08:48
 */
@Aspect
@Slf4j
public class I18nTranslationAopAspect {

    // 线程本地变量：跟踪已处理对象，防止循环引用
    private final ThreadLocal<IdentityHashMap<Object, Boolean>> processedObjects = ThreadLocal.withInitial(IdentityHashMap::new);

    private I18nTranslationExecutor i18nTranslationExecutor;

    public I18nTranslationAopAspect(I18nTranslationExecutor i18nTranslationExecutor) {
        this.i18nTranslationExecutor = i18nTranslationExecutor;
    }

    /**
     * 核心环绕通知：处理国际化翻译
     */
    @Around("@annotation(com.ixtech.global.annotation.I18nTranslation)")
    public Object processI18nTranslation(ProceedingJoinPoint joinPoint) throws Throwable {

        // 执行目标方法
        Object result = joinPoint.proceed();
        if (result == null) {
            return null;
        }

        try {

            // 获取请求语言
            String languageCode = Optional.ofNullable(LanguageContextHolder.getPrimaryLanguage())
                    .map(RequestUtils.LanguagePriority::languageCode).orElse(null);
            if (languageCode == null) {
                return result;
            }

            // 初始化翻译任务和待查询的key集合
            List<TranslationTask> translationTasks = new ArrayList<>();
            Set<String> collectedKeys = new HashSet<>();

            // 递归处理返回结果，收集翻译任务和key
            processObject(result, translationTasks, collectedKeys, 1);

            // 批量查询翻译内容
            Map<String, String> translationMap = null;
            if (!collectedKeys.isEmpty()) {
                translationMap = i18nTranslationExecutor.getTranslations(collectedKeys, languageCode);
            }

            // 执行所有翻译任务
            if (MapUtils.isNotEmpty(translationMap)) {
                for (TranslationTask task : translationTasks) {
                    task.applyTranslation(translationMap);
                }
            }

        } catch (Exception e) {
            log.error("翻译失败：{}", e.getMessage(), e);
        } finally {
            // 清理线程本地变量
            processedObjects.remove();
        }

        return result;
    }

    /**
     * 递归处理对象：收集翻译任务和key
     */
    private void processObject(Object obj, List<TranslationTask> tasks, Set<String> resourceKeys, int level) {

        // 处理null和基本类型
        if (obj == null || isPrimitiveType(obj.getClass())) {
            return;
        }

        // 避免递归太深带来的性能损耗，同时避免未考虑到的错误导致递归无法结束，造成栈溢出
        if (level > I18nConstants.MAX_RECURSIVE_SEARCH_DEPTH) {
            log.warn("递归层级过深，已忽略对更深层数据进行翻译，最大搜索深度：" + I18nConstants.MAX_RECURSIVE_SEARCH_DEPTH);
            return;
        }

        // 检测循环引用（防止栈溢出）
        if (processedObjects.get().containsKey(obj)) {
            return;
        }
        processedObjects.get().put(obj, Boolean.TRUE);

        // 处理数组类型（新增）
        if (obj.getClass().isArray()) {
            processArray((Object[]) obj, tasks, resourceKeys, level);
            return;
        }

        // 处理集合类型
        if (obj instanceof Collection) {
            processCollection((Collection<?>) obj, tasks, resourceKeys, level);
            return;
        }

        // 处理Map类型
        if (obj instanceof Map) {
            processMap((Map<?, ?>) obj, tasks, resourceKeys, level);
            return;
        }

        // 处理普通Java对象
        Class<?> clazz = obj.getClass();
        Field[] allFields = clazz.getDeclaredFields();

        // 遍历所有字段，收集翻译任务和处理嵌套对象
        for (Field field : allFields) {
            try {
                field.setAccessible(true);
                Object fieldValue = field.get(obj);

                // 收集i18n字段的翻译任务
                I18nField i18nFieldAnnotation = field.getAnnotation(I18nField.class);
                if (i18nFieldAnnotation == null) {
                    // 递归处理嵌套对象
                    processObject(fieldValue, tasks, resourceKeys, level + 1);
                    continue;
                }

                // 获取需要翻译的字段，如果@I18nField注解中指定了link属性，
                // 则link属性指定的字段为需要翻译的字段，未指定link属性则当前字段为需要翻译的字段
                Field targetField = field;
                String linkFieldName = i18nFieldAnnotation.link();
                if (StringUtils.isNotBlank(linkFieldName)) {
                    try {
                        targetField = clazz.getDeclaredField(linkFieldName.trim());
                        targetField.setAccessible(true);
                    } catch (Exception e) {
                        log.warn("类：{}中未找到待翻译的目标字段：{}，跳过@I18nField字段：{}的翻译任务", clazz.getName(), linkFieldName, field.getName());
                        continue;
                    }
                }
                if (targetField.getType() != String.class) {
                    // 待翻译字段不是String类型，忽略
                    log.warn("类：{}中待翻译的字段：{}不是String类型，忽略", clazz.getName(), targetField.getName());
                    continue;
                }

                if (Modifier.isFinal(targetField.getModifiers())) {
                    log.warn("类：{}中待翻译的字段：{}是final类型，忽略", clazz.getName(), targetField.getName());
                    continue;
                }

                // 获取翻译资源key
                String resourceKey = i18nFieldAnnotation.resourceKey();
                resourceKey = StringUtils.isBlank(resourceKey) ? (String) fieldValue : resourceKey;
                if (StringUtils.isBlank(resourceKey)) {
                    // 找不到翻译资源key，忽略
                    // log.warn("类：{}中待翻译的字段：{}找不到对应的翻译资源key，忽略", clazz.getName(), targetField.getName());
                    continue;
                }

                tasks.add(new TranslationTask(obj, targetField, resourceKey));
                resourceKeys.add(resourceKey);

            } catch (IllegalAccessException e) {
                log.error("翻译器在解析类：{}的字段：{}时发生错误", clazz.getName(), field.getName(), e);
            }
        }

    }

    /**
     * 处理数组类型
     */
    private void processArray(Object[] array, List<TranslationTask> tasks, Set<String> keys, int level) {
        for (Object item : array) {
            processObject(item, tasks, keys, level);
        }
    }

    /**
     * 处理集合类型
     */
    private void processCollection(Collection<?> collection, List<TranslationTask> tasks, Set<String> keys, int level) {
        for (Object item : collection) {
            processObject(item, tasks, keys, level);
        }
    }

    /**
     * 处理Map类型
     */
    private void processMap(Map<?, ?> map, List<TranslationTask> tasks, Set<String> keys, int level) {
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            processObject(entry.getValue(), tasks, keys, level);
        }
    }

    /**
     * 判断是否为基本类型（无需国际化处理）
     */
    private boolean isPrimitiveType(Class<?> clazz) {
        return clazz.isPrimitive()
                || Number.class.isAssignableFrom(clazz)
                // 基本类型数组
                || (clazz.isArray() && clazz.getComponentType().isPrimitive())
                || clazz == Boolean.class
                || clazz == Character.class
                || clazz == String.class
                || clazz == LocalDateTime.class
                || clazz == LocalDate.class
                || clazz == LocalTime.class
                || clazz == Date.class;
    }

    /**
     * 翻译任务内部类（处理字段赋值逻辑）
     *
     * @param targetObject 目标对象
     * @param targetField  待翻译的原始字段
     * @param resourceKey  翻译资源key
     */
    private record TranslationTask(Object targetObject, Field targetField, String resourceKey) {
        /**
         * 应用翻译
         */
        public void applyTranslation(Map<String, String> translationMap) {
            try {
                // 获取翻译内容
                String translation = translationMap.get(resourceKey);
                if (StringUtils.isBlank(translation)) {
                    return;
                }
                // 设置字段值
                targetField.set(targetObject, translation);
            } catch (IllegalAccessException e) {
                log.error("翻译赋值失败: 类={}, 字段={}, key={}", targetObject.getClass().getName(), targetField.getName(), resourceKey, e);
            }
        }
    }

}
