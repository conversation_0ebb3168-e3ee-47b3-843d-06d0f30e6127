package com.ixtech.management.integration.internal.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 车辆订单新增请求Req
 *
 * <AUTHOR> hu
 * @date 2025/4/4 21:21
 */
@Data
//@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CarOrderAddReq {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String sourceOrdercode;

    /**
     * 订单来源 1->门店；2->网站；3->微信；4->淘宝；5->携程；6->租租车；7->惠租车；8->租租车ERC; 9->易途8；
     */
    @NotNull(message = "订单来源不能为空")
    private Integer source;

    /**
     * 供应商id
     */
    @NotNull(message = "供应商不能为空")
    private Long vendorId;

    @NotNull(message = "下单不能为空")
    private String time;

    /**
     * 客户姓名
     */
    @NotBlank(message = "客户姓名不能为空")
    private String userName;

    /**
     * 客户联系方式
     */
    @NotBlank(message = "客户联系方式不能为空")
    private String mobile;

    @NotBlank(message = "客户联系方式区号不能为空")
    private String mobileArea;

    /**
     * 客户电子邮箱
     */
    @NotBlank(message = "客户电子邮箱不能为空")
    private String email;

    /**
     * 客户国籍
     */
    @NotBlank(message = "客户国籍不能为空")
    private String customerNationality;

    /**
     * 航班号
     */
    private String flightNumber;

    /**
     * 车型id
     */
    @NotNull(message = "车型不能为空")
    private Long modelId;

    /**
     * 档位
     */
//    @NotNull(message = "档位不能为空")
    private String transmission;

    /**
     * 座位数
     */
//    @NotNull(message = "座位数不能为空")
    private Integer seat;

    /**
     * 车门数
     */
//    @NotNull(message = "车门数不能为空")
    private Integer door;

    /**
     * 燃油类型
     */
//    @NotNull(message = "燃油类型不能为空")
    private String fuelModel;

    /**
     * SIPP
     */
    @NotBlank(message = "SIPP不能为空")
    private String SIPP;

    /**
     * 取车时间
     */
    @NotNull(message = "取车时间不能为空")
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private String appointStarttime;

    /**
     * 取车门店id
     */
    @NotNull(message = "取车门店不能为空")
    private Long appointGetstoreid;

    /**
     * 还车时间
     */
    @NotNull(message = "还车时间不能为空")
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private String appointEndtime;

    /**
     * 还车门店id
     */
    @NotNull(message = "还车门店不能为空")
    private Long appointReturnstoreid;


    /**
     * 预付金额（含加价）
     */
    @NotNull(message = "预付金额（含加价）不能为空")
    private BigDecimal prepaidAmount;

    /**
     * 到付金额（含加价）
     */
    @NotNull(message = "到付金额（含加价）不能为空")
    private BigDecimal cashOnDeliveryAmount;

    /**
     * 总金额（含加价）
     */
    @NotNull(message = "总金额（含加价）不能为空")
    private BigDecimal totalprice;

    @NotNull(message = "当地货币不能为空")
    private String LocalCurrency;

    /**
     * 单程费
     */
    private BigDecimal oneWayFee;

    /**
     * 特殊取车时间费
     */
    private BigDecimal specialPickupTimeFee;

    /**
     * 特殊还车时间费
     */
    private BigDecimal specialReturnTimeFee;

    /**
     * 车辆的保险id
     */
    @NotNull(message = "车辆保险不能为空")
    private Long stockInsId;

    /**
     * 平台险购买状态 true：是 false：否
     */
    @NotNull(message = "是否购买平台险不能为空")
    private Integer buyInsurance;

    /**
     * 结算金额
     */
    @NotNull(message = "结算金额不能为空")
    private BigDecimal settlementAmount;

    /**
     * 货币代码
     */
//    @NotEmpty(message = "货币代码不能为空")
//    private String unit;

    /**
     * 支付方式
     */
    @NotNull(message = "支付方式不能为空")
    private Integer paytype;

    /**
     * 备注
     */
    private String note;

}
