package com.ixtech.global.repo.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Database Table Remarks:
 *   租车订单的来源
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table jipinzuche_car_order_source
 */
public class IxChannelPO implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_order_source.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   active
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_order_source.active
     *
     * @mbg.generated
     */
    private Boolean active;

    /**
     * Database Column Remarks:
     *   deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_order_source.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     * Database Column Remarks:
     *   create_time in UTC
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_order_source.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     * Database Column Remarks:
     *   update_time in UTC
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_order_source.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     * Database Column Remarks:
     *   名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_order_source.title
     *
     * @mbg.generated
     */
    private String title;

    /**
     * Database Column Remarks:
     *   api的 stage
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_order_source.stage
     *
     * @mbg.generated
     */
    private String stage;

    /**
     * Database Column Remarks:
     *   0->未删除；1->删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_order_source.is_delete
     *
     * @mbg.generated
     */
    private Integer isDelete;

    /**
     * credential
     */
    private String credentialKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_order_source.id
     *
     * @return the value of jipinzuche_car_order_source.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_order_source.id
     *
     * @param id the value for jipinzuche_car_order_source.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_order_source.active
     *
     * @return the value of jipinzuche_car_order_source.active
     *
     * @mbg.generated
     */
    public Boolean getActive() {
        return active;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_order_source.active
     *
     * @param active the value for jipinzuche_car_order_source.active
     *
     * @mbg.generated
     */
    public void setActive(Boolean active) {
        this.active = active;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_order_source.deleted
     *
     * @return the value of jipinzuche_car_order_source.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_order_source.deleted
     *
     * @param deleted the value for jipinzuche_car_order_source.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_order_source.create_time
     *
     * @return the value of jipinzuche_car_order_source.create_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_order_source.create_time
     *
     * @param createTime the value for jipinzuche_car_order_source.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_order_source.update_time
     *
     * @return the value of jipinzuche_car_order_source.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_order_source.update_time
     *
     * @param updateTime the value for jipinzuche_car_order_source.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_order_source.title
     *
     * @return the value of jipinzuche_car_order_source.title
     *
     * @mbg.generated
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_order_source.title
     *
     * @param title the value for jipinzuche_car_order_source.title
     *
     * @mbg.generated
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_order_source.stage
     *
     * @return the value of jipinzuche_car_order_source.stage
     *
     * @mbg.generated
     */
    public String getStage() {
        return stage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_order_source.stage
     *
     * @param stage the value for jipinzuche_car_order_source.stage
     *
     * @mbg.generated
     */
    public void setStage(String stage) {
        this.stage = stage == null ? null : stage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_order_source.is_delete
     *
     * @return the value of jipinzuche_car_order_source.is_delete
     *
     * @mbg.generated
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_order_source.is_delete
     *
     * @param isDelete the value for jipinzuche_car_order_source.is_delete
     *
     * @mbg.generated
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getCredentialKey() {
        return credentialKey;
    }

    public void setCredentialKey(String credentialKey) {
        this.credentialKey = credentialKey;
    }

}
