package com.ixtech.management.facade.api;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.OrderSourceService;
import com.ixtech.management.domain.service.VendorService;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.VendorAddResp;
import com.ixtech.management.integration.internal.resp.VendorListInfoResp;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 供应商管理
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:15
 */
@RestController
@RequestMapping("/v1/management/internal/order_source")
public class OrderSourceController {

    @Resource
    private OrderSourceService orderSourceService;

    /**
     * 供应商下拉列表
     *
     * @return
     */
    @PermitAll
    @PostMapping(value = "/dropdown_list")
    public ApiResponse<List<SelectOptionResponse<Long>>> dropdownList(@RequestBody VendorDropdownReq vendorDropdownReq) {
        List<SelectOptionResponse<Long>> result = orderSourceService.dropdownList(vendorDropdownReq);
        return ApiResponse.success(result);
    }

}
