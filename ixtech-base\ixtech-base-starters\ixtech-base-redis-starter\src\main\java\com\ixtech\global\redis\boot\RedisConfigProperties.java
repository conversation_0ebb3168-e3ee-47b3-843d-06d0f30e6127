package com.ixtech.global.redis.boot;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "config.redis")
public class RedisConfigProperties {

    /**
     * 主机
     */
    private String host;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 密码
     */
    private String password;

    /**
     * 本项目DB
     */
    private Integer index;

    /**
     * 最大空闲数
     */
    private int maxIdle = 100;

    /**
     * 连接池的最大数据库连接数
     */
    private int maxTotal = 128;

    private int minIdle = 50;

    /**
     * 最大建立连接等待时间
     */
    private int maxWaitMillis = 10000;

    /**
     * 逐出连接的最小空闲时间 默认1800000毫秒(30分钟)
     */
    private int minEvictableIdleTimeMillis = 300000;

    /**
     * 每次逐出检查时 逐出的最大数目 如果为负数就是 : 1/abs(n), 默认3
     */
    private int numTestsPerEvictionRun = 16;

    /**
     * 逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
     */
    private int timeBetweenEvictionRunsMillis = 3000;

    /**
     * 是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
     */
    private boolean testOnBorrow;

    /**
     * 在空闲时检查有效性, 默认false
     */
    private boolean testWhileIdle = true;

    /**
     * Netty 线程数
     */
    private int threads = 32;

    /**
     * Netty 事件线程数
     */
    private int nettyThreads = 64;

    /**
     * 重试次数
     */
    private int retryAttempts = 5;

    /**
     * 重试间隔，默认1500
     */
    private int retryInterval = 1500;
}
