package com.ixtech.global.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 飞书机器人工具类
 *
 * <AUTHOR> hu
 * @date 2025/7/19 17:24
 */
@Slf4j
public class FeiShuBotUtils {

    private static String algorithm = "HmacSHA256";

    public static void sendText(String webhook, String secret, String text) throws Exception {

        long timestamp = System.currentTimeMillis() / 1000;

        Map<String, Object> body = new HashMap<>();
        body.put("timestamp", timestamp);
        body.put("sign", generateSign(secret, timestamp));
        body.put("msg_type", "text");

        Map<String, String> content = new HashMap<>();
        body.put("content", content);
        content.put("text", text);

        ResponseEntity<String> response = new RestTemplate().postForEntity(webhook, body, String.class);
        System.out.println("飞书机器人消息发送结果：" + response);

    }

    /**
     * 生成签名
     *
     * @param secret
     * @param timestamp
     * @return
     * @throws Exception
     */
    private static String generateSign(String secret, long timestamp) throws Exception {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;
        //使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance(algorithm);
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), algorithm));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }

}
