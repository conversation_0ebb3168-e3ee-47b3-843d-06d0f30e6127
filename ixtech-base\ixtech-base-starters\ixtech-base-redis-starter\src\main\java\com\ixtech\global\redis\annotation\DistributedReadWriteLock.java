package com.ixtech.global.redis.annotation;

import com.ixtech.global.redis.enums.LockType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 分布式读写锁注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedReadWriteLock {

    /**
     * 锁的 Key (动态部分)。
     * <p>
     * 支持 Spring Expression Language (SpEL) 表达式。
     * 例如："'store-price:' + #storeId"
     * </p>
     * @return 锁的 key 的动态部分
     */
    String key();

    /**
     * 锁的类型，读锁或写锁。
     * 默认为读锁。
     * @return 锁类型
     */
    LockType type() default LockType.READ;

    /**
     * 尝试获取锁的最大等待时间。
     * 默认-1，表示不等待，立即尝试获取。
     * @return 等待时间
     */
    long waitTime() default -1L;

    /**
     * 锁的租约时间（自动释放时间）。
     * 仅对写锁有效。
     * @return 租约时间
     */
    long leaseTime() default 30L;

    /**
     * 时间单位。
     * @return 时间单位
     */
    TimeUnit unit() default TimeUnit.SECONDS;
}
