package com.ixtech.global.domain.service;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.integration.internal.req.OrderQueryReq;
import com.ixtech.global.integration.internal.req.RentalRequestReq;
import com.ixtech.global.integration.internal.resp.OrderCancelResp;
import com.ixtech.global.integration.internal.resp.RentalResp;
import com.ixtech.global.repo.entity.IxChannelPO;

import java.util.List;

/**
 * 订单 Service
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface CarOrderService {
    /**
     * 渠道 订单预订
     *
     * @param rentalRequestReq 订单请求
     * @return 订单详情
     */
    ApiResponse<RentalResp> vehRes(RentalRequestReq rentalRequestReq);

    /**
     * 渠道 订单取消
     *
     * @param req 订单查询req
     * @return 订单取消resp
     */
    ApiResponse<OrderCancelResp> vehCancel(OrderQueryReq req);

    /**
     * 渠道 订单查询
     *
     * @param req 订单查询req
     * @return 订单查询resp
     */
    ApiResponse<RentalResp> vehResStatusSearch(OrderQueryReq req);

    /**
     * 获取渠道集合
     *
     * @return key为credential
     */
    List<IxChannelPO> getSourceList();

}
