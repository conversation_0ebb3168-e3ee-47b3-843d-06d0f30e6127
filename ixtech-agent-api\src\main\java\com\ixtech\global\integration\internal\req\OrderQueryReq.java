package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单查询 请求对象
 * 用于接收订单相关的请求数据
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderQueryReq {

    /**
     * 来源平台
     * 表示订单的来源，必填字段
     */
    // @NotNull(message = "来源平台不能为空")
    private Integer source;

    /**
     * 来源平台订单号
     * 来源平台的唯一订单标识，必填字段
     */
    @NotBlank(message = "合作伙伴订单编号 不能为空")
    @JsonAlias("partner_order_no")
    private String sourceOrderId;

    /**
     * IX平台订单号
     * IX平台的订单标识，非必填字段
     */
    private String ixOrderCode;

    /**
     * IX平台确认号
     * IX平台的确认标识，非必填字段
     */
    private String ixConfirmCode;
}
