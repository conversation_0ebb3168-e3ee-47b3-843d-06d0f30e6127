package com.ixtech.management.domain.service.impl;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ixtech.management.common.constants.Constants;
import com.ixtech.management.common.constants.NormalConstant;
import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.exception.BizException;
import com.ixtech.management.common.utils.DateUtils;
import com.ixtech.management.common.utils.ExcelUtils;
import com.ixtech.management.config.ExcelHeaderCheckException;
import com.ixtech.management.domain.enums.InsuranceEnum;
import com.ixtech.management.domain.enums.OrderCreateTypeEnum;
import com.ixtech.management.domain.enums.*;
import com.ixtech.management.domain.service.CarOrderService;
import com.ixtech.management.integration.internal.client.OrdersrvFeignClient;
import com.ixtech.management.integration.internal.client.ProductsrvFeignClient;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.*;
import com.ixtech.management.common.validator.ExcelValidationProcessor;
import com.ixtech.management.repo.entity.*;
import com.ixtech.management.repo.repository.VendorRepository;
import com.ixtech.management.repo.repository.*;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.ixtech.management.common.enums.ErrorCodeConstants.INVALID_CAR_STOCK_ID;
import static com.ixtech.management.common.enums.ErrorCodeConstants.INVALID_ORDER_STATUS;

import static com.ixtech.management.common.utils.DateUtils.*;
import static com.ixtech.management.common.validator.CTripExcelHeaderValidator.carBrands;

/**
 * 车辆订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CarOrderServiceImpl implements CarOrderService {


    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private VendorRepository vendorRepository;

    @Resource
    private IxCarOrderPriceRepository orderPriceRepository;

    @Resource
    private OrdersrvFeignClient ordersrvFeignClient;

    @Resource
    private ProductsrvFeignClient productsrvFeignClient;

    @Resource
    private CarOrderSourceRepository jipinzucheCarOrderSourceRepository;

    @Resource
    private JipinzucheCarOrderRepository jipinzucheCarOrderRepository;

    @Resource
    private IxCarOrderCustomerRepository carOrderCustomerRepository;

    @Resource
    private JipinzucheCarListRepository jipinzucheCarListRepository;

    @Resource
    private JipinzucheCarStockRepository carStockRepository;

    @Resource
    private JipinzucheCarModelRepository carModelRepository;

    @Resource
    private JipinzucheCarListRepository carListRepository;

    @Resource
    private JipinzucheCarOrderPaytypeRepository carOrderPaytypeRepository;

    @Resource
    private ExcelValidationProcessor excelValidationProcessor;
    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(PATTERN);

    private static final String CURRENCY_PREFIX = "USD";

    public static Map<Long, Set<String>> fileOrderSourceId = Maps.newHashMap();

    public static Map<Long, Map<String, List<CarOrderImportRespVO.CarOrderCheckOrImportResult>>>
            carOrderImportRespVOMap = Maps.newHashMap();

    private static Map<Integer, List<Integer>> orderStatusMap = Map.of(
            2, List.of(1, 2),
            3, List.of(3),
            4, List.of(4),
            5, List.of(5),
            6, List.of(-1, -3),
            7, List.of(3),
            8, List.of(6)
    );

    private static final Map<Integer, String> manageOrderStatusMap = Map.of(
            2, "订单创建",
            3, "订单确认",
            4, "已取车",
            5, "已还车",
            6, "订单取消",
            7, "逾期未取",
            8, "完成"
    );

    @Autowired
    private JipinzucheCarStockInsuranceRepository jipinzucheCarStockInsuranceRepository;
    @Autowired
    private JipinzucheInsuranceRepository jipinzucheInsuranceRepository;
    @Autowired
    private JipinzucheCarOrderPaytypeRepository jipinzucheCarOrderPaytypeRepository;
    @Autowired
    private IxCarOrderCustomerRepository ixCarOrderCustomerRepository;
    @Resource
    private IxCarOrderPriceRepository ixCarOrderPriceRepository;


    /**
     * 根据页面对应订单业务状态，获取对应数据库订单状态
     * @param targetValue 页面订单状态
     * @return 数据库订单状态
     */
    public static List<Integer> findKeysByValue(int targetValue) {
        return orderStatusMap.entrySet().stream()
                .filter(entry -> entry.getValue().contains(targetValue))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 渠道端导出excel文件内容格式校验
     *
     * @param distribution  渠道
     * @param file          excel文件
     * @param fileVersionId 文件版本号
     * @return 校验结果
     */
    @Override
    public PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> fileCheck(String distribution
            , MultipartFile file, Long fileVersionId, PageParam pageParam) {
        if (null == distribution || file.isEmpty() || null == fileVersionId) {
            throw new BizException("参数校验异常");
        }
        // 文件表头校验和行数校验
        Map<String, List<CarOrderImportRespVO.CarOrderCheckOrImportResult>> result = fileHeaderCheck(fileVersionId, distribution, file);
        carOrderImportRespVOMap.put(fileVersionId, result);
        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> lineFailList = result.get("checkAllData");
        return getPageResult(lineFailList, pageParam);
    }

    /**
     * 页面结果集分页查询
     *
     * @param pageReqVO 分页查询
     * @return 分页数据
     */
    @Override
    public PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> getImportCarOrder(
            CarOrderImportReqVO pageReqVO) throws ExcelHeaderCheckException {
        if (!carOrderImportRespVOMap.containsKey(pageReqVO.getFileVersionId()) ||
                !(carOrderImportRespVOMap.containsKey(pageReqVO.getFileVersionId()))) {
            throw new ExcelHeaderCheckException("查询文件结果不存在");
        }

        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> voList = new ArrayList<>();
        String resultType = "";
        // 没有筛选项，查询All
        if (pageReqVO.getImportStatus() == null && pageReqVO.getCheckStatus() == null) {
            if (!carOrderImportRespVOMap.get(pageReqVO.getFileVersionId())
                    .containsKey(pageReqVO.getResultType())) {
                throw new ExcelHeaderCheckException("查询文件结果集不存在");
            }
            resultType = pageReqVO.getResultType();

        } else if (pageReqVO.getImportStatus() != null && !pageReqVO.getImportStatus().isEmpty()) {
            if (Objects.equals(pageReqVO.getImportStatus(), "0")) {
                resultType = "importErrorData";
            }
            if (Objects.equals(pageReqVO.getImportStatus(), "1")) {
                resultType = "importSuccessData";
            }
            if (!carOrderImportRespVOMap.get(pageReqVO.getFileVersionId())
                    .containsKey(resultType)) {
                throw new ExcelHeaderCheckException("查询文件结果集不存在");
            }

        } else if (pageReqVO.getImportStatus() == null && !pageReqVO.getCheckStatus().isEmpty()) {
            if (Objects.equals(pageReqVO.getCheckStatus(), "0")) {
                resultType = "checkErrorData";
            }
            if (Objects.equals(pageReqVO.getCheckStatus(), "1")) {
                resultType = "checkSuccessData";
            }
            if (!carOrderImportRespVOMap.get(pageReqVO.getFileVersionId())
                    .containsKey(resultType)) {
                throw new ExcelHeaderCheckException("查询文件结果集不存在");
            }
        }
        if (resultType.isEmpty()) {
            throw new ExcelHeaderCheckException("查询文件结果集不存在");
        }
        voList = carOrderImportRespVOMap.get(pageReqVO.getFileVersionId())
                .get(resultType);
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(pageReqVO.getPageNo());
        pageParam.setPageSize(pageReqVO.getPageSize());
        return getPageResult(voList, pageParam);
    }


    /**
     * excel文件订单数据导入
     *
     * @param distribution  渠道
     * @param file          文件
     * @param fileVersionId 文件版本号
     * @return 导入结果集
     */
    @Override
    public PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> orderExcelImport(
            Integer distribution, MultipartFile file, Long fileVersionId, PageParam pageParam) throws ExcelHeaderCheckException {
        // 确认文件是否存在校验错误
        if (!carOrderImportRespVOMap.get(fileVersionId).get("checkErrorData").isEmpty()) {
            throw new ExcelHeaderCheckException("文件未通过校验，请完成校验后再导入");
        }

        // 文件解析并导入
        excelReadAndOrderImport(distribution, file, fileVersionId);

        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> voList =
                carOrderImportRespVOMap.get(fileVersionId)
                        .get("importAllData");
        return getPageResult(voList, pageParam);
    }

    /**
     * 获取租赁时间跨度天数
     */
    private int getDays(LocalDateTime pickupTime, LocalDateTime returnTime) {
        int minutes = DateUtils.calculateMinuteDurationBetween(pickupTime, returnTime);
        int days;
        // 不满24h算一天
        if (minutes <= NormalConstant.ONE_DAY_MINUTES) {
            days = NormalConstant.NUM_ONE;
        } else {
            // 超过24小时，先求整天数，再求余数；后根据余数判断是否超过2小时，超过2小时算1天，反之不算
            int fullDay = minutes / NormalConstant.ONE_DAY_MINUTES;
            int remainder = minutes % NormalConstant.ONE_DAY_MINUTES;
            days = remainder > NormalConstant.TWO_HOURS_MINUTES ? (fullDay + NormalConstant.NUM_ONE) : fullDay;
        }
        return days;
    }

    @Override
    public UploadResultResp getImportResult(CarOrderImportReqVO pageReqVO) {
        Map<String, List<CarOrderImportRespVO.CarOrderCheckOrImportResult>> stringListMap = carOrderImportRespVOMap.get(pageReqVO.getFileVersionId());
        if (null == stringListMap || stringListMap.isEmpty()) {
            return null;
        }
        UploadResultResp uploadResultResp = new UploadResultResp();
        if (stringListMap.containsKey("importAllData")) {
            uploadResultResp.setSuccessNum(stringListMap.get("importSuccessData").size());
            uploadResultResp.setFailNum(stringListMap.get("importErrorData").size());
            uploadResultResp.setTotalNum(stringListMap.get("importAllData").size());
        } else if (stringListMap.containsKey("checkAllData")) {
            uploadResultResp.setSuccessNum(stringListMap.get("checkSuccessData").size());
            uploadResultResp.setFailNum(stringListMap.get("checkErrorData").size());
            uploadResultResp.setTotalNum(stringListMap.get("checkAllData").size());
        }
        return uploadResultResp;
    }

    @Override
    public PageResult<CarOrderListQueryResp> vehOrderList(CarOrderListQueryReq req) {
        req.setLimitStart(((long) (req.getPageNo() - 1) * req.getPageSize()));
        req.setLimitSize(Long.valueOf(req.getPageSize()));
        // 设置订单状态
        if (null != req.getManageOrderStatus() && req.getManageOrderStatus() != 0) {
            if (orderStatusMap.containsKey(req.getManageOrderStatus())) {
                req.setOrderStatus(orderStatusMap.get(req.getManageOrderStatus()));
            } else {
                throw exception(INVALID_ORDER_STATUS, "订单状态非法，请检查后重新提交");
            }
        }
        ApiResponse<ManagementCarOrderListPageResp> result = ordersrvFeignClient.vehOrderList(req);
        // 获取所有 订单来源信息
        List<CarOrderSource> sources = jipinzucheCarOrderSourceRepository.queryAllCarOrderSources();
        Map<Long, String> sourceMap = sources.stream()
                .collect(Collectors.toMap(
                        CarOrderSource::getId,  // Key 映射
                        CarOrderSource::getTitle // Value 映射
                ));
        PageResult<CarOrderListQueryResp> pageResult = new PageResult<>();
        List<CarOrderListQueryResp> list = new ArrayList<>();
        if (null != result && result.isSuccess() && null != result.getResult() && null != result.getResult().getList()) {
            result.getResult().getList().forEach(jipinzucheCarOrderResp -> {
                CarOrderListQueryResp carOrderListQueryResp = new CarOrderListQueryResp();
                carOrderListQueryResp.setId(jipinzucheCarOrderResp.getId());
                carOrderListQueryResp.setSourceOrderCode(jipinzucheCarOrderResp.getSourceOrdercode() != null
                        ? jipinzucheCarOrderResp.getSourceOrdercode() : "");
                carOrderListQueryResp.setCreateTypeStr(
                        jipinzucheCarOrderResp.getCreateType() != null
                                ? Optional.ofNullable(OrderCreateTypeEnum.getByName(jipinzucheCarOrderResp.getCreateType()))
                                .map(OrderCreateTypeEnum::getCode)
                                .orElse("")
                                : ""
                );
                // 订单来源
                carOrderListQueryResp.setSource(jipinzucheCarOrderResp.getSource() != null
                        ? sourceMap.get((long)jipinzucheCarOrderResp.getSource())
                        : null);

                // 门店名称
                Store store = storeRepository.selectById(jipinzucheCarOrderResp.getAppointGetstoreid());
                if (null != store && store.getId() != null) {
                    carOrderListQueryResp.setStoreName(store.getName());
                    Vendor vendor = vendorRepository.selectById(store.getVendorId());
                    if (null != vendor && vendor.getId() != null) {
                        // 供应商名称
                        carOrderListQueryResp.setVendorName(vendor.getName());
                    }
                }
                // 预计取车时间
                carOrderListQueryResp.setAppointStartTimeStr(jipinzucheCarOrderResp.getAppointStarttime());
                // 预计还车时间
                carOrderListQueryResp.setAppointEndTimeStr(jipinzucheCarOrderResp.getAppointEndtime());
                // 订单创建时间（实际的下订单时间）
                carOrderListQueryResp.setTimeStr(jipinzucheCarOrderResp.getTime());
                // 车型名称
                carOrderListQueryResp.setModelsName(jipinzucheCarOrderResp.getAppointCarname());
                // 总金额
                carOrderListQueryResp.setTotalAmountStr((jipinzucheCarOrderResp.getSettlementAmount() != null ? jipinzucheCarOrderResp.getSettlementAmount() : "0")
                        + jipinzucheCarOrderResp.getSettlementCurrency());
                // 订单状态
                List<Integer> keysByValue = findKeysByValue(jipinzucheCarOrderResp.getStatus());  // 数据库订单状态
                if(!keysByValue.isEmpty()) {
                    if (keysByValue.contains(3) && keysByValue.contains(7)) {
                        carOrderListQueryResp.setStatusStr(manageOrderStatusMap.get(3));
                        // 逾期赋值
                        LocalDateTime startTime = DateUtils.parseStringToLocalDateTime(jipinzucheCarOrderResp.getAppointStarttime());
                        if (null != startTime && startTime.isBefore(LocalDateTime.now())) {
                            carOrderListQueryResp.setStatusStr(manageOrderStatusMap.get(7));
                        }
                    }
                    if (null == carOrderListQueryResp.getStatusStr()) {
                        carOrderListQueryResp.setStatusStr(manageOrderStatusMap.get(keysByValue.getFirst()));
                    }
                }
                list.add(carOrderListQueryResp);
            });

            pageResult.setTotal(result.getResult().getTotal());
            pageResult.setList(list);
        }

        return pageResult;
    }

    @Override
    public List<CarOrderListExportResp> vehOrderListExport(CarOrderListQueryReq req) {
        req.setLimitStart(-1L);
        req.setLimitSize(-1L);
        // 设置订单状态
        if (null != req.getManageOrderStatus() && req.getManageOrderStatus() != 0) {
            if (orderStatusMap.containsKey(req.getManageOrderStatus())) {
                req.setOrderStatus(orderStatusMap.get(req.getManageOrderStatus()));
            } else {
                throw exception(INVALID_ORDER_STATUS, "订单状态非法，请检查后重新提交");
            }
        }
        ApiResponse<ManagementCarOrderListPageResp> result = ordersrvFeignClient.vehOrderList(req);
        // 获取所有 订单来源信息
        List<CarOrderSource> sources = jipinzucheCarOrderSourceRepository.queryAllCarOrderSources();
        Map<Long, String> sourceMap = sources.stream()
                .filter(Objects::nonNull)  // 过滤掉整个对象为 null 的情况
                .filter(source -> source.getId() != null)  // 确保 id 不为 null
                .filter(source -> source.getTitle() != null)  // 确保 title 不为 null
                .collect(Collectors.toMap(
                        CarOrderSource::getId,
                        CarOrderSource::getTitle
                ));
        Map<Long, String> sourceSettlementCurrencyMap = sources.stream()
                .collect(Collectors.toMap(
                        source -> source.getId() != null ? source.getId() : 0L,  // 默认 0
                        source -> source.getSettlementCurrency() != null ? source.getSettlementCurrency() : ""  // 默认 ""
                ));
        List<Vendor> vendors = vendorRepository.selectAll();
        Map<Long, String> vendorMap = vendors.stream()
                .filter(Objects::nonNull)  // 过滤掉整个对象为 null 的情况
                .filter(vendor -> vendor.getId() != null)  // 确保 id 不为 null
                .filter(vendor -> vendor.getName() != null)
                .collect(Collectors.toMap(
                        Vendor::getId,
                        Vendor::getName
                ));
        List<CarOrderListExportResp> list = new ArrayList<>();
        if (null != result && result.isSuccess() && null != result.getResult() && null != result.getResult().getList()) {
            result.getResult().getList().forEach(j -> {
                setListQueryResp(j, sourceSettlementCurrencyMap, vendorMap, sourceMap, list);
            });
        }

        return list;
    }

    private void setListQueryResp(ManagementCarOrderResp j, Map<Long, String> sourceSettlementCurrencyMap, Map<Long, String> vendorMap, Map<Long, String> sourceMap, List<CarOrderListExportResp> list) {
        CarOrderListExportResp carOrderListQueryResp = new CarOrderListExportResp();
        JipinzucheCarOrderPO jj = jipinzucheCarOrderRepository.selectById(j.getId());
        if (null != jj) {
            BeanUtils.copyProperties(jj, carOrderListQueryResp);

            IxCarOrderPrice ixCarOrderPrice = ixCarOrderPriceRepository.selectByCarOrderId(j.getId());
            if (null != ixCarOrderPrice) {
                carOrderListQueryResp.setTotalprice(String.format("%.2f", jj.getTotalprice() != null ? jj.getTotalprice() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setOneWayFee(String.format("%.2f", ixCarOrderPrice.getOneWayFee() != null ? ixCarOrderPrice.getOneWayFee() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setInsuranceprice(String.format("%.2f", jj.getInsuranceprice() != null ? jj.getInsuranceprice() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setCancelprice(String.format("%.2f", jj.getCancelprice() != null ? jj.getCancelprice() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setExtraprice(String.format("%.2f", jj.getExtraprice() != null ? jj.getExtraprice() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setOtherprice(String.format("%.2f", jj.getOtherprice() != null ? jj.getOtherprice() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setOvertimecost(String.format("%.2f", jj.getOvertimecost() != null ? jj.getOvertimecost() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setCarreturnPrice(String.format("%.2f", jj.getCarreturnPrice() != null ? jj.getCarreturnPrice() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setPrepaidAmount(String.format("%.2f", ixCarOrderPrice.getPrepaidAmount() != null ? ixCarOrderPrice.getPrepaidAmount() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setCashOnDeliveryAmount(String.format("%.2f", ixCarOrderPrice.getCashOnDeliveryAmount() != null ? ixCarOrderPrice.getCashOnDeliveryAmount() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setSpecialPickupTimeFee(String.format("%.2f", ixCarOrderPrice.getSpecialPickupTimeFee() != null ? ixCarOrderPrice.getSpecialPickupTimeFee() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setSpecialReturnTimeFee(String.format("%.2f", ixCarOrderPrice.getSpecialReturnTimeFee() != null ? ixCarOrderPrice.getSpecialReturnTimeFee() : new BigDecimal(0)) + " " + ixCarOrderPrice.getLocalCurrency());
                carOrderListQueryResp.setSettlementAmount(String.format("%.2f", ixCarOrderPrice.getSettlementAmount() != null ? ixCarOrderPrice.getSettlementAmount() : new BigDecimal(0)) + " " + (sourceSettlementCurrencyMap.get(Long.valueOf(jj.getSource())) != null ? sourceSettlementCurrencyMap.get(Long.valueOf(jj.getSource())) : ""));
                carOrderListQueryResp.setLocalCurrency(ixCarOrderPrice.getLocalCurrency());
            }
            carOrderListQueryResp.setVendorName(vendorMap.get(jj.getVendorId()));
            if (null != jj.getAppointStarttime()) {
                carOrderListQueryResp.setAppointStarttime(timestampToString(jj.getAppointStarttime()));
            }
            if (null != jj.getAppointEndtime()) {
                carOrderListQueryResp.setAppointEndtime(timestampToString(jj.getAppointEndtime()));
            }
            if (null != jj.getTime()) {
                carOrderListQueryResp.setTime(timestampToString(jj.getTime()));
            }
            carOrderListQueryResp.setSource(sourceMap.get(Long.valueOf(jj.getSource())));
            if (null != jj.getSex()) {
                carOrderListQueryResp.setSex(jj.getSex() == 1 ? "男" : "女");
            }
            if (null != jj.getPaytime()) {
                carOrderListQueryResp.setPaytime(timestampToString(jj.getPaytime()));
            }
            JipinzucheCarOrderPaytype jipinzucheCarOrderPaytype =
                    jipinzucheCarOrderPaytypeRepository.selectById(Long.valueOf(jj.getPaytype()));
            if (null != jipinzucheCarOrderPaytype && null != jipinzucheCarOrderPaytype.getTitle()) {
                carOrderListQueryResp.setPaytype(jipinzucheCarOrderPaytype.getTitle());
            }
            if (null != jj.getCanceltime()) {
                carOrderListQueryResp.setCanceltime(timestampToString(jj.getCanceltime()));
            }
            if (null != jj.getOvertimePrepay()) {
                carOrderListQueryResp.setOvertimePrepay(jj.getOvertimePrepay() == 1 ? "是" : "否");
            }
            carOrderListQueryResp.setStatus(OrderStatusEnum.getByCode(jj.getStatus()).getTitle());
            carOrderListQueryResp.setOvertimePrepay(
                    jj.getOvertimePrepay() != null && jj.getOvertimePrepay() == 1 ? "是" : "否");
            carOrderListQueryResp.setCreateType(OrderCreateTypeEnum.getByName(jj.getCreateType()).getCode());
            list.add(carOrderListQueryResp);
        }
    }

    @Override
    public Long vehOrderCheckUnique(String sourceOrdercode) {
        return jipinzucheCarOrderRepository.selectCountBySourceOrderCode(sourceOrdercode);
    }

    @Override
    public JipinzucheCarOrderPO selectBySourceAndSourceCode(OrderQueryReq orderQueryReq) {
        return jipinzucheCarOrderRepository.selectBySourceAndSourceOrderCode(orderQueryReq);
    }

    @Override
    public PageResult<CarOrderSource> vehAllOrderSourceList() {
        List<CarOrderSource> sources = jipinzucheCarOrderSourceRepository.queryAllCarOrderSources();
        PageResult<CarOrderSource> pageResult = new PageResult<>();
        pageResult.setList(sources);
        pageResult.setTotal((long) sources.size());
        return pageResult;
    }

    @Override
    public CarOrderInfoResp vehOrderInfo(Long id) {
        ApiResponse<CarOrderInfoResp> carOrderInfoRespApiResponse = ordersrvFeignClient.vehOrderInfo(id);
        if (carOrderInfoRespApiResponse.isSuccess() && carOrderInfoRespApiResponse.getResult() != null) {
            CarOrderInfoResp result = carOrderInfoRespApiResponse.getResult();
            List<Integer> keysByValue = findKeysByValue(result.getStatus());
            if(!keysByValue.isEmpty()) {
                if (keysByValue.contains(3) && keysByValue.contains(7)) {
                    result.setStatusStr(manageOrderStatusMap.get(3));
                    // 逾期赋值
                    LocalDateTime startTime = DateUtils.parseStringToLocalDateTime(result.getAppointStarttime());
                    if (null != startTime && startTime.isBefore(LocalDateTime.now())) {
                        result.setStatusStr(manageOrderStatusMap.get(7));
                    }
                }
                if (null == result.getStatusStr()) {
                    result.setStatusStr(manageOrderStatusMap.get(keysByValue.getFirst()));
                }
            }
            result.setCreateTypeStr(
                    result.getCreateType() != null
                            ? Optional.ofNullable(OrderCreateTypeEnum.getByName(result.getCreateType()))
                            .map(OrderCreateTypeEnum::getCode)
                            .orElse("")
                            : ""
            );
            // 获取所有 订单来源信息
            List<CarOrderSource> sources = jipinzucheCarOrderSourceRepository.queryAllCarOrderSources();
            Map<Long, String> sourceMap = sources.stream()
                    .collect(Collectors.toMap(
                            CarOrderSource::getId,  // Key 映射
                            CarOrderSource::getTitle // Value 映射
                    ));
            // 订单来源
            result.setSourceStr(result.getSource() != null
                    ? sourceMap.get((long)result.getSource())
                    : null);

            // 门店名称
            Store getStore = storeRepository.selectById(result.getAppointGetstoreid());
            if (null != getStore && getStore.getId() != null) {
                result.setAppointGetstorename(getStore.getName());
                Vendor vendor = vendorRepository.selectById(getStore.getVendorId());
                if (null != vendor && vendor.getId() != null) {
                    // 供应商名称
                    result.setVendorName(vendor.getName());
                }
            }
            Store returnStore = storeRepository.selectById(result.getAppointReturnstoreid());
            if (null != returnStore && returnStore.getId() != null) {
                result.setAppointReturnstorename(returnStore.getName());
            }
            // 预定车型
            setCarModelInfo(result);

            // 支付方式
            JipinzucheCarOrderPaytype jipinzucheCarOrderPaytype =
                    carOrderPaytypeRepository.selectById(Long.valueOf(result.getPaytype()));
            if (null != jipinzucheCarOrderPaytype) {
                result.setPaymentMethod(jipinzucheCarOrderPaytype.getTitle());
            }

            return result;
        }
        return null;
    }

    /**
     * 设置车型信息
     * @param result 结果对象
     */
    private void setCarModelInfo(CarOrderInfoResp result) {
        if (null != result.getAppointStockid()) {
            JipinzucheCarStock jipinzucheCarStock = carStockRepository.selectById(result.getAppointStockid());
            JipinzucheCarList jipinzucheCarList = carListRepository.selectById(result.getAppointCarid());
            if (null != jipinzucheCarStock) {
                JipinzucheCarModel jipinzucheCarModel =
                        carModelRepository.selectById(Long.valueOf(jipinzucheCarStock.getModelid()));
                if (null != jipinzucheCarModel) {
                    String transmissionStr = jipinzucheCarModel.getTransmission() == 1 ? "自动档": "手动档";
                    result.setOrderedCarModel(jipinzucheCarModel.getName()+" "+transmissionStr+" "
                            +jipinzucheCarModel.getSeat()+"座 " + jipinzucheCarModel.getDoor() + "门 ");
                    if (null != jipinzucheCarList) {
                        result.setOrderedCarModel(result.getOrderedCarModel()
                                + (jipinzucheCarList.getFuelmodel() != null
                                ? jipinzucheCarList.getFuelmodel() : "") + "(燃油)");
                    }
                }
            }
        }
    }

    @Override
    @DSTransactional
    public Integer vehOrderAdd(CarOrderAddReq req) {
        JipinzucheCarOrderPO jipinzucheCarOrder = new JipinzucheCarOrderPO();
        BeanUtils.copyProperties(req, jipinzucheCarOrder);
        List<JipinzucheCarStock> jipinzucheCarStock =
                carStockRepository.selectByModelIdAndStoreId(req.getModelId(), req.getAppointGetstoreid());
        if (null == jipinzucheCarStock || jipinzucheCarStock.isEmpty()) {
            throw exception0(INVALID_CAR_STOCK_ID.getCode(), "未查询到门店车型信息");
        }
        jipinzucheCarOrder.setAppointStockid(jipinzucheCarStock.getFirst().getId());
        jipinzucheCarOrder.setStockid(jipinzucheCarStock.getFirst().getId());
        jipinzucheCarOrder.setOrdercode(genCode());
        jipinzucheCarOrder.setAppointGetstoreid(Math.toIntExact(req.getAppointGetstoreid()));
        jipinzucheCarOrder.setAppointReturnstoreid(Math.toIntExact(req.getAppointReturnstoreid()));
        Store getStore = storeRepository.selectById(req.getAppointGetstoreid());
        Store returnStore = storeRepository.selectById(req.getAppointReturnstoreid());
        jipinzucheCarOrder.setAppointGetstorename(getStore.getName());
        jipinzucheCarOrder.setAppointReturnstorename(returnStore.getName());
        JipinzucheCarModel jipinzucheCarModel = carModelRepository.selectById(req.getModelId());
        jipinzucheCarOrder.setAppointCarname(jipinzucheCarModel.getName());
        jipinzucheCarOrder.setCarname(jipinzucheCarModel.getName());
        jipinzucheCarOrder.setCarcode(req.getSIPP());
        jipinzucheCarOrder.setAppointCarcode(req.getSIPP());
        long l1 = parseToTimestamp(req.getAppointStarttime());
        jipinzucheCarOrder.setAppointStarttime(l1);
        long l2 = parseToTimestamp(req.getAppointEndtime());
        jipinzucheCarOrder.setAppointEndtime(l2);
        int days = getDays(timestampToLocalDateTime(l1)
                , timestampToLocalDateTime(l2));
        jipinzucheCarOrder.setAppointDays(days);

        String s = utcToLocalTimeString(Long.parseLong(req.getTime()), getStore.getLat(), getStore.getLon());
        if (StringUtils.isNotEmpty(s)) {
            jipinzucheCarOrder.setTime(parseToTimestamp(s));
        }
        JipinzucheCarStockInsurance jipinzucheCarStockInsurance =
                jipinzucheCarStockInsuranceRepository.selectById(req.getStockInsId());
        if (null != jipinzucheCarStockInsurance) {
            jipinzucheCarOrder.setInsurance(jipinzucheCarStockInsurance.getInsid());
            jipinzucheCarOrder.setInsuranceprice(jipinzucheCarStockInsurance.getPrice());
        }
        jipinzucheCarOrder.setStockInsId(Math.toIntExact(req.getStockInsId()));
        JipinzucheInsurance jipinzucheInsurance =
                jipinzucheInsuranceRepository.selectById(Long.valueOf(jipinzucheCarStockInsurance.getInsid()));
        jipinzucheCarOrder.setStockInsTitle(jipinzucheInsurance.getTitle());
        jipinzucheCarOrder.setUsername(req.getUserName());
        jipinzucheCarOrder.setContactline(req.getFlightNumber());
        jipinzucheCarOrder.setStatus(OrderStatusEnum.UNCONFIRMED.getCode());
        jipinzucheCarOrder.setVendorId(req.getVendorId());
        jipinzucheCarOrder.setLocalPayprice(req.getTotalprice());
        jipinzucheCarOrder.setTotalprice(req.getTotalprice());
        // 结算货币
        Vendor vendor = vendorRepository.selectById(req.getVendorId());
        jipinzucheCarOrder.setUnit(vendor.getSettlementCurrency());

        int insert = jipinzucheCarOrderRepository.insert(jipinzucheCarOrder);
        if (insert == 0) {
            throw new BizException("订单入库失败");
        }
        IxCarOrderPrice ixCarOrderPrice = new IxCarOrderPrice();
        ixCarOrderPrice.setCarOrderId(Long.valueOf(jipinzucheCarOrder.getId()));
        ixCarOrderPrice.setPrepaidAmount(req.getPrepaidAmount());
        ixCarOrderPrice.setCashOnDeliveryAmount(req.getCashOnDeliveryAmount());
        ixCarOrderPrice.setOneWayFee(req.getOneWayFee());
        ixCarOrderPrice.setSpecialPickupTimeFee(req.getSpecialPickupTimeFee());
        ixCarOrderPrice.setSpecialReturnTimeFee(req.getSpecialReturnTimeFee());
        ixCarOrderPrice.setBuyInsurance(req.getBuyInsurance());
        ixCarOrderPrice.setSettlementAmount(req.getSettlementAmount());
        ixCarOrderPrice.setLocalCurrency(req.getLocalCurrency());
        Long l = ixCarOrderPriceRepository.insertOne(ixCarOrderPrice);
        if (l == 0) {
            throw new BizException("订单金额信息入库失败");
        }

        IxCarOrderCustomer ixCarOrderCustomer = new IxCarOrderCustomer();
        ixCarOrderCustomer.setCitizenCountryCode(req.getCustomerNationality());
        ixCarOrderCustomer.setDriverAge(28);
        ixCarOrderCustomer.setEmailAddress(req.getEmail());
        ixCarOrderCustomer.setSurname(req.getUserName());
        ixCarOrderCustomer.setGivenName(req.getUserName());
        ixCarOrderCustomer.setMobileAreaCityCode(req.getMobileArea());
        ixCarOrderCustomer.setMobilePhoneNumber(req.getMobile());
        ixCarOrderCustomer.setCarOrderId(Long.valueOf(jipinzucheCarOrder.getId()));
        return carOrderCustomerRepository.insert(ixCarOrderCustomer);
    }

    /**
     * 生成 订单code
     *
     * @return code
     */
    public static String genCode() {
        return Constants.ODR + RandomStringUtils.randomAlphanumeric(8)+ System.currentTimeMillis();
    }

    /**
     * 解析excel文件订单数据，并对数据进行导入
     *
     * @param distribution 渠道
     * @param file         文件
     */
    private void excelReadAndOrderImport(Integer distribution
            , MultipartFile file, Long fileVersionId) throws ExcelHeaderCheckException {
        // 逐行数据解析
        Workbook workbook;
        Sheet sheet;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            sheet = workbook.getSheetAt(0);
        } catch (IOException e) {
            throw new ExcelHeaderCheckException("excel文件解析失败");
        }
        CarOrderSource source = jipinzucheCarOrderSourceRepository.selectById(Long.valueOf(distribution));
        if (null == source) {
            throw new ExcelHeaderCheckException("渠道信息获取失败");
        }

        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> errorList = Lists.newArrayList();
        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> successList = Lists.newArrayList();
        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> allList = Lists.newArrayList();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            // 行数据订单实体生成
            Row row = sheet.getRow(i);
            CarOrderImportRespVO.CarOrderCheckOrImportResult result
                    = new CarOrderImportRespVO.CarOrderCheckOrImportResult();
            generateOrder(source, row, result);
            result.setIsImportSucceed(result.getImportFailReason().isEmpty());
            result.setId(i+1);
            result.setIsCheckSucceed(true);
            if (result.getIsImportSucceed()) {
                successList.add(result);
            }   else {
                errorList.add(result);
            }
            allList.add(result);
        }
        Map<String, List<CarOrderImportRespVO.CarOrderCheckOrImportResult>> result = Maps.newHashMap();
        result.put("importErrorData", errorList);
        result.put("importSuccessData", successList);
        result.put("importAllData", successList);
        // 导入结果集
        carOrderImportRespVOMap.put(fileVersionId, result);

    }

    /**
     * 调用接口创建订单
     *
     * @param row 行数据
     */
    private void generateOrder(CarOrderSource source, Row row
            , CarOrderImportRespVO.CarOrderCheckOrImportResult result) {
        String orderDate = "";
        String surname = "";
        String mobileNumber = "";
        String flightNumber = "";
        String pickUpDateTimeStr = "";
        LocalDateTime pickUpDateTime = null;    // 需后续处理时初始化
        String returnDateTimeStr = "";
        LocalDateTime returnDateTime = null;
        String vehicle = "";
        Integer status = 3;
        BigDecimal overtimecost = new BigDecimal(0);
        boolean overtimePrepay = false;
        String packageName = "";
        String sippCode = "";
        String emailAddress = "";
        String amountStr = "";
        BigDecimal amount = BigDecimal.ZERO;
        String currency = "USD";
        // 需后续处理时初始化
        Long pickUpLocationId = 0L;
        String pickUpLocationName = "";
        Long returnLocationId = 0L;
        String returnLocationName = "";
        String sourceOrderId = "";
        String note = "";
        Long vendorId = null;
        BigDecimal prepayAmount = new BigDecimal(0);
        BigDecimal payOnArrival = new BigDecimal(0);
        BigDecimal totalAmount = new BigDecimal(0);
        String localCurrencyCode = "";
        BigDecimal oneWayFee = new BigDecimal(0);
        BigDecimal specialPickupTimeFee = new BigDecimal(0);
        BigDecimal specialReturnTimeFee = new BigDecimal(0);
        String countryOfOrigin = "";
        BigDecimal settlementAmount = new BigDecimal(0);
        Integer buyInsurance = 0;

        sourceOrderId = getCellStringValue(row, 0, true, "Source Order ID");
        orderDate = getCellStringValue(row, 1, false, "Order Date");
        emailAddress = getCellStringValue(row, 8, false, "Email");
        surname = getCellStringValue(row, 6, false, "Surname");
        mobileNumber = getCellStringValue(row, 7, false, "Mobile");
        flightNumber = getCellStringValue(row, 9, false, "Flight Number");
        pickUpLocationName = getCellStringValue(row, 55, false, "Pickup Location");
        returnLocationName = getCellStringValue(row, 58, false, "Return Location");
        sippCode = getCellStringValue(row, 50, false, "SIPP Code");

        amountStr = getCellStringValue(row, 60, true, "Amount");
        if (amountStr != null) {
            settlementAmount = new BigDecimal(amountStr.replaceAll("[^0-9.]", ""));
            if (!amountStr.contains(CURRENCY_PREFIX)) {
                throw new IllegalArgumentException("金额格式错误，缺少USD前缀: " + amountStr);
            }
            String numericPart = amountStr.split(CURRENCY_PREFIX)[1].trim();
            amount = parseBigDecimal(numericPart);
        }

        // 3. 处理日期时间
        pickUpDateTimeStr = getCellStringValue(row, 14, true, "Pickup Time");
        returnDateTimeStr = getCellStringValue(row, 15, true, "Return Time");
        pickUpDateTime = LocalDateTime.parse(pickUpDateTimeStr, FORMATTER);
        returnDateTime = LocalDateTime.parse(returnDateTimeStr, FORMATTER);

        if (null != row.getCell(26) && null != row.getCell(26).getStringCellValue()) {
            note = row.getCell(26).getStringCellValue();

        }
        packageName = row.getCell(46).getStringCellValue();
        vehicle = row.getCell(19).getStringCellValue();
        if (row.getCell(34) != null) {
            Cell cell = row.getCell(34);
            String cellValue = cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : "";
            if (!cellValue.trim().isEmpty()) {
                try {
                    overtimecost = new BigDecimal(cellValue);
                } catch (NumberFormatException e) {
                    // 处理非法数字格式
                    overtimecost = BigDecimal.ZERO; // 或抛出业务异常
                }
            }
        }
        if (overtimecost.compareTo(BigDecimal.ZERO) > 0) {
            overtimePrepay = true;
        }

//        String cellToString = ExcelUtils.getCellToString(row.getCell(21));
        if (null != row.getCell(21)) {
            prepayAmount = new BigDecimal(row.getCell(21).getStringCellValue().replaceAll("[^0-9.]", ""));
        }
        if (null != row.getCell(22) && null != row.getCell(22).getStringCellValue()) {
            payOnArrival = new BigDecimal(row.getCell(22).getStringCellValue().replaceAll("[^0-9.]", ""));
        }
        if (null != row.getCell(23) && null != row.getCell(23).getStringCellValue()) {
            totalAmount = new BigDecimal(row.getCell(23).getStringCellValue().replaceAll("[^0-9.]", ""));
        }
        if (null != row.getCell(24) && null != row.getCell(24).getStringCellValue()) {
            localCurrencyCode = row.getCell(24).getStringCellValue();
        }
        if (null != row.getCell(31) && null != row.getCell(31).getStringCellValue()) {
            oneWayFee = new BigDecimal(row.getCell(31).getStringCellValue().replaceAll("[^0-9.]", ""));
        }
        if (null != row.getCell(34) && null != row.getCell(34).getStringCellValue()) {
            specialPickupTimeFee = new BigDecimal(row.getCell(34).getStringCellValue().replaceAll("[^0-9.]", ""));
        }
        if (null != row.getCell(37) && null != row.getCell(37).getStringCellValue()) {
            specialReturnTimeFee = new BigDecimal(row.getCell(37).getStringCellValue().replaceAll("[^0-9.]", ""));
        }
        if (null != row.getCell(59) && null != row.getCell(59).getStringCellValue()) {
            countryOfOrigin = row.getCell(59).getStringCellValue();
        }
        if (null != row.getCell(60) && null != row.getCell(60).getStringCellValue()) {
            String settleAmountStr = row.getCell(60).getStringCellValue().replaceAll("[^0-9.]", "");
            settlementAmount = new BigDecimal(settleAmountStr);
            currency = settleAmountStr.replaceAll(settleAmountStr, "");

        }
        if (null != row.getCell(69) && null != row.getCell(69).getStringCellValue()) {
            buyInsurance = Objects.equals(row.getCell(69).getStringCellValue(), "YES") ? 1 : 0;
        }

        StoreQueryReq storeQueryReq = new StoreQueryReq();
        storeQueryReq.setName(pickUpLocationName);
        ApiResponse<StoreResp> pickUpStore = ordersrvFeignClient.storeSearch(storeQueryReq);
        if (!pickUpStore.isSuccess() || ObjectUtils.isEmpty(pickUpStore.getResult())) {
            result.setImportFailReason(result.getImportFailReason() + "取车门店不存在；");
        } else {
            pickUpLocationId = pickUpStore.getResult().getId();
            vendorId = pickUpStore.getResult().getVendorId();
        }
        storeQueryReq.setName(returnLocationName);
        ApiResponse<StoreResp> returnStore = ordersrvFeignClient.storeSearch(storeQueryReq);
        if (!returnStore.isSuccess()) {
            result.setImportFailReason(result.getImportFailReason() + "还车门店不存在；");
        } else {
            returnLocationId = returnStore.getResult().getId();
        }
        // 设置stock_id
        String modelName = "";
        String[] arr1 = vehicle.split("自动");
        if (arr1.length > 1) {
            modelName = arr1[0].split("-")[1].trim();
        } else {
            String[] arr2 = vehicle.split("手动");
            modelName = arr2[0].split("-")[1].trim();
        }
        if (modelName.split(" ").length > 1) {
            if (carBrands.containsKey(modelName.split(" ")[0])) {
                modelName = carBrands.get(modelName.split(" ")[0]) + " " + modelName.split(" ")[1];
            }
        }
        ApiResponse<CarModelPO> carModelPOApiResponse =
                productsrvFeignClient.velModelNameQuery(modelName);
        if (!carModelPOApiResponse.isSuccess() || carModelPOApiResponse.getError() != null) {
            result.setImportFailReason(result.getImportFailReason() + "vehicle车型不存在；");
        }
        StockRangeReq storeQueryReq1 = new StockRangeReq();
        storeQueryReq1.setStoreId(pickUpStore.getResult().getId());
        storeQueryReq1.setModelId(carModelPOApiResponse.getResult().getId());
        ApiResponse<JipinzucheCarStockResp> jipinzucheCarStockApiResponse =
                ordersrvFeignClient.vehCarStock(storeQueryReq1);
        if (null == jipinzucheCarStockApiResponse
                || !jipinzucheCarStockApiResponse.isSuccess() || jipinzucheCarStockApiResponse.getError() != null) {
            result.setImportFailReason(result.getImportFailReason() + "车型stock不存在；");
        }

        CarStockInsuranceReq carStockInsuranceReq = new CarStockInsuranceReq();
        carStockInsuranceReq.setStockId(Long.valueOf(jipinzucheCarStockApiResponse.getResult().getId()));
        carStockInsuranceReq.setTitle(packageName);
        ApiResponse<CarStockInsuranceManagementModel> carStockInsuranceManagementModelApiResponse =
                productsrvFeignClient.velCarStockInsuranceTitleQuery(carStockInsuranceReq);
        if (!carStockInsuranceManagementModelApiResponse.isSuccess()
                || carStockInsuranceManagementModelApiResponse.getError() != null
                || carStockInsuranceManagementModelApiResponse.getResult() == null) {
            result.setImportFailReason(result.getImportFailReason() + "车型保险package name不存在；");
        }
        // 入库结果处理
        result.setSourceOrdercode(sourceOrderId);

        CarOrderOrderSrvVehAddReq carOrderPO = new CarOrderOrderSrvVehAddReq();

        List<JipinzucheCarList> jipinzucheCarLists =
                jipinzucheCarListRepository.selectByStockId(jipinzucheCarStockApiResponse.getResult().getId());
        if (null != jipinzucheCarLists && !jipinzucheCarLists.isEmpty()) {
            carOrderPO.setAppointCarid(jipinzucheCarLists.getFirst().getId());
        }
        carOrderPO.setOrdercode(genCode());
        carOrderPO.setNote(note);
        carOrderPO.setAppointCarname(modelName);
        carOrderPO.setCarname(modelName);
        carOrderPO.setTime(LocalDateTime.parse(orderDate, FORMATTER));
        carOrderPO.setPaytime(LocalDateTime.parse(orderDate, FORMATTER));
        carOrderPO.setUsername(surname);
        carOrderPO.setMobile(mobileNumber);
        carOrderPO.setContactline(flightNumber);
        carOrderPO.setAppointStarttime(pickUpDateTime);
        carOrderPO.setAppointEndtime(returnDateTime);
        int days = getDays(pickUpDateTime, returnDateTime);
        carOrderPO.setAppointDays(days);
        carOrderPO.setStockid(jipinzucheCarStockApiResponse.getResult().getId());
        carOrderPO.setAppointStockid(jipinzucheCarStockApiResponse.getResult().getId());
        carOrderPO.setStatus(status);
        carOrderPO.setNote(note);
        carOrderPO.setOvertimecost(overtimecost);
        carOrderPO.setOvertimePrepay(overtimePrepay);
        carOrderPO.setInsurance(InsuranceEnum.getByName(packageName).getCode());
        carOrderPO.setStockInsId(Math.toIntExact(carStockInsuranceManagementModelApiResponse.getResult().getId()));
        carOrderPO.setStockInsTitle(carStockInsuranceManagementModelApiResponse.getResult().getTitle());
        carOrderPO.setAppointCarcode(sippCode);
        carOrderPO.setEmail(emailAddress);
        // 总价 暂存当地货币总金额 todo
        carOrderPO.setTotalprice(totalAmount);
        // 结算货币
        carOrderPO.setUnit(source.getSettlementCurrency());

        carOrderPO.setAppointGetstoreid(Math.toIntExact(pickUpLocationId));
        carOrderPO.setAppointGetstorename(pickUpLocationName);
        carOrderPO.setAppointReturnstoreid(Math.toIntExact(returnLocationId));
        carOrderPO.setAppointReturnstorename(returnLocationName);
        carOrderPO.setSourceOrdercode(sourceOrderId);
        carOrderPO.setSource(Math.toIntExact(source.getId()));
        carOrderPO.setCreateType(1);
        carOrderPO.setVendorId(vendorId);

        carOrderPO.setPrepaidAmount(prepayAmount);
        carOrderPO.setCashOnDeliveryAmount(payOnArrival);
        carOrderPO.setOneWayFee(oneWayFee);
        carOrderPO.setSpecialPickupTimeFee(specialPickupTimeFee);
        carOrderPO.setSpecialReturnTimeFee(specialReturnTimeFee);
        carOrderPO.setBuyInsurance(buyInsurance);
        carOrderPO.setLocalCurrency(localCurrencyCode);
        carOrderPO.setSettlementAmount(settlementAmount);

        carOrderPO.setCitizenCountryCode(countryOfOrigin);


        ApiResponse<Integer> updateResp = ordersrvFeignClient.vehAdd(carOrderPO);
        if (!updateResp.isSuccess() || updateResp.getResult() == null) {
            result.setImportFailReason(result.getImportFailReason() + "订单数据入库失败，请联系管理员；");
        }/* else {
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setSourceOrderId(carOrderPO.getSourceOrdercode());
            orderQueryReq.setSource(carOrderPO.getSource());
            JipinzucheCarOrderPO jipinzucheCarOrder =
                    jipinzucheCarOrderRepository.selectBySourceAndSourceOrderCode(orderQueryReq);
            if (null != jipinzucheCarOrder && null != jipinzucheCarOrder.getId()) {
                IxCarOrderCustomer ixCarOrderCustomer = new IxCarOrderCustomer();
                ixCarOrderCustomer.setCarOrderId(Long.valueOf(jipinzucheCarOrder.getId()));
                ixCarOrderCustomer.setDriverAge(28);
                ixCarOrderCustomer.setSurname(carOrderPO.getUsername());
                ixCarOrderCustomer.setGivenName(carOrderPO.getUsername());
                if (StringUtils.isNotEmpty(mobileNumber) && mobileNumber.split("-").length == 2) {
                    ixCarOrderCustomer.setMobileAreaCityCode(mobileNumber.split("-")[0]);
                    ixCarOrderCustomer.setMobilePhoneNumber(mobileNumber.split("-")[1]);
                }
                ixCarOrderCustomer.setEmailAddress(carOrderPO.getEmail());
                ixCarOrderCustomer.setCitizenCountryCode(countryOfOrigin);
                Integer insert = ixCarOrderCustomerRepository.insert(ixCarOrderCustomer);
                if (null == insert || insert == 0) {
                    result.setImportFailReason(result.getImportFailReason() + "订单客户数据入库失败，请联系管理员；");
                }

                IxCarOrderPrice ixCarOrderPrice = new IxCarOrderPrice();
                ixCarOrderPrice.setCarOrderId(Long.valueOf(jipinzucheCarOrder.getId()));
                ixCarOrderPrice.setPrepaidAmount(prepayAmount);
                ixCarOrderPrice.setCashOnDeliveryAmount(payOnArrival);
                ixCarOrderPrice.setOneWayFee(oneWayFee);
                ixCarOrderPrice.setSpecialPickupTimeFee(specialPickupTimeFee);
                ixCarOrderPrice.setSpecialReturnTimeFee(specialReturnTimeFee);
                ixCarOrderPrice.setBuyInsurance(buyInsurance);
                ixCarOrderPrice.setLocalCurrency(localCurrencyCode);
                ixCarOrderPrice.setSettlementAmount(settlementAmount);
                Long l = orderPriceRepository.insertOne(ixCarOrderPrice);
                if (null == l || l == 0) {
                    result.setImportFailReason(result.getImportFailReason() + "订单金额信息数据入库失败，请联系管理员；");
                }
            } else {
                result.setImportFailReason(result.getImportFailReason() + "订单新增入库数据查询失败，请联系管理员；");
            }
        }*/

    }


    /**
     * excel文件表头检查字段数据合法性校验
     *
     * @param distribution 渠道
     * @param file         文件
     * @return 结果集
     */
    private Map<String, List<CarOrderImportRespVO.CarOrderCheckOrImportResult>> fileHeaderCheck(Long fileVersionId
            , String distribution, MultipartFile file) {
        // 选择
        Map<String, List<CarOrderImportRespVO.CarOrderCheckOrImportResult>> checkResultMap = new HashMap<>();

        excelValidationProcessor.validateExcel(fileVersionId, distribution, file, checkResultMap);

        return checkResultMap;
    }

    /**
     * 获取结果集分页数据
     *
     * @param lineFailList 结果集
     * @param pageParam    分页
     * @return 分页数据
     */
    private static PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> getPageResult(
            List<CarOrderImportRespVO.CarOrderCheckOrImportResult> lineFailList, PageParam pageParam) {
        int fromIndex = Math.max((pageParam.getPageNo() - 1) * pageParam.getPageSize(), 0);
        // 边界检查
        int toIndex = Math.min(fromIndex + pageParam.getPageSize(), lineFailList.size());
        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> pageData =
                (fromIndex < toIndex)
                        ? lineFailList.subList(fromIndex, toIndex)
                        : Collections.emptyList();  // 如果范围无效返回空列表
        PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> pageResult = new PageResult<>();
        pageResult.setTotal((long) lineFailList.size());
        pageResult.setList(pageData);
        return pageResult;
    }

    private static String getCellStringValue(Row row, int cellIndex, boolean required, String fieldName) {
        if (row == null) {
            if (required) throw new IllegalArgumentException(fieldName + "不能为空");
            return null;
        }

        Cell cell = row.getCell(cellIndex);
        if (cell == null || cell.getCellType() == CellType.BLANK) {
            if (required) throw new IllegalArgumentException(fieldName + "不能为空");
            return null;
        }

        try {
            String value = cell.getCellType() == CellType.STRING
                    ? cell.getStringCellValue().trim()
                    : String.valueOf(cell.getNumericCellValue()).trim();

            if (required && value.isEmpty()) {
                throw new IllegalArgumentException(fieldName + "不能为空");
            }
            return value.isEmpty() ? null : value;
        } catch (Exception e) {
            throw new IllegalArgumentException(fieldName + "格式错误", e);
        }
    }

    private static BigDecimal parseBigDecimal(String value) {
        if (value == null || value.trim().isEmpty()) return null;

        try {
            return new BigDecimal(value.replaceAll(",", "")); // 处理千分位逗号
        } catch (NumberFormatException e) {
            return null;
        }
    }

}