package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 规则信息 resp
 *
 * @author: Phili
 * @date： 2025/3/31
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RuleSearchResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 附加取车规则
  */
 private AdditionalPickupRule additionalPickupRule;

 /**
  * 提前预订规则
  */
 private AdvanceBookingRule advanceBookingRule;

 /**
  * 年龄规则
  */
 private AgeRule ageRule;

 /**
  * 取消规则
  */
 private CancelRule cancelRule;

 /**
  * 跨境规则
  */
 private CrossBorderRule crossBorderRule;

 /**
  * 押金规则
  */
 private DepositRule depositRule;

 /**
  * 规则ID
  */
 private String id;

 /**
  * 责任条款
  */
 private Liabilities liabilities;

 /**
  * 驾照规则
  */
 private LicenseRule licenseRule;

 /**
  * 到店支付规则
  */
 private PayOnArrivalRule payOnArrivalRule;

 /**
  * 取车还车规则
  */
 private List<PickupReturnRule> pickupReturnRules;

 /**
  * 规则适用范围
  */
 private RuleScope ruleScope;

 /**
  * 规则类型
  */
 private String type;


 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class AdditionalPickupRule {
  /**
   * 驾照备注
   */
  private String driverLicenseNote;

  /**
   * ID描述
   */
  private String idDescription;

  /**
   * 其他取车材料
   */
  private String otherPickupMaterial;

  /**
   * 是否需要票据
   */
  private Boolean ticketRequiredInd;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class AdvanceBookingRule {
  /**
   * 最大提前预订时间
   */
  private TimePeriod maxAdvancedBookingTime;

  /**
   * 最小提前预订时间
   */
  private TimePeriod minAdvancedBookingTime;

  /**
   * 是否必须
   */
  private Boolean requiredInd;

  @Data
  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  public static class TimePeriod {
   /**
    * 时间长度
    */
   private Integer period;

   /**
    * 时间类型
    */
   private String type;
  }
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class AgeRule {
  /**
   * 年龄附加费
   */
  private List<AgeSurcharge> ageSurCharge;

  /**
   * 最大年龄
   */
  private Integer maximumAge;

  /**
   * 最小年龄
   */
  private Integer minimumAge;

  @Data
  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  public static class AgeSurcharge {
   /**
    * 年龄收费类型
    */
   private String ageChargeType;

   /**
    * 年龄限制
    */
   private Integer ageLimit;

   /**
    * 收费金额
    */
   private Money chargeAmount;

   /**
    * 驾驶员年龄类型
    */
   private String driverAgeType;

   /**
    * 最大收费
    */
   private Money maximumCharge;

   /**
    * 是否必须收费
    */
   private Boolean requiredCharge;

   /**
    * 是否含税
    */
   private Boolean taxInclusive;
  }
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class Money {
  /**
   * 金额
   */
  private Number amount;

  /**
   * 货币类型
   */
  private String currency;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class CancelRule {
  /**
   * 取消规则详情列表
   */
  private List<CancelRuleDetail> cancelRuleDetailList;

  /**
   * 免费取消小时数
   */
  private Integer freeCancelHours;

  @Data
  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  public static class CancelRuleDetail {
   /**
    * 最大提前小时数
    */
   private Integer advanceHoursMax;

   /**
    * 最小提前小时数
    */
   private Integer advanceHoursMin;

   /**
    * 金额上限
    */
   private Money amountUpperLimit;

   /**
    * 收费模式
    */
   private Integer chargeMode;

   /**
    * 天数
    */
   private Integer day;

   /**
    * 固定金额
    */
   private Money fixedAmount;

   /**
    * 百分比
    */
   private String percentage;
  }
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class CrossBorderRule {
  /**
   * 国家代码列表
   */
  private List<String> countryCodes;

  /**
   * 跨境类型
   */
  private String crossBorderType;

  /**
   * 跨境规则
   */
  private Integer crossRule;

  /**
   * 描述
   */
  private String description;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class DepositRule {
  /**
   * 押金模式
   */
  private String depositMode;

  /**
   * 是否需要押金
   */
  private Boolean depositRequiredInd;

  /**
   * 押金类型
   */
  private String depositType;

  /**
   * 最大押金金额
   */
  private Money maxDepositAmount;

  /**
   * 最大退款天数
   */
  private Integer maxRefundDay;

  /**
   * 最小押金金额
   */
  private Money minDepositAmount;

  /**
   * 最小退款天数
   */
  private Integer minRefundDay;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class Liabilities {
  /**
   * 保险覆盖范围
   */
  private List<Coverage> coverages;

  @Data
  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  public static class Coverage {
   /**
    * 描述
    */
   private String description;

   /**
    * 类型
    */
   private Integer type;
  }
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class LicenseRule {
  /**
   * 目的地
   */
  private Location destination;

  /**
   * 驾照组
   */
  private List<DrivingLicenseGroup> drivingLicenseGroups;

  /**
   * 最小驾驶经验
   */
  private Integer minDrivingExperience;

  /**
   * 出发地
   */
  private Location origin;

  /**
   * 是否需要驾驶经验
   */
  private Boolean requiredDrivingExperience;

  /**
   * 驾驶经验单位
   */
  private String unitDrivingExperience;

  @Data
  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  public static class Location {
   /**
    * 代码
    */
   private String code;

   /**
    * 类型
    */
   private Integer type;
  }

  @Data
  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  public static class DrivingLicenseGroup {
   /**
    * 文档列表
    */
   private List<Document> documents;

   /**
    * 组排序号
    */
   private Integer groupSortNum;
  }

  @Data
  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  public static class Document {
   /**
    * 描述
    */
   private String description;

   /**
    * 驾照类型
    */
   private String licenceType;

   /**
    * 排序号
    */
   private Integer sortNum;
  }
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class PayOnArrivalRule {
  /**
   * 描述
   */
  private String description;

  /**
   * 支付方式
   */
  private String payMethod;

  /**
   * 是否需要芯片卡
   */
  private Boolean requiredChipCard;

  /**
   * 是否需要凸印卡
   */
  private Boolean requiredEmbossedCard;

  /**
   * 是否需要两张信用卡
   */
  private Boolean requiredTwoCreditCards;

  /**
   * 支持的卡代码列表
   */
  private List<String> supportCardCodeList;

  /**
   * 是否支持银联双币卡
   */
  private Boolean supportUnionPayDualCard;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class PickupReturnRule {
  /**
   * 描述
   */
  private String description;

  /**
   * 类型
   */
  private String type;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class RuleScope {
  /**
   * 国家代码列表
   */
  private List<String> countryCodes;

  /**
   * 位置详情
   */
  private List<LocationDetail> locationDetails;

  /**
   * 车辆代码
   */
  private List<String> vehicleCodes;

  @Data
  @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
  public static class LocationDetail {
   /**
    * 代码
    */
   private String code;

   /**
    * 代码上下文
    */
   private String codeContext;
  }
 }
}
