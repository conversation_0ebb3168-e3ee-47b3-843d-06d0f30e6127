package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付方式枚举
 */
@Getter
@AllArgsConstructor
public enum PayMethodEnum implements DictInf {

    PAY_AT_COUNTER(1, "到付"),
    PREPAID(2, "预付"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static PayMethodEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}


