package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 车辆保险
 *
 * <AUTHOR>
 * @since 2025-03-24 09:44:00
 */
@Data
public class CarStockInsuranceDTO {

    /**
     * 库存id
     */
    @JsonProperty("stock_id")
    private Long stockId;

    /**
     * car_model id
     */
    @JsonProperty("model_id")
    private Long modelId;

    /**
     * 保险 id
     */
    @JsonProperty("insurance_id")
    private Long insuranceId;

    /**
     * 保险名称
     */
    @JsonProperty("title")
    private String title;

    /**
     * 保险code
     */
    @JsonProperty("code")
    private String code;

    /**
     * 保险价格
     */
    @JsonProperty("price")
    private BigDecimal price;
}
