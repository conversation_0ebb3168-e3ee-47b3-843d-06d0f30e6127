package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.CarOrderSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/18 14:22
 */
@DS("ix")
@Mapper
public interface CarOrderSourceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CarOrderSource record);

    Long insertSelective(CarOrderSource record);

    CarOrderSource selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CarOrderSource record);

    int updateByPrimaryKey(CarOrderSource record);

    int updateBatch(List<CarOrderSource> list);

    int updateBatchSelective(List<CarOrderSource> list);

    int batchInsert(@Param("list") List<CarOrderSource> list);

    /**
     * 根据条件查询渠道信息
     *
     * @param id              渠道id
     * @param channelName     渠道简称
     * @param companyFullName 公司全称
     * @return
     */
    List<CarOrderSource> searchByCondition(@Param(value = "id") Long id,
                                           @Param(value = "channelName") String channelName,
                                           @Param(value = "companyFullName") String companyFullName);

    /**
     * 查询指定stage对应的渠道信息
     * @param stage
     * @return
     */
    List<CarOrderSource> selectByStage(@Param(value = "stage") String stage);

    List<CarOrderSource> findAllByJipinzucheCarOrderSource();

    CarOrderSource selectByTitle(String fileType);
}