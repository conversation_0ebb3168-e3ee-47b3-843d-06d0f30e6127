package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 费用支付状态枚举
 * 对应表: transfer_order_charge, 字段: payment_status
 */
@Getter
public enum TransferPaymentStatusEnum {

    PENDING_PAYMENT(1, "待支付"),
    PAID(2, "已支付"),
    ;

    private final Integer code;
    private final String label;

    TransferPaymentStatusEnum(Integer code, String label) {
        this.code = code;
        this.label = label;
    }

}
