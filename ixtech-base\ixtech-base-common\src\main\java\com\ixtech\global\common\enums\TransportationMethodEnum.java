package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransportationMethodEnum implements DictInf {

    WALKING("1", "步行可达"),
    FREE_SHUTTLE("2", "免费巴士"),
    PAID_SHUTTLE("3", "收费巴士"),
    TAXI_RECOMMENDED("4", "建议打的"),
    LIGHT_RAIL("5", "轻轨列车"),
    CONTACT_PICKUP("6", "联系接送"),
    SIGN_PICKUP("7", "举牌接机"),
    CAR_DELIVERY("8", "送车上门"),
    SELF_SERVICE("9", "步行可达，自助取还");

    private final String value;
    private final String label;
}