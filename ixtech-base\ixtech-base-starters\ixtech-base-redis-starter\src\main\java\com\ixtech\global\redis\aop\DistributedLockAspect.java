package com.ixtech.global.redis.aop;

import com.ixtech.global.common.exception.ServerException;
import com.ixtech.global.redis.RedisLockService;
import com.ixtech.global.redis.annotation.DistributedLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

import static com.ixtech.global.common.exception.enums.CommonErrorCode.LOCK_FAILED;


@Aspect
@Component
@Slf4j
@ConditionalOnClass(Aspect.class)
@RequiredArgsConstructor
public class DistributedLockAspect {

    private final RedisLockService redisLockService;

    // SpEL 表达式解析器 (优化为静态常量)
    private static final ExpressionParser PARSER = new SpelExpressionParser();
    // 用于获取方法参数名 (优化为静态常量)
    private static final DefaultParameterNameDiscoverer DISCOVERER = new DefaultParameterNameDiscoverer();

    /**
     * 从配置文件注入统一的锁前缀。
     * 推荐使用 "spring.application.name" 来区分不同服务。
     * 默认值为 "distributed-lock:"，确保在未配置时也有一个默认前缀。
     */
    // @Value("${myapp.lock.key-prefix:${spring.application.name:default-app}}:lock:")
    private String lockKeyPrefix = "lock::";

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        // 1. 解析 SpEL 表达式，生成锁 key 的动态部分
        String dynamicKeyPart = parseDynamicKey(distributedLock.key(), joinPoint);
        if (StringUtils.isEmpty(dynamicKeyPart)) {
            log.warn("DistributedLock dynamic key part is empty, skipping lock. Method: {}", joinPoint.getSignature().toShortString());
            return joinPoint.proceed();
        }

        // 2. 拼接上前缀，构成完整的 lock key
        String lockKey = lockKeyPrefix + dynamicKeyPart;
        log.debug("Generated distributed lock key: {}", lockKey);

        // 3. 获取锁
        RLock lock = redisLockService.getLock(lockKey);

        // 4. 尝试加锁
        try {
            boolean isLocked = lock.tryLock(distributedLock.waitTime(), distributedLock.leaseTime(), distributedLock.unit());

            if (!isLocked) {
                log.warn("Failed to acquire distributed lock, key: {}", lockKey);
                throw ServerException.of(LOCK_FAILED);
            }

            // 5. 加锁成功，执行业务方法
            return joinPoint.proceed();
        } finally {
            // 6. 释放锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 解析 SpEL 表达式，获取锁的动态部分
     *
     * @param keyExpression SpEL 表达式
     * @param joinPoint     切点
     * @return 解析后的字符串
     */
    private String parseDynamicKey(String keyExpression, ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        EvaluationContext context = new MethodBasedEvaluationContext(null, method, args, DISCOVERER);

        try {
            return PARSER.parseExpression(keyExpression).getValue(context, String.class);
        } catch (Exception e) {
            log.error("Error parsing SpEL expression for distributed lock key: {}", keyExpression, e);
            // SpEL 解析失败时，也应抛出异常，避免使用错误的 key
            throw new IllegalArgumentException("Failed to parse Spring Expression Language (SpEL) for key: " + keyExpression, e);
        }
    }
}
