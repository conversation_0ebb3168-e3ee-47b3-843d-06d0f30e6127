package com.ixtech.global.common.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 新的日期工具类
 * 规避LocalDateTimeUtils工具类中因为初始化TimeZoneEngine造成的执行缓慢影响
 *
 * <AUTHOR> hu
 * @date 2025/7/30 09:07
 */
public class DateTimeUtils {

    public static final String UTC = "UTC";

    public static final String SHORT_TIME_PATTERN = "HH:mm";
    public static final String TIME_PATTERN = "HH:mm:ss";
    public static final String MONTH_PATTERN = "yyyy-MM";
    public static final String SHORT_DATE_PATTERN = "yyMMdd";
    public static final String SHORT_DATETIME_PATTERN = "yyyy-MM-dd HH:mm";
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_PATTERN = "yyyy-MM-dd";

    public static final DateTimeFormatter SHORT_TIME_FORMATTER = DateTimeFormatter.ofPattern(SHORT_TIME_PATTERN);
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(TIME_PATTERN);
    public static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern(MONTH_PATTERN);
    public static final DateTimeFormatter SHORT_DATE_FORMATTER = DateTimeFormatter.ofPattern(SHORT_DATE_PATTERN);
    public static final DateTimeFormatter SHORT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern(SHORT_DATETIME_PATTERN);
    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(DATETIME_PATTERN);
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

    /**
     * 获取当前时刻的UTC+0时区的时间字符串
     *
     * @return 返回格式 yyyy-MM-dd HH:mm:ss
     */
    public static String getCurrentUtcTimeStr() {
        LocalDateTime utcLocalDateTime = getCurrentUtcTime();
        return utcLocalDateTime.format(DATETIME_FORMATTER);
    }

    /**
     * 获取当前时刻的UTC+0时区的时间
     *
     * @return
     */
    public static LocalDateTime getCurrentUtcTime() {
        return LocalDateTime.now(ZoneId.of(UTC));
    }

    /**
     * 将字符串时间转换为LocalDateTime
     *
     * @param dateTimeStr 时间，格式 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, DATETIME_FORMATTER);
    }

    /**
     * 将LocalDateTime转换为字符串时间，返回格式：yyyy-MM-dd HH:mm:ss
     *
     * @param localDateTime
     * @return
     */
    public static String formatDateTime(LocalDateTime localDateTime) {
        return localDateTime.format(DATETIME_FORMATTER);
    }

    /**
     * 推测本地时间对应的时区
     *
     * @param utcDateTime   UTC+0时区时间
     * @param localDateTime 对应的本地时间
     * @return
     */
    public static String guessLocalTimezone(LocalDateTime utcDateTime, LocalDateTime localDateTime) {

        if (utcDateTime == null || localDateTime == null) {
            return null;
        }

        // 计算本地时区与UTC+0的偏移量（分钟）
        long utcOffsetMinutes = ChronoUnit.MINUTES.between(utcDateTime, localDateTime);

        // 将分钟转换为小时和分钟
        long hours = utcOffsetMinutes / 60;
        long minutes = Math.abs(utcOffsetMinutes % 60);

        // 格式化输出
        return String.format("UTC%+03d:%02d", hours, minutes);
    }

    /**
     * 将UTC0时区的时间字符串转换为指定时区的本地时间字符串
     *
     * @param utcTime       UTC+0时区的时间字符串，格式yyyy-MM-dd HH:mm:ss
     * @param localTimeZone 本地 IANA 时区，eg: Asia/Shanghai
     * @return
     */
    public static String convertUtcTimeToLocalTime(String utcTime, String localTimeZone) {

        // 将UTC时间字符串解析为LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.parse(utcTime, DATETIME_FORMATTER);

        // 将LocalDateTime转换为ZonedDateTime，指定为UTC时区
        ZonedDateTime utcZonedDateTime = localDateTime.atZone(ZoneOffset.UTC);

        // 转换为目标时区
        ZoneId targetZoneId = ZoneId.of(localTimeZone);
        ZonedDateTime localZonedDateTime = utcZonedDateTime.withZoneSameInstant(targetZoneId);

        // 格式化为字符串返回
        return localZonedDateTime.format(DATETIME_FORMATTER);
    }

    /**
     * 将UTC0时区的时间字符串转换为指定时区的本地时间字符串
     *
     * @param utcTime       UTC+0时区的时间(数据库默认时间)
     * @param localTimeZone 本地 IANA 时区，eg: Asia/Shanghai
     * @return
     */
    public static String convertUtcTimeToLocalTime(LocalDateTime utcTime, String localTimeZone) {

        // 将LocalDateTime转换为ZonedDateTime，指定为UTC时区
        ZonedDateTime utcZonedDateTime = utcTime.atZone(ZoneOffset.UTC);

        // 转换为目标时区
        ZoneId targetZoneId = ZoneId.of(localTimeZone);
        ZonedDateTime localZonedDateTime = utcZonedDateTime.withZoneSameInstant(targetZoneId);

        // 格式化为字符串返回
        return localZonedDateTime.format(DATETIME_FORMATTER);
    }

    /**
     * 将本地时间字符串（指定时区）转换为UTC+0时区的时间字符串
     *
     * @param localTime     本地时间字符串，格式yyyy-MM-dd HH:mm:ss
     * @param localTimeZone 本地IANA时区，例如: Asia/Shanghai
     * @return
     */
    public static String convertLocalTimeToUtc(String localTime, String localTimeZone) {

        // 将本地时间字符串解析为LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.parse(localTime, DATETIME_FORMATTER);

        // 将LocalDateTime转换为ZonedDateTime，指定为本地时区
        ZoneId localZoneId = ZoneId.of(localTimeZone);
        ZonedDateTime localZonedDateTime = localDateTime.atZone(localZoneId);

        // 转换为UTC时区
        ZonedDateTime utcZonedDateTime = localZonedDateTime.withZoneSameInstant(ZoneOffset.UTC);

        // 格式化为字符串返回
        return utcZonedDateTime.format(DATETIME_FORMATTER);
    }

}
