package com.ixtech.management.common.dto;

import java.io.Serializable;

import com.ixtech.management.common.exception.inf.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Generated;

@Data
public class ApiResponse<T> implements Serializable {
    private static final long serialVersionUID = 2326196004883935529L;
    private boolean success = true;
    private T result;
    private ErrorData error;

    public ApiResponse(boolean success, int code, String message, T result) {
        this.success = success;
        this.result = result;
        this.error = new ErrorData();
        this.error.setMessage(message);
        this.error.setCode(String.valueOf(code));
    }

    public static <T> ApiResponse<T> success() {
        return new ApiResponse<T>();
    }

    public static <T> ApiResponse<T> success(T result) {
        ApiResponse<T> res = new ApiResponse<T>();
        res.setResult(result);
        return res;
    }

    public static <T> ApiResponse<T> success(T result, String message) {
        ApiResponse<T> res = new ApiResponse<T>();
        res.setResult(result);
        return res;
    }

    public static <T> ApiResponse<T> fail(String message) {
        ApiResponse<T> res = new ApiResponse<T>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage(message);
        errorData.setCode(String.valueOf(1));
        res.setError(errorData);
        return res;
    }

    public static <T> ApiResponse<T> fail(String message, int code) {
        ApiResponse<T> res = new ApiResponse<T>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage(message);
        errorData.setCode(String.valueOf(code));
        res.setError(errorData);
        return res;
    }

    public static <T> ApiResponse<T> fail(String message, T result) {
        ApiResponse<T> res = new ApiResponse<T>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage(message);
        errorData.setCode(String.valueOf(1));
        res.setError(errorData);
        return res;
    }

    public static <T> ApiResponse<T> fail(T result) {
        ApiResponse<T> res = new ApiResponse<T>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage("未定义的错误");
        errorData.setCode("1");
        res.setError(errorData);
        res.setResult(result);
        return res;
    }

    public static <T> ApiResponse<T> fail(T result, String message) {
        ApiResponse<T> res = new ApiResponse<T>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage(message);
        errorData.setCode("1");
        res.setError(errorData);
        return res;
    }

    public static <T> ApiResponse<T> makeResult(Boolean success, int code, String message, T result, ErrorData errorData) {
        ApiResponse<T> res = new ApiResponse<T>();
        res.setSuccess(success);
        res.setResult(result);
        res.setError(errorData);
        return res;
    }

    public static <T> ApiResponse<T> fail(ErrorCode errorCode) {
        ErrorData errorData = new ErrorData();
        errorData.setCode(errorCode.getFullCode());
        errorData.setMessage(errorCode.getMessage());
        return new ApiResponse<T>(false, null, errorData);
    }

    public static <T> ApiResponse<T> fail(ErrorCode errorCode, String message) {
        ErrorData errorData = new ErrorData();
        errorData.setCode(errorCode.getFullCode());
        errorData.setMessage(message);
        return new ApiResponse<T>(false, null, errorData);
    }

    public static <T> ApiResponse<T> fail(String code, String message) {
        ErrorData errorData = new ErrorData();
        errorData.setCode(code);
        errorData.setMessage(message);
        return new ApiResponse<T>(false, null, errorData);
    }

    protected ApiResponse() {
    }

    @Generated
    public boolean isSuccess() {
        return this.success;
    }

    @Generated
    public T getResult() {
        return this.result;
    }

    @Generated
    public ErrorData getError() {
        return this.error;
    }

    @Generated
    public void setSuccess(boolean success) {
        this.success = success;
    }

    @Generated
    public void setResult(T result) {
        this.result = result;
    }

    @Generated
    public void setError(ErrorData error) {
        this.error = error;
    }

    @Generated
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ApiResponse)) {
            return false;
        } else {
            ApiResponse<?> other = (ApiResponse)o;
            if (!other.canEqual(this)) {
                return false;
            } else if (this.isSuccess() != other.isSuccess()) {
                return false;
            } else {
                Object this$result = this.getResult();
                Object other$result = other.getResult();
                if (this$result == null) {
                    if (other$result != null) {
                        return false;
                    }
                } else if (!this$result.equals(other$result)) {
                    return false;
                }

                Object this$error = this.getError();
                Object other$error = other.getError();
                if (this$error == null) {
                    if (other$error != null) {
                        return false;
                    }
                } else if (!this$error.equals(other$error)) {
                    return false;
                }

                return true;
            }
        }
    }

    @Generated
    protected boolean canEqual(Object other) {
        return other instanceof ApiResponse;
    }

    @Generated
    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        result = result * 59 + (this.isSuccess() ? 79 : 97);
        Object $result = this.getResult();
        result = result * 59 + ($result == null ? 43 : $result.hashCode());
        Object $error = this.getError();
        result = result * 59 + ($error == null ? 43 : $error.hashCode());
        return result;
    }

    @Generated
    public String toString() {
        boolean var10000 = this.isSuccess();
        return "ApiResponse(success=" + var10000 + ", result=" + String.valueOf(this.getResult()) + ", error=" + String.valueOf(this.getError()) + ")";
    }

    @Generated
    public ApiResponse(boolean success, T result, ErrorData error) {
        this.success = success;
        this.result = result;
        this.error = error;
    }
}
