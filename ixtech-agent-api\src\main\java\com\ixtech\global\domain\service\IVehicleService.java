package com.ixtech.global.domain.service;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.integration.internal.req.ReferenceQueryReq;
import com.ixtech.global.integration.internal.req.VehicleAvailListQueryReq;
import com.ixtech.global.integration.internal.req.VehicleDetailQueryReq;
import com.ixtech.global.integration.internal.resp.ReferenceQueryResp;
import com.ixtech.global.integration.internal.resp.VehicleAvailQueryResp;

/**
 * 核心商品车辆相关接口
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IVehicleService {

    /**
     * 车辆搜索：
     * 1、根据机场码或门店code及所属供应商直接查询门店集合
     * 2、对查询到的门店集合在内存中根据取还车时间进行筛选
     * 3、查询条件组装
     * 4、查询车辆库存信息
     * 5、遍历集合，获取费用等信息
     * 6、拼装结果集
     *
     * @param request 车辆查询request
     * @return VehicleAvailListQueryResp 可用车辆集合
     */
    ApiResponse<VehicleAvailQueryResp> queryVehicleAvailList(VehicleAvailListQueryReq request);

    /**
     * 车辆搜索：
     * 1、根据机场码或门店code及所属供应商直接查询门店集合
     * 2、对查询到的门店集合在内存中根据取还车时间进行筛选
     * 3、查询条件组装
     * 4、查询车辆库存信息
     * 5、遍历集合，获取费用等信息
     * 6、拼装结果集
     *
     * @param request 车辆查询request
     * @return VehicleAvailListQueryResp 可用车辆详情
     */
    ApiResponse<VehicleAvailQueryResp> queryVehicleAvailDetail(VehicleDetailQueryReq request);


    /**
     * 根据referenceId查询简略车辆信息
     */
    ApiResponse<ReferenceQueryResp> queryVehicleByReferenceId(ReferenceQueryReq request);

}
