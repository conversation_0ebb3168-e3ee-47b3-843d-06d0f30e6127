package com.ixtech.global;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@MapperScan({"com.ixtech.global.*.mapper"})
@EnableFeignClients(basePackages = "com.ixtech.global.**.client")
public class IxtechAgentApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(IxtechAgentApiApplication.class, args);
    }

}
