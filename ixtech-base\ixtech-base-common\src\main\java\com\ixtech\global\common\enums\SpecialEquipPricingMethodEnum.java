package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 附加设备 计价单位枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SpecialEquipPricingMethodEnum implements DictInf {

    /**
     * 个
     */
    BY_QUANTITY(1, "个"), // 步骤 3: 修改为 Integer 字面量

    /**
     * 次
     */
    BY_COUNT(2, "次"), // 步骤 3: 修改为 Integer 字面量

    /**
     * 按（个·天）
     */
    BY_ITEM_DAY(3, "个·天"); // 步骤 3: 修改为 Integer 字面量

    /**
     * 单位值
     */
    private final Integer code; // 步骤 1: 修改字段类型为 Integer

    /**
     * 单位名称
     */
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(code);
    }


    public static String getLabelByCode(String code) { // 步骤 5: 修改参数类型为 Integer
        if (code == null) {
            return null;
        }
        for (SpecialEquipPricingMethodEnum specialEquipPricingMethodEnum : SpecialEquipPricingMethodEnum.values()) {
            if (specialEquipPricingMethodEnum.getValue().equals(code)) { // 步骤 5: 使用 equals 比较 Integer
                return specialEquipPricingMethodEnum.getLabel();
            }
        }
        return null;
    }
}
