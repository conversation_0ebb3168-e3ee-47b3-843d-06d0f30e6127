package com.ixtech.global.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;

/**
 * 服务启动后初始化翻译资源的本地缓存
 *
 * <AUTHOR> hu
 * @date 2025/6/10 18:26
 */
@Slf4j
public class I18nTranslationCacheInitializer implements CommandLineRunner {

    private I18nTranslationCacheManager i18nTranslationCacheManager;

    public I18nTranslationCacheInitializer(I18nTranslationCacheManager i18nTranslationCacheManager) {
        this.i18nTranslationCacheManager = i18nTranslationCacheManager;
    }

    @Override
    public void run(String... args) throws Exception {

        // 未启用本地缓存
        boolean useCache = i18nTranslationCacheManager.useCache();
        if (!useCache) {
            return;
        }

        // 启动后初始化本地资源缓存
        new Thread(this::initTranslationLocalCache).start();

    }

    /**
     * 初始化缓存
     */
    private void initTranslationLocalCache() {

        try {
            // 从翻译服务拉取需要初始化的翻译资源对应的多语言翻译内容
            log.debug("正在从翻译服务拉取初始化语言包");
            // TODO 待后续完善

            // 加载到本地缓存中
            log.debug("正在加载初始化语言包");

        } catch (Exception e) {
            log.error("初始化语言包失败", e);
        }

    }

}
