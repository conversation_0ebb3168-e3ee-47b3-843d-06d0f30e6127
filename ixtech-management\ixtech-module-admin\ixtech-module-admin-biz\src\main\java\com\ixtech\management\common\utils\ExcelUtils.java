package com.ixtech.management.common.utils;


import org.apache.poi.ss.usermodel.Cell;

public class ExcelUtils {

    public static String getCellToString(Cell cell) {

        String contactValue = "";
        if (cell != null) {
            // 根据单元格类型获取值
            switch (cell.getCellType()) {
                case STRING:
                    contactValue = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    // 如果是数字类型，转换为字符串
                    contactValue = String.valueOf((long) cell.getNumericCellValue());
                    break;
                case FORMULA:
                    // 如果是公式，尝试获取字符串值
                    try {
                        contactValue = cell.getStringCellValue();
                    } catch (IllegalStateException e) {
                        // 如果公式结果是数字，转换为字符串
                        contactValue = String.valueOf((long) cell.getNumericCellValue());
                    }
                    break;
                default:
                    // 其他类型按空字符串处理
                    contactValue = "";
            }
        }
        return contactValue;
    }
}
