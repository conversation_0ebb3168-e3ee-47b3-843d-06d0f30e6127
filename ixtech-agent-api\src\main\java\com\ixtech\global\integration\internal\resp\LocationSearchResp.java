package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 门店搜索 resp
 *
 * @author: Phili
 * @date： 2025/3/31
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LocationSearchResp implements Serializable {

 private static final long serialVersionUID = 1L;


 /**
  * 地址信息
  */
 private AddressResp address;

 /**
  * 到达方式
  */
 private Integer arrivalWay;

 /**
  * 是否在机场
  */
 private Boolean atAirport;

 /**
  * 代码
  */
 private String code;

 /**
  * 机场码
  */
 private String iata;

 /**
  * 代码上下文
  */
 private String codeContext;

 /**
  * 柜台位置
  */
 private String counterLocation;

 /**
  * 名称
  */
 private String name;

 /**
  * 是否需要航班号
  */
 private Boolean needFlightNo;

 /**
  * 运营时间表
  */
 private List<OperationSchedule> operationSchedules;

 /**
  * 特殊设备列表
  */
 private List<SpecialEquipment> specialEquipments;

 /**
  * 特殊运营时间表
  */
 private List<SpOperationSpecialSchedule> spOperationSchedules;

 /**
  * 电话列表
  */
 private List<Telephone> telephones;

 /**
  * 航站楼信息
  */
 private String terminalInfo;

 /**
  * 航站楼类型
  */
 private Integer terminalType;

 /**
  * 车辆租赁指示信息
  */
 private List<VehRentLocInfoResp> vehRentLocInfos;


 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class OperationSchedule {

  /**
   * 运营时间列表
   */
  private List<OperationTime> operationTimes;

  /**
   * 工作时间类型
   */
  private String workTimeType;

 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class OperationTime {

  /**
   * 星期几的集合，"Sun" "Mon" "Tue" "Weds" "Thur" "Fri" "Sat"
   */
  private List<String> dayOfWeeks;

  /**
   * 持续时间
   */
  private String duration;

  /**
   * 结束时间
   */
  private String end;

  /**
   * 开始时间
   */
  private String start;

  /**
   * 时间描述文本
   */
  private String text;
 }

 /**
  * 门店特殊营业时间业务对象
  */
 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class SpOperationSpecialSchedule {
  /**
   * 特殊营业时段开始日期
   */
  private LocalDate startDate;
  /**
   * 特殊营业时段结束日期
   */
  private LocalDate endDate;
  /**
   * 持续时间
   */
  private String duration;

  /**
   * 结束时间
   */
  private String end;

  /**
   * 开始时间
   */
  private String start;

 }


 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class SpecialEquipment {

  /**
   * 设备描述
   */
  private String equipDesc;

  /**
   * 设备类型
   */
  private String type;

 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class Telephone {

  /**
   * 区号/城市代码
   */
  private String areaCityCode;

  /**
   * 电话号码
   */
  private String phoneNumber;

 }
}
