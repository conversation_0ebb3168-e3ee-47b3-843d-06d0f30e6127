package com.ixtech.management.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum OrderStatusEnum {
    /**
     * 1
     */
    REFUSE(-3, "拒单"),
    DELETED(-2,"删除"),
    CANCELED(-1, "已取消"),
    UNPAY(1, "待付款"),
    UNCONFIRMED(2, "待确认"),
    UNPICKUP(3, "待取车"),
    UNRETURN(4, "待还车"),
    RETURNED(5, "已还车、保养中"),
    COMPLETED(6, "完成");


    private final int code;
    private final String title;

    private static final Map<String, OrderStatusEnum> TITLE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(OrderStatusEnum::getTitle, Function.identity()));

    private static final Map<Integer, OrderStatusEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(OrderStatusEnum::getCode, Function.identity()));

    OrderStatusEnum(int code, String title) {
        this.code = code;
        this.title = title;
    }

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 对应的枚举，找不到返回null
     */
    public static OrderStatusEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据名称获取枚举
     * @param title 名称
     * @return 对应的枚举，找不到返回null
     */
    public static OrderStatusEnum getByName(String title) {
        return TITLE_MAP.get(title);
    }

    /**
     * 检查编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    public static boolean containsCode(Integer code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 检查名称是否存在
     * @param title 名称
     * @return 是否存在
     */
    public static boolean containsName(String title) {
        return TITLE_MAP.containsKey(title);
    }

    /**
     * 获取所有编码
     * @return 编码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(values()).map(OrderStatusEnum::getCode).toArray(Integer[]::new);
    }

    /**
     * 获取所有名称
     * @return 名称数组
     */
    public static String[] getAllNames() {
        return Arrays.stream(values()).map(OrderStatusEnum::getTitle).toArray(String[]::new);
    }

    @Override
    public String toString() {
        return "OrderCreateTypeEnum{" +
                "title='" + title + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
