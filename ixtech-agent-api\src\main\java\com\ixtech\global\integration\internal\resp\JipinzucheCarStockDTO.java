package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 车辆库存
 *
 * <AUTHOR>
 * @since 2025-03-25 15:17:05
 */
@Data
public class JipinzucheCarStockDTO {

    @JsonProperty("id")
    private Long id; // 修改：从 Integer 改为 Long

    /**
     * #__car_model 的id
     */
    @JsonProperty("model_id")
    private Long modelid; // 修改：从 Integer 改为 Long

    /**
     * 门店id
     */
    @JsonProperty("store_id")
    private Long storeid; // 修改：从 Integer 改为 Long

    /**
     * 车辆数量
     */
    @JsonProperty("stock")
    private Integer stock;

    /**
     * 同组名
     */
    @JsonProperty("group_name")
    private String groupName;

    /**
     * 起赔额
     */
    @JsonProperty("accident")
    private BigDecimal accident;

    /**
     * 起赔额单位(如：RMB，THB)
     */
    @JsonProperty("accident_unit")
    private String accidentUnit;
}
