package com.ixtech.management.integration.internal.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 车辆订单表(JipinzucheCarOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-03-21 10:15:47
 */
@Data
public class ManagementCarOrderResp {

    private Long id;

    /**
     * 订单号
     */
    private String ordercode;

    /**
     * 表 #__car_list 的id
     */
    // 修改：将 appointCarid 字段改为 Long
    private Long appointCarid;

    /**
     * 表 #__car_stock 的id
     */
    // 修改：将 appointStockid 字段改为 Long
    private Long appointStockid;

    /**
     * 预约取车门店  表 #__car_stock 的id
     */
    // 修改：将 appointGetstoreid 字段改为 Long
    private Long appointGetstoreid;

    /**
     * 预约还车门店  #__store的id
     */
    // 修改：将 appointReturnstoreid 字段改为 Long
    private Long appointReturnstoreid;

    /**
     * 预约取车门店名称
     */
    private String appointGetstorename;

    /**
     * 预约还车门店名称
     */
    private String appointReturnstorename;

    /**
     * 车辆名称
     */
    private String appointCarname;

    /**
     * 车辆图片
     */
    private String appointCarimg;

    /**
     * 车牌号码
     */
    private String appointPlatenumber;

    /**
     * 租车开始时间
     */
    // 修改：将 appointStarttime 字段改为 LocalDateTime
    private String appointStarttime;

    /**
     * 租车结束时间
     */
    // 修改：将 appointEndtime 字段改为 LocalDateTime
    private String appointEndtime;

    /**
     * 预约天数
     */
    private Integer appointDays;

    /**
     * 罚单收货地址
     */
    private String ticketAddress;

    /**
     * 下单时间
     */
    private String time;

    /**
     * 下单ip
     */
    private String ip;

    /**
     * 1->门店；2->网站；3->微信；4->淘宝；5->携程；6->租租车；7->惠租车；8->租租车ERC; 9->易途8；
     */
    private Integer source;

    /**
     * 1->普通订单；2->闪租；3->无忧组
     */
    private Integer ordertype;

    /**
     * 来源订单号
     */
    private String sourceOrdercode;

    /**
     * 备注
     */
    private String note;

    /**
     * 下单人
     */
    // 修改：将 mid 字段改为 Long
    private Long mid;

    /**
     * 1->基本险；2->全额险
     */
    private Integer insurance;

    /**
     * 车辆的保险 car_stock_insurance 表的id
     */
    // 修改：将 stockInsId 字段改为 Long
    private Long stockInsId;

    /**
     * 车辆保险名称
     */
    private String stockInsTitle;

    /**
     * 1->男；2->女
     */
    private Integer sex;

    /**
     * 姓
     */
    private String surname;

    /**
     * 名
     */
    private String username;

    /**
     * 电话
     */
    private String mobile;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 护照号
     */
    private String passportnum;

    /**
     * 颜色
     */
    private String appointCarcolor;

    /**
     * 车辆四字码
     */
    private String appointCarcode;

    /**
     * 日均价（元）
     */
    private BigDecimal price;

    /**
     * 保险价格
     */
    private BigDecimal insuranceprice;

    /**
     * 总价
     */
    private BigDecimal totalprice;

    /**
     * 付款金额
     */
    private BigDecimal payprice;

    /**
     * 当地货币总金额
     */
    private BigDecimal localPayprice;

    /**
     * 付款时间
     */
    // 修改：将 paytime 字段改为 LocalDateTime
    private String paytime;

    /**
     * 1->支付宝；2->微信；3->信用卡；4->现金；5->携程；6->租租车；7->惠租车；8->租租车ERC；9->Booking;
     */
    private Integer paytype;

    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     * 押金付款方式：1->支付宝；2->微信；3->信用卡；4->现金；5->"闪租"；6->"无忧租"; 7->"芝麻信誉";
     */
    private Integer depositPaytype;

    /**
     * 订单确认时间
     */
    // 修改：将 confirmtime 字段改为 LocalDateTime
    private String confirmtime;

    /**
     * 订单确认ip
     */
    private String confirmip;

    /**
     * 订单确认人
     */
    // 修改：将 confirmmid 字段改为 Long
    private Long confirmmid;

    /**
     * 航班号
     */
    private String contactline;

    /**
     * 确认码
     */
    private String confirmcode;

    /**
     * -3->拒单;-2->删除；-1->已取消；1->待付款；2->待确认；3->已确认,待取车；4->已取车,待还车；5->已还车，保养中；6->完成
     */
    private Integer status;

    /**
     * 取车时间
     */
    // 修改：将 getcartime 字段改为 LocalDateTime
    private String getcartime;

    /**
     * 点击确认取车时间
     */
    // 修改：将 realGetcartime 字段改为 LocalDateTime
    private String realGetcartime;

    /**
     * 确认取车人
     */
    // 修改：将 getcarmid 字段改为 Long
    private Long getcarmid;

    /**
     * 实际取车#__car_list 的id
     */
    // 修改：将 carid 字段改为 Long
    private Long carid;

    /**
     * 实际取车 #__car_stock的id
     */
    // 修改：将 stockid 字段改为 Long
    private Long stockid;

    /**
     * 实际取车 车辆名称
     */
    private String carname;

    /**
     * 实际取车  车辆图片
     */
    private String carimg;

    /**
     * 实际取车 车辆四字码
     */
    private String carcode;

    /**
     * 实际取车 车辆颜色
     */
    private String carcolor;

    /**
     * 实际取车 车牌号
     */
    private String platenumber;

    /**
     * 还车时间
     */
    // 修改：将 returntime 字段改为 LocalDateTime
    private String returntime;

    /**
     * 点击还车时间
     */
    // 修改：将 realReturntime 字段改为 LocalDateTime
    private String realReturntime;

    /**
     * 还车确认人
     */
    // 修改：将 returnmid 字段改为 Long
    private Long returnmid;

    /**
     * 还车门店
     */
    // 修改：将 returnstoreid 字段改为 Long
    private Long returnstoreid;

    /**
     * 还车门店名称
     */
    private String returnstorename;

    /**
     * 车辆入库时间
     */
    // 修改：将 orderEndtime 字段改为 LocalDateTime
    private String orderEndtime;

    /**
     * 确认车辆入库人
     */
    // 修改：将 orderEndmid 字段改为 Long
    private Long orderEndmid;

    /**
     * 本次行驶里程
     */
    private BigDecimal mileage;

    /**
     * 应退押金
     */
    private BigDecimal returnDeposit;

    /**
     * 1->待退；2->已退
     */
    private Integer returnDepositStatus;

    /**
     * 押金退还时间
     */
    // 修改：将 returnDepositTime 字段改为 LocalDateTime
    private String returnDepositTime;

    /**
     * 退款人
     */
    // 修改：将 returnDepositMid 字段改为 Long
    private Long returnDepositMid;

    /**
     * 订单取消时间
     */
    // 修改：将 canceltime 字段改为 LocalDateTime
    private String canceltime;

    /**
     * 取消人的mid
     */
    // 修改：将 cancelmid 字段改为 Long
    private Long cancelmid;

    /**
     * 订单取消应扣金额：1.取车（当地时间）前48小时可免费取消； 2、48小时内取消，将从车款中扣除2日租金
     */
    private BigDecimal cancelprice;

    /**
     * 额外费用
     */
    private BigDecimal extraprice;

    /**
     * 额外备注
     */
    private String extranote;

    /**
     * 1->普通单 2->芝麻单
     */
    private Integer sesame;

    /**
     * 芝麻信誉分数
     */
    private Integer grade;

    /**
     * 其他费用
     */
    private BigDecimal otherprice;

    /**
     * 其他费用备注
     */
    private String othernote;

    /**
     * 驾照图片
     */
    private String licensePic;

    /**
     * 工作人员确认取消状态码  0->代表未确认  1->已确认
     */
    private Integer canceledstatus;

    /**
     * 确认取消人
     */
    // 修改：将 canceledmid 字段改为 Long
    private Long canceledmid;

    /**
     * 折扣价当地货币
     */
    private BigDecimal discountprice;

    /**
     * 记录每笔订单单日里程限制
     */
    private BigDecimal daymileage;

    /**
     * 近日任务备注
     */
    private String morenote;

    /**
     * 拒单时间
     */
    // 修改：将 refusetime 字段改为 LocalDateTime
    private String refusetime;

    /**
     * 拒单人的ID
     */
    // 修改：将 refusemid 字段改为 Long
    private Long refusemid;

    /**
     * 儿童座椅数量
     */
    private String childseat;

    /**
     * 儿童座椅收取费用
     */
    private BigDecimal childseatprice;

    /**
     * 是否预付儿童座椅 1->是；0->否
     */
    private Boolean childseatPrepay;

    /**
     * 正常营业可加班费用
     */
    private BigDecimal overtimecost;

    /**
     * 是否预付加班费用 1->是；0->否
     */
    private Boolean overtimePrepay;

    /**
     * 异地还车费
     */
    private BigDecimal carreturnPrice;

    /**
     * 携程回传价格
     */
    private BigDecimal ctripprice;

    /**
     * 区分无忧租下面的全险和非权限0->非全险   1->全险
     */
    private Integer ordertypes;

    /**
     * 租租车会员优惠码
     */
    private String discountCode;

    /**
     * 信誉卡卡号
     */
    private String cardnumber;

    /**
     * 信誉卡的有效年限
     */
    private String termofvalidity;

    /**
     * cvv码
     */
    private Integer cvvnumbber;

    /**
     * 下单时记录的汇率
     */
    private Double exchangeRate;

    /**
     * 租租车会员对应的日租金
     */
    private BigDecimal discountDayprice;

    /**
     * 货币单位
     */
    private String unit;

    /**
     * 是否是早鸟套餐 0->否；1->是
     */
    private Boolean isRate;

    /**
     * 早鸟套餐码
     */
    private String rateCode;

    /**
     * 排班表id
     */
    private Long orderRangeId;

    /**
     * 创建类型，0-手动，1-api，2-导入
     */
    private Integer createType;

    /**
     * create_time in UTC
     */
    private String createTime;

    private BigDecimal settlementAmount;

    private String settlementCurrency;
}
