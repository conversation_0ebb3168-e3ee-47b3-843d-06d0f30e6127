package com.ixtech.global.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据分页结果集response
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
public class PageResponse<T> implements Serializable {

    private static final long serialVersionUID = 8656597559014685635L;
    /**
     * 总记录数
     */
    private long total;
    /**
     * 当前页
     */
    private long current;

    /**
     * 结果集
     */
    private List<T> list = Collections.emptyList();


    public static <T> PageResponse<T> success(List<T> list, long total, long current) {
        PageResponse<T> res = new PageResponse<>();
        res.setList(list);
        res.setTotal(total);
        res.setCurrent(current);
        return res;
    }

    /**
     * 将当前 PageResponse<T> 转换为 PageResponse<U>
     *
     * @param converter 转换函数，将 T 转换为 U
     * @param <U>       目标类型
     * @return 转换后的 PageResponse<U>
     */
    public <U> PageResponse<U> convert(Function<T, U> converter) {
        if (converter == null) {
            throw new IllegalArgumentException("Converter function cannot be null");
        }

        // 转换 list
        List<U> convertedList = this.list == null || this.list.isEmpty()
                ? Collections.emptyList()
                : this.list.stream()
                .map(converter)
                .collect(Collectors.toList());

        // 创建新的 PageResponse
        PageResponse<U> result = new PageResponse<>();
        result.setTotal(this.total);
        result.setCurrent(this.current);
        result.setList(convertedList);

        return result;
    }
}
