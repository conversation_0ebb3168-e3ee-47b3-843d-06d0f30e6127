package com.ixtech.global.common.biz.rental.constant;

/**
 * 通用全局变量
 *
 * @author: JP
 * @date： 2025/3/27
 */
public class NormalConstant {

    /**
     * 供应商名
     */
    public static final String VENDOR_NAME = "IX RENTAL";
    public static final String VENDOR_CODE = "IX RENTAL";

    /**
     * KLOOK 渠道 相关的常量
     */
    public static class Klook {
        /**
         * KLOOK 供应商名
         */
        public static final String VENDOR_NAME = "JDM";
        public static final String VENDOR_CODE = "JDM";

        /**
         * KLOOK 渠道
         */
        public static final String CHANNEL = "klook";
    }

    /**
     * 货币单位，美元
     */
    public static final String CURRENCY_USD = "USD";

    /**
     * 免费距离类型，每天
     */
    public static final String PER_DAY = "per_day";

    /**
     * 免费距离类型，每天
     */
    public static final String TOTAL = "total";


    /**
     * hash长度
     */
    public static final int HASH_LENGTH = 16;

    /**
     * hash salt
     */
    public static final long HASH_SALT = 2826242220L;

    /* =============================================================================== */
    /**
     * 常用数字1~10
     */
    public static final int NUM_ZERO = 0;
    public static final long NUM_ZERO_LONG = 0L;
    public static final int NUM_ONE = 1;
    public static final int NUM_TWO = 2;
    public static final int NUM_THREE = 3;
    public static final int NUM_FOUR = 4;
    public static final int NUM_FIVE = 5;
    public static final int NUM_SIX = 6;
    public static final int NUM_SEVEN = 7;
    public static final int NUM_EIGHT = 8;
    public static final int NUM_NINE = 9;
    public static final int NUM_TEN = 10;


    /**
     * 每日小时数
     */
    public static final int NUM_TWENTY_FOUR = 24;

    /**
     * 每日分钟数:24*60
     */
    public static final int ONE_DAY_MINUTES = 1440;

    /**
     * 2小时分钟数:120
     */
    public static final int TWO_HOURS_MINUTES = 120;

    /**
     * 提前预定小时数18H
     */
    public static final int NUM_EIGHTEEN = 18;

    /**
     * 提前预定小时数24H
     */
    public static final int NUM_TWENTY_FOUR_HOURS = 24;

    /* =============================================================================== */

    /**
     * 特殊字符
     */
    public static final String REGEX_ONE = ":";
    public static final String REGEX_TWO = ",";
    public static final String REGEX_THREE = "|";
    public static final String REGEX_FOUR = "-";

    /**
     * 确认时长
     */
    public static final int CONFIRM_HOURS = 24;
}
