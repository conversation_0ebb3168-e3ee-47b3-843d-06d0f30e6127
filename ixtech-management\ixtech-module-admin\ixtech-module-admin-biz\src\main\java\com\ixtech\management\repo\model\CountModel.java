package com.ixtech.management.repo.model;

import lombok.Data;

import java.util.function.IntConsumer;

/**
 * 目标对象的总共/在线数量 Model
 *
 * <AUTHOR> hu
 * @date 2025/4/8 09:59
 */
@Data
public class CountModel {

    /**
     * 目标id（供应商id、门店id、...）
     */
    private Long targetId;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 在线数量
     */
    private Integer activeCount;

    /**
     * 数量填充
     *
     * @param countModel
     * @param setTotalCount  设置总数量的方法
     * @param setActiveCount 设置在线数量的方法
     */
    public static void setCountFields(CountModel countModel, IntConsumer setTotalCount, IntConsumer setActiveCount) {
        if (countModel != null) {
            setTotalCount.accept(countModel.getTotalCount());
            setActiveCount.accept(countModel.getActiveCount());
        } else {
            setTotalCount.accept(0);
            setActiveCount.accept(0);
        }
    }

}
