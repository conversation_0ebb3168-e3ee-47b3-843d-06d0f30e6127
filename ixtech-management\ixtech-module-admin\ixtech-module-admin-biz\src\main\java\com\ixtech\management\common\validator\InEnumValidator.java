package com.ixtech.management.common.validator;

import com.ixtech.management.common.annotation.InEnum;
import com.ixtech.management.common.enums.BaseEnum;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义参数校验枚举，用于校验参数是否在枚举中
 *
 * <AUTHOR> hu
 * @date 2025/4/4 18:16
 */
public class InEnumValidator implements ConstraintValidator<InEnum, Object> {
    private Class<? extends BaseEnum<?>> enumClass;
    private List<Object> enumValues;

    @Override
    public void initialize(InEnum annotation) {
        enumClass = annotation.enumClass();
        enumValues = Arrays.stream(enumClass.getEnumConstants())
                .map(BaseEnum::getCode)
                .collect(Collectors.toList());
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {

        // 为空时不校验
        if (value == null) {
            return true;
        }

        // 校验通过
        if (enumValues.contains(value)) {
            return true;
        }

        return false;
    }
}
