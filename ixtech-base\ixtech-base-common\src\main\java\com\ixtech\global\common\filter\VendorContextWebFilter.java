package com.ixtech.global.common.filter;

import com.ixtech.global.common.context.VendorContextHolder;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 多租户 Context Web 过滤器
 */
public class VendorContextWebFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        // 设置
        Long tenantId = parseHeaderId(request, VendorContextHolder.VENDOR_ID_HEADER);
        if (tenantId != null) {
            VendorContextHolder.setVendorId(tenantId);
        }
        Long userId = parseHeaderId(request, VendorContextHolder.USER_ID_HEADER);
        if (userId != null) {
            VendorContextHolder.setUserId(userId);
        }
        try {
            chain.doFilter(request, response);
        } finally {
            // 清理
            VendorContextHolder.clear();
        }
    }

    public Long parseHeaderId(HttpServletRequest request, String headerKey) {
        String value = request.getHeader(headerKey);
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        return Long.valueOf(value);
    }

}
