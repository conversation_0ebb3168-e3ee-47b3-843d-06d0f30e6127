package com.ixtech.global.common.exception.handler;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.exception.ApiException;
import com.ixtech.global.common.exception.ServerException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

import static com.ixtech.global.common.exception.enums.CommonErrorCode.CLIENT_ERROR;


/**
 * 全局异常处理器，将 Exception 翻译成 ApiResponse + 对应的异常编号
 */
@RestControllerAdvice(name = "serverGlobalExceptionHandler")
@AllArgsConstructor
@Slf4j
@Order(100)
public class ServerExceptionHandler {

    /**
     * 处理 SpringMVC 参数校验不正确
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse<?> constraintViolationExceptionHandler(ConstraintViolationException ex) {
        log.warn("[constraintViolationExceptionHandler]", ex);
        String message = ex.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        return ApiResponse.fail(CLIENT_ERROR, String.format("请求参数不正确:%s", message));
    }

    /**
     * 处理 SpringMVC 参数校验不正确
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<?> methodArgumentNotValidExceptionExceptionHandler(MethodArgumentNotValidException ex) {
        log.warn("[methodArgumentNotValidExceptionExceptionHandler]", ex);
        FieldError fieldError = ex.getBindingResult().getFieldError();
        assert fieldError != null; // 断言，避免告警
        return ApiResponse.fail(CLIENT_ERROR, String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
    }

    /**
     * 处理业务异常 ServerException
     * <p>
     * 例如说，商品库存不足，用户手机号已存在。
     */
    @ExceptionHandler(value = ServerException.class)
    public ApiResponse<?> serviceExceptionHandler(ServerException ex) {
        log.info("[serverExceptionHandler ServerException: ]", ex);
        // 构建支持错误文案国际化的接口返回对象
        return ApiResponse.failWithI18n(ex.getErrorCode(), ex.getMessage(), ex.getResourceKey());
    }

    /**
     * 处理内部Api调用异常
     *
     * @param ax
     * @date 2025/6/5
     */
    @ExceptionHandler(value = ApiException.class)
    public ApiResponse<?> serviceExceptionHandler(ApiException ax) {
        log.info("[serverExceptionHandler ApiException: ]", ax);
        return ApiResponse.fail(ax.getErrorCode(), ax.getErrorMsg());
    }
}
