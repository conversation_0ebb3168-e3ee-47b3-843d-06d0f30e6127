package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 接送机消息发送类型
 *
 * <AUTHOR> hu
 * @date 2025/7/31 09:16
 */
@Getter
public enum TransferMessageSendTypeEnum {

    WECHAT(1, "微信"),
    ;

    TransferMessageSendTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 消息发送类型
     */
    private final Integer code;

    /**
     * 消息发送类型描述
     */
    private final String value;

    public static TransferMessageSendTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferMessageSendTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
