package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.JipinzucheCarStockInsurance;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【jipinzuche_car_stock_insurance】的数据库操作Mapper
* @createDate 2025-04-21 13:28:25
* @Entity generator.domain.JipinzucheCarStockInsurance
*/
@DS("ix")
@Mapper
public interface JipinzucheCarStockInsuranceMapper {

    List<JipinzucheCarStockInsurance> selectByStockId(Integer id);

    JipinzucheCarStockInsurance selectById(Long id);
}




