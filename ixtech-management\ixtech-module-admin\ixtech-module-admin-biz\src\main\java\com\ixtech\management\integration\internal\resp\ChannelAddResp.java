package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 渠道新增响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:32
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChannelAddResp {

    /**
     * 渠道id
     */
    private Long id;

}
