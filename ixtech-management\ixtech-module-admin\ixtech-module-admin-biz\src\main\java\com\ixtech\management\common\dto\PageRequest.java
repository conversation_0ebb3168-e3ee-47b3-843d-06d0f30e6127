package com.ixtech.management.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 数据分页查询resquest
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PageRequest implements Serializable {

    private static final long serialVersionUID = -4381075801048303456L;
    //标准排序：升序
    public static final String ASC = "ASC";
    //标准排序：降序
    public static final String DESC = "DESC";
    //标准排序：添加时间
    public static final String ADD_TIME = "add_time";
    //标准排序：更新时间
    public static final String UPDATE_TIME = "update_time";

    /**
     * 页码从1开始，如果需要分页查询，请设置页码，否则不进行分页查询
     */
    private int pageNum =1;

    /**
     * 每页数量
     */
    private int pageSize = 10;

    /**
     * 排序字段名
     */
    private String orderBy;

    private String orderDirection = DESC;

    private String groupBy;

    /**
     * 是否需要总条数
     */
    private boolean needTotalCount = true;

    /**
     * 是否配置了分页查询
     */
    public boolean isPageQuery() {
        return pageNum > 0;
    }
}
