package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 订单日志操作类型枚举
 * 对应表: rental_order_log, 字段: operation_type
 * (1-创建订单，2-确认订单，3-分配车辆，4-确认提车，5-确认还车，6-用户取消订单，7-拒单，8-商户取消订单)
 */
@Getter
@AllArgsConstructor
public enum OrderLogOperationTypeEnum implements DictInf {

    CREATE_ORDER(1, "创建订单"),
    CONFIRM_ORDER(2, "确认订单"),
    ASSIGN_VEHICLE(3, "分配车辆"),
    CONFIRM_PICKUP(4, "确认提车"),
    CONFIRM_DROPOFF(5, "确认还车"),
    CUSTOMER_CANCEL(6, "用户取消订单"),
    REJECT_ORDER(7, "拒单"),
    MERCHANT_CANCEL(8, "商户取消订单"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static OrderLogOperationTypeEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
