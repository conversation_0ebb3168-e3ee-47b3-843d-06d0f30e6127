package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.ixtech.management.common.annotation.InEnum;
import com.ixtech.management.common.enums.SettlementCurrencyEnum;
import com.ixtech.management.common.enums.SettlementModeEnum;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商更新请求Req
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:52
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VendorUpdateReq {

    /**
     * 供应商id
     */
    @NotNull(message = "供应商id不能为空")
    private Long id;

    /**
     * 供应商简称
     */
    private String name;

    /**
     * 公司全称
     */
    private String companyFullName;

    /**
     * 供应商编码
     */
    private String code;

    /**
     * 负责人
     */
    private String principal;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 电话区号
     */
    private String contactNumberCode;

    /**
     * 报价货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    @InEnum(enumClass = SettlementCurrencyEnum.class, message = "不支持的报价货币")
    private String quoteCurrency;

    /**
     * 结算货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    @InEnum(enumClass = SettlementCurrencyEnum.class, message = "不支持的结算货币")
    private String settlementCurrency;

    /**
     * 结算模式 1：底价模式 2：抽佣模式
     */
    @InEnum(enumClass = SettlementModeEnum.class, message = "不支持的结算模式")
    private Byte settlementMode;

    /**
     * 比例x%
     */
    @Digits(integer = 2, fraction = 1, message = "比例设置错误，请设置（0，100）范围内的数值，最多1位小数")
    @DecimalMin(value = "0", inclusive = false, message = "比例设置错误，请设置（0，100）范围内的数值，最多1位小数")
    @DecimalMax(value = "100", inclusive = false, message = "比例设置错误，请设置（0，100）范围内的数值，最多1位小数")
    private BigDecimal rate;

}
