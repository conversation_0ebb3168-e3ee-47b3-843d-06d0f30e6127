package com.ixtech.global.config;

import com.ixtech.global.FeignChannelContextInterceptor;
import com.ixtech.global.FeignInterceptor;
import com.ixtech.global.common.context.ChannelContextHolder;
import com.ixtech.global.common.context.VendorContextHolder;
import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign 配置类
 *
 * @author: JP
 * @date： 2025/4/18
 */
@Configuration
public class FeignConfig {

    @Bean
    public FeignInterceptor feignInterceptor() {
        return new FeignInterceptor();
    }

    @Bean
    public RequestInterceptor requestInterceptor() {
        return new FeignChannelContextInterceptor();
    }

}
