package com.ixtech.global.common.filter;

import com.ixtech.global.common.context.ChannelContextHolder;
import com.ixtech.global.common.context.VendorContextHolder;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 渠道 Context Web 过滤器
 */
public class ChannelContextWebFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        // 设置 Channel ID
        Long id = parseHeaderId(request, ChannelContextHolder.CHANNEL_ID_HEADER);
        if (id != null) {
            ChannelContextHolder.setChannelId(id);
        }

        // 步骤 1 & 2: 获取并设置 Channel Code
        String channelCode = request.getHeader(ChannelContextHolder.CHANNEL_CODE_HEADER);
        if (!ObjectUtils.isEmpty(channelCode)) {
            ChannelContextHolder.setChannelCode(channelCode);
        }

        try {
            chain.doFilter(request, response);
        } finally {
            // 清理
            ChannelContextHolder.clear();
        }
    }

    public Long parseHeaderId(HttpServletRequest request, String headerKey) {
        String value = request.getHeader(headerKey);
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        // 注意：这里假设 Channel-Id 是 Long 类型，如果 Channel-Code 是 String，则不需要 parseHeaderId 方法
        // 对于 Channel-Code，直接获取 String 值即可
        return Long.valueOf(value);
    }

}