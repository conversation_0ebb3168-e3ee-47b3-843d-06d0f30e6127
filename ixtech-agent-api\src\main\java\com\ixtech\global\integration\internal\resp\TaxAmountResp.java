package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 税费金额 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TaxAmountResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 金额
  */
 private AmountResp amount;

 /**
  * 税码
  */
 private String taxCode;

 /**
  * 是否含税
  */
 private Boolean taxInclusive;
}
