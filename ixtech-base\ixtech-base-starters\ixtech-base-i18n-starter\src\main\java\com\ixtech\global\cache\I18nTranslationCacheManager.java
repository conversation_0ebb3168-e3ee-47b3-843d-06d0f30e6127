package com.ixtech.global.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.ixtech.global.config.I18nProperties;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 翻译本地缓存管理器
 *
 * <AUTHOR> hu
 * @date 2025/6/10 11:58
 */
@Slf4j
public class I18nTranslationCacheManager {

    /**
     * 本地缓存，用于缓存翻译结果
     */
    private Cache<String, String> cache;

    private I18nProperties i18nProperties;

    public I18nTranslationCacheManager(I18nProperties i18nProperties) {
        this.i18nProperties = i18nProperties;
    }

    @PostConstruct
    public void init() {
        I18nProperties.LocalCache localCache = i18nProperties.getLocalCache();
        cache = Caffeine.newBuilder()
                .maximumSize(localCache.getMaximumSize())
                .expireAfterAccess(localCache.getExpireAfterAccess(), TimeUnit.SECONDS)
                .recordStats()
                .build();
    }

    /**
     * 是否启用缓存
     *
     * @return
     */
    public boolean useCache() {
        return Boolean.TRUE.equals(i18nProperties.getLocalCache().getEnable());
    }

    /**
     * 将翻译资源key和语言代码组合成缓存key
     *
     * @param languageCode
     * @param resourceKey
     * @return
     */
    private String wrapperKey(String languageCode, String resourceKey) {
        return String.format("%s:%s", languageCode, resourceKey);
    }

    /**
     * 从缓存key中解析出翻译资源key
     *
     * @param languageCode
     * @param cacheKey
     * @return
     */
    private String unWrapperKey(String languageCode, String cacheKey) {
        if (StringUtils.isBlank(cacheKey)) {
            return cacheKey;
        }
        String prefix = String.format("%s:", languageCode);
        if (cacheKey.startsWith(prefix)) {
            return cacheKey.substring(prefix.length());
        }
        return cacheKey;
    }

    /**
     * 批量获取指定语言的翻译资源缓存值
     *
     * @param resourceKeys 翻译资源key
     * @param languageCode 语言code
     * @return
     */
    public Map<String, String> get(Collection<String> resourceKeys, String languageCode) {

        Map<String, String> result = new HashMap<>();

        // 是否启用本地缓存
        if (!useCache()) {
            return result;
        }

        if (CollectionUtils.isEmpty(resourceKeys) || StringUtils.isBlank(languageCode)) {
            return result;
        }

        // 批量获取缓存值
        Map<String, String> cachedValues = cache.getAllPresent(
                resourceKeys.stream().map(resourceKey -> wrapperKey(languageCode, resourceKey))
                        .collect(Collectors.toSet())
        );
        if (MapUtils.isNotEmpty((cachedValues))) {
            cachedValues.forEach((cacheKey, value) -> {
                // 去掉语言代码前缀
                String resourceKey = unWrapperKey(languageCode, cacheKey);
                result.put(resourceKey, value);
            });
        }

        return result;
    }

    /**
     * 获取指定语言的翻译资源缓存值
     *
     * @param resourceKey  翻译资源key
     * @param languageCode 语言code
     * @return
     */
    public String get(String resourceKey, String languageCode) {

        // 是否启用本地缓存
        if (!useCache()) {
            return null;
        }

        if (StringUtils.isBlank(resourceKey) || StringUtils.isBlank(languageCode)) {
            return null;
        }

        return cache.getIfPresent(wrapperKey(languageCode, resourceKey));
    }

    /**
     * 批量设置缓存
     *
     * @param resourceKeyTranslationMap key为翻译资源key，value为对应的翻译内容
     * @param languageCode              语言code
     */
    public void put(Map<String, String> resourceKeyTranslationMap, String languageCode) {

        // 是否启用本地缓存
        if (!useCache()) {
            return;
        }

        if (MapUtils.isEmpty(resourceKeyTranslationMap) || StringUtils.isBlank(languageCode)) {
            return;
        }

        // 转换key并放入缓存
        cache.putAll(
                resourceKeyTranslationMap.entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        e -> wrapperKey(languageCode, e.getKey()),
                                        Map.Entry::getValue
                                )
                        )
        );
    }

    /**
     * 批量移除缓存
     *
     * @param resourceKeys 翻译资源key
     * @param languageCode 语言code
     */
    public void remove(Collection<String> resourceKeys, String languageCode) {

        if (CollectionUtils.isEmpty(resourceKeys) || StringUtils.isBlank(languageCode)) {
            return;
        }
        cache.invalidateAll(
                resourceKeys.stream().map(resourceKey -> wrapperKey(languageCode, resourceKey))
                        .collect(Collectors.toSet())
        );

    }

    /**
     * 清空缓存
     */
    public void clear() {
        cache.invalidateAll();
    }

    /**
     * 获取缓存统计信息
     */
    public String getStats() {
        return cache.stats().toString();
    }

}