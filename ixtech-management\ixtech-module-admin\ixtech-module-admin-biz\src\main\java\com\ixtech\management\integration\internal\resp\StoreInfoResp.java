package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 门店详情响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:55
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StoreInfoResp {

    /**
     * 门店id
     */
    private Long id;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 门店类型 1：城市 2：机场 3：酒店
     */
    private Integer type;

    /**
     * 门店类型（字符串描述）
     */
    private String typeStr;

    /**
     * 负责人
     */
    private String principal;

    /**
     * 负责人电话
     */
    private String principalMobile;

    /**
     * 电话区号
     */
    private String mobileAreaCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 纬度
     */
    private BigDecimal lat;

    /**
     * 经度
     */
    private BigDecimal lon;

    /**
     * 上下线状态 true：上线 false：下线
     */
    private Boolean active;

    /**
     * 录入车辆数
     */
    private Integer carCount;

    /**
     * 在线车辆数
     */
    private Integer activeCarCount;

    /**
     * 覆盖车辆型号
     */
    private List<String> carModels;

}
