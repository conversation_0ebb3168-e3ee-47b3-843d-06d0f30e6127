<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.JipinzucheCarStockInsuranceMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.JipinzucheCarStockInsurance">
        <id property="id" column="id" />
        <result property="active" column="active" />
        <result property="deleted" column="deleted" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="stockid" column="stockid" />
        <result property="insid" column="insId" />
        <result property="info" column="info" />
        <result property="price" column="price" />
        <result property="ip" column="ip" />
        <result property="mid" column="mid" />
        <result property="lastedittime" column="lastedittime" />
        <result property="lasteditmid" column="lasteditmid" />
        <result property="lasteditip" column="lasteditip" />
        <result property="isDelete" column="is_delete" />
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,stockid,
        insId,info,price,ip,mid,
        lastedittime,lasteditmid,lasteditip,is_delete
    </sql>
    <sql id="Base_delete">
        and active=1 and deleted = 0 and is_delete = 0
    </sql>
    <select id="selectByStockId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_stock_insurance
        where stockid = #{id}
        <include refid="Base_delete" />
    </select>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_stock_insurance
        where id = #{id}
        <include refid="Base_delete" />
    </select>
</mapper>
