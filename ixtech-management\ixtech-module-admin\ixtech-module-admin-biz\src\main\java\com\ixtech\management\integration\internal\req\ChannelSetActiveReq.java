package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 渠道上下线请求Req
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:40
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChannelSetActiveReq {

    /**
     * 渠道id
     */
    @NotNull(message = "渠道id不能为空")
    private Long id;

    /**
     * 上下线状态 true：上线 false：下线
     */
    @NotNull(message = "上下线状态不能为空")
    private Boolean active;

}
