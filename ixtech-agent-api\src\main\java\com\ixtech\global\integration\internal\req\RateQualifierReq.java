package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.io.Serializable;

/**
 * 费率 req
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RateQualifierReq implements Serializable {

 private static final long serialVersionUID = 7985504938627085943L;

 /**
  * 合同代码
  */
 private String contractCode;

 /**
  * 促销代码
  */
 private String promotionCode;

 /**
  * 费率类别
  */
 private String rateCategory;

 /**
  * 费率限定符
  */
 private String rateQualifier;

 /**
  * 公司折扣编号
  */
 private String corpDiscountNmbr;
}
