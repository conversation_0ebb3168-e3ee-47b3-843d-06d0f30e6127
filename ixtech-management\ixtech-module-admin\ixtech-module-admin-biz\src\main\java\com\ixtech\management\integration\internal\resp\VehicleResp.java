package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 车辆信息对象，包含车辆代码和品牌型号。
 */
@Data
public class VehicleResp {

    /**
     * 车辆代码。
     */
    @JsonProperty("code")
    private String code;

    /**
     * 车辆品牌名称
     */
    @JsonProperty("brand_name")
    private String brandName;

    /**
     * 车辆型号名称
     */
    @JsonProperty("model_name")
    private String modelName;
}
