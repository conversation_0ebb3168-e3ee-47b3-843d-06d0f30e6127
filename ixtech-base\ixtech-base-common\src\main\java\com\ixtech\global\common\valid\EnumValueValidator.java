package com.ixtech.global.common.valid;

import com.ixtech.global.common.annotation.ValidEnum;
import com.ixtech.global.common.enums.inf.DictInf;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.ArrayList;
import java.util.List;

public class EnumValueValidator implements ConstraintValidator<ValidEnum, Object> {
    private Class<?> enumClass;
    private boolean nullable;
    private String message;

    @Override
    public void initialize(ValidEnum constraintAnnotation) {
        this.enumClass = constraintAnnotation.enumClass();
        this.nullable = constraintAnnotation.nullable();
        this.message = constraintAnnotation.message(); // 获取自定义错误消息
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        // 禁用默认错误消息
        context.disableDefaultConstraintViolation();
        List<String> errors = new ArrayList<>();

        // 处理空值
        if (value == null) {
            if (!nullable) {
                errors.add("值不能为空");
            } else {
                return true;
            }
        }

        // 获取枚举常量
        DictInf[] enumConstants = null;
        if (errors.isEmpty()) { // 仅在没有空值错误时获取枚举常量
            try {
                enumConstants = enumClass.asSubclass(Enum.class).asSubclass(DictInf.class).getEnumConstants();
            } catch (ClassCastException e) {
                errors.add("无效的枚举类: " + enumClass.getName());
            }
        }

        // 如果 value 是 List 类型
        if (errors.isEmpty() && value instanceof List<?> list) {
            if (list.isEmpty() && !nullable) {
                errors.add("列表不能为空");
            }
            // 验证列表中的每个元素
            for (int i = 0; i < list.size(); i++) {
                Object item = list.get(i);
                if (item == null) {
                    if (!nullable) {
                        errors.add("列表中索引 " + i + " 的元素不能为空");
                    }
                    continue;
                }
                try {
                    DictInf.fromCode(enumConstants, item);
                } catch (IllegalArgumentException e) {
                    errors.add("列表中索引 " + i + " 的元素无效: " + item);
                }
            }
        } else if (errors.isEmpty()) { // 处理单个值
            try {
                DictInf.fromCode(enumConstants, value);
            } catch (IllegalArgumentException e) {
                errors.add("无效的枚举值: " + value);
            }
        }

        // 如果有错误，添加到校验上下文
        if (!errors.isEmpty()) {
            for (String error : errors) {
                context.buildConstraintViolationWithTemplate(message + ": " + error)
                        .addConstraintViolation();
            }
            return false;
        }

        return true;
    }
}
