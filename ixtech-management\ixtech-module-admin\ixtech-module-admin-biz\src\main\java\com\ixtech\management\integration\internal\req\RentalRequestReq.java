package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 租车请求对象，包含租车相关的所有信息。
 */
@Data
public class RentalRequestReq {

    /**
     * 客户信息，必填
     */
    @NotNull(message = "客户信息不能为空")
    @Valid
    @JsonProperty("customer")
    private CustomerReq customerReq; // 客户信息对象

    /**
     * 预估总金额，必填
     */
    @NotNull(message = "预估总金额不能为空")
    @Valid
    @JsonProperty("estimated_total_amount")
    private EstimatedTotalAmountReq estimatedTotalAmountReq; // 预估总金额对象

    /**
     * 费率限定信息，可选
     */
    @Valid
    @JsonProperty("rate_qualifier")
    private RateQualifierReq rateQualifierReq; // 费率限定对象

    /**
     * 取车时间，格式：yyyy-MM-dd HH:mm:ss，必填
     */
    @NotNull(message = "取车时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("pick_up_date_time")
    private LocalDateTime pickUpDateTime; // 取车时间

    /**
     * 还车时间，格式：yyyy-MM-dd HH:mm:ss，必填
     */
    @NotNull(message = "还车时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("return_date_time")
    private LocalDateTime returnDateTime; // 还车时间

    /**
     * 取车地点信息，必填
     */
    @NotNull(message = "取车地点信息不能为空")
    @Valid
    @JsonProperty("pickup_location")
    private LocationReq pickupLocationReq; // 取车地点对象

    /**
     * 还车地点信息，必填
     */
    @NotNull(message = "还车地点信息不能为空")
    @Valid
    @JsonProperty("return_location")
    private LocationReq returnLocationReq; // 还车地点对象

    /**
     * 供应商代码（如 "ZI"），必填
     */
    @NotBlank(message = "供应商代码不能为空")
    @JsonProperty("vendor_pref_code")
    private String vendorPrefCode; // 供应商偏好代码

    /**
     * 供应商名称（如 "xxxx"），必填
     */
    @NotBlank(message = "供应商名称不能为空")
    @JsonProperty("vendor_pref_name")
    private String vendorPrefName; // 供应商偏好名称

    /**
     * 车辆代码（如 "ICAR"），必填
     */
    @NotBlank(message = "车辆代码不能为空")
    @JsonProperty("veh_pref_code")
    private String vehPrefCode; // 车辆偏好代码

    // /**
    //  * 车辆具体型号代码，必填
    //  */
    // @NotBlank(message = "车辆具体型号代码 不能为空")
    // @JsonProperty("vehicle_model_name")
    // private String vehicleModelName; // 车辆型号名称

    /**
     * 特殊设备偏好列表，可选
     */
    @Valid
    @JsonProperty("special_equip_pref")
    private List<SpecialEquipPrefReq> specialEquipPrefReqs; // 特殊设备偏好列表

    /**
     * 到达详情，可选
     */
    @Valid
    @JsonProperty("arrival_detail")
    private ArrivalDetailReq arrivalDetailReq; // 到达详情对象

    /**
     * 唯一标识符（如 "8EGUKD04SW29853-6303"），可选
     */
    @NotBlank(message = "唯一标识符 不能为空")
    @JsonProperty("reference_id")
    private String referenceId; // 唯一标识符

    /**
     * 订单来源（1=门店, 2=网站, 3=微信, 4=淘宝, 5=携程, 6=租租车, 7=惠租车, 8=租租车ERC, 9=易途8），必填
     */
    @NotNull(message = "订单来源不能为空")
    @JsonProperty("source")
    private Integer source; // 订单来源

    /**
     * 来源订单 ID（如 "111321321311"），必填
     */
    @NotBlank(message = "来源订单 ID 不能为空")
    @JsonProperty("source_order_id")
    private String sourceOrderId; // 来源订单ID
}
