package com.ixtech.global.repo.mapper;

import com.ixtech.global.repo.entity.IxChannelPO;
import com.ixtech.global.repo.entity.IxChannelExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface IxChannelMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    long countByExample(IxChannelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    int deleteByExample(IxChannelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    int insert(IxChannelPO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    int insertSelective(IxChannelPO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    List<IxChannelPO> selectByExample(IxChannelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    IxChannelPO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") IxChannelPO record, @Param("example") IxChannelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") IxChannelPO record, @Param("example") IxChannelExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(IxChannelPO record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jipinzuche_car_order_source
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(IxChannelPO record);
}
