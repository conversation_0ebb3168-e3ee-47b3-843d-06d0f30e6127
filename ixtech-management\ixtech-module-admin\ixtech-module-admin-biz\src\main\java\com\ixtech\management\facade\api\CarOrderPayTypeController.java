package com.ixtech.management.facade.api;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.CarOrderPayTypeService;
import com.ixtech.management.domain.service.CarStockInsuranceService;
import com.ixtech.management.integration.internal.req.CarStockListReq;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 供应商管理
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:15
 */
@RestController
@RequestMapping("/v1/management/internal/paytype")
public class CarOrderPayTypeController {

    @Resource
    private CarOrderPayTypeService carOrderPayTypeService;

    /**
     * 支付方式下拉列表
     * @return
     */
    @PermitAll
    @PostMapping(value = "/dropdown_list")
    public ApiResponse<List<SelectOptionResponse<Long>>> dropdownList() {

        List<SelectOptionResponse<Long>> result = carOrderPayTypeService.dropdownList();
        return ApiResponse.success(result);
    }

}
