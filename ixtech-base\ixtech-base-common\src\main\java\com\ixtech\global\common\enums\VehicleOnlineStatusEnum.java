package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车型状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VehicleOnlineStatusEnum implements DictInf {

    /**
     * 已上线
     */
    WAITING_ONLINE("1", "已上线"),

    /**
     * 待上线
     */
    ONLINE("2", "待上线");

    /**
     * 状态值
     */
    private final String value;
    
    /**
     * 状态名称
     */
    private final String label;

    public static String getLabelByValue(String value) {
        for (VehicleOnlineStatusEnum e : values()) {
            if (String.valueOf(e.value).equals(value)) {
                return e.label;
            }
        }
        return "";
    }
}