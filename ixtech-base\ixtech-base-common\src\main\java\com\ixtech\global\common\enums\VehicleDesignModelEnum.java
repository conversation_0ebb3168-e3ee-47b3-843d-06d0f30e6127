package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否指定车型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VehicleDesignModelEnum implements DictInf {


    /**
     * 否
     */
    NO("0", "非指定车型"),
    /**
     * 是
     */
    YES("1", "指定车型");


    /**
     * 状态值
     */
    private final String value;
    
    /**
     * 状态名称
     */
    private final String label;

    public static String getLabelByValue(String value) {
        for (VehicleDesignModelEnum e : values()) {
            if (String.valueOf(e.value).equals(value)) {
                return e.label;
            }
        }
        return "";
    }
}