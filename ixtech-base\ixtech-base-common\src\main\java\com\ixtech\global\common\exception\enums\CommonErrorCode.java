package com.ixtech.global.common.exception.enums;

import com.ixtech.global.common.exception.inf.ErrorCode;

import java.util.Arrays;

/**
 * 通用错误码枚举
 * 错误码格式: 服务码(4位) + 错误类型(1位) + 具体错误码(4位)
 * 例如: 0001S0000
 */
public enum CommonErrorCode implements ErrorCode {
    // 系统错误 (S)
    SYSTEM_ERROR(ERROR_TYPE_SYSTEM, "0000", "系统错误"),
    SERVICE_UNAVAILABLE(ERROR_TYPE_SYSTEM, "0001", "服务不可用"),

    // 数据库相关异常 (归类到系统错误)
    DATABASE_ERROR(ERROR_TYPE_SYSTEM, "0100", "数据库错误"),
    DB_CONNECTION_FAILED(ERROR_TYPE_SYSTEM, "0101", "数据库连接失败"),
    DB_QUERY_TIMEOUT(ERROR_TYPE_SYSTEM, "0102", "数据库查询超时"),
    DB_DATA_INTEGRITY(ERROR_TYPE_SYSTEM, "0103", "数据完整性错误"),

    // 文件相关异常 (归类到系统错误)
    FILE_ERROR(ERROR_TYPE_SYSTEM, "0200", "文件处理错误"),
    FILE_NOT_FOUND(ERROR_TYPE_SYSTEM, "0201", "文件不存在"),
    FILE_ACCESS_DENIED(ERROR_TYPE_SYSTEM, "0202", "文件访问权限不足"),
    FILE_FORMAT_INVALID(ERROR_TYPE_SYSTEM, "0203", "文件格式无效"),

    // IO相关异常 (归类到系统错误)
    IO_ERROR(ERROR_TYPE_SYSTEM, "0300", "IO操作错误"),
    IO_READ_FAILED(ERROR_TYPE_SYSTEM, "0301", "文件读取失败"),
    IO_WRITE_FAILED(ERROR_TYPE_SYSTEM, "0302", "文件写入失败"),
    IO_STREAM_CLOSED(ERROR_TYPE_SYSTEM, "0303", "IO流已关闭"),

    // 客户端相关异常 (归类到系统错误)
    CLIENT_ERROR(ERROR_TYPE_SYSTEM, "0400", "客户端错误"),
    AUTHENTICATION_FAILED(ERROR_TYPE_SYSTEM, "0401", "认证失败"),
    TOKEN_EXPIRED(ERROR_TYPE_SYSTEM, "0402", "令牌已过期"),
    RATE_LIMIT_EXCEEDED(ERROR_TYPE_SYSTEM, "0403", "请求频率超限"),
    INVALID_REQUEST(ERROR_TYPE_SYSTEM, "0404", "无效请求"),

    // 参数错误 (P)
    PARAM_INVALID(ERROR_TYPE_PARAM, "0000", "参数无效"),
    PARAM_MISSING(ERROR_TYPE_PARAM, "0001", "缺少必要参数"),
    PARAM_FORMAT_ERROR(ERROR_TYPE_PARAM, "0002", "参数格式错误"),
    PARAM_RANGE_ERROR(ERROR_TYPE_PARAM, "0003", "参数超出范围"),
    PARAM_ENUM_ERROR(ERROR_TYPE_PARAM, "0004", "无效的参数"),

    // 业务错误 (B)
    BUSINESS_ERROR(ERROR_TYPE_BUSINESS, "0000", "业务处理错误"),
    RESOURCE_NOT_FOUND(ERROR_TYPE_BUSINESS, "0001", "资源未找到"),
    OPERATION_NOT_ALLOWED(ERROR_TYPE_BUSINESS, "0002", "操作不允许"),
    BUSINESS_RULE_VIOLATION(ERROR_TYPE_BUSINESS, "0003", "违反业务规则"),

    // 加锁失败
    LOCK_FAILED(ERROR_TYPE_BUSINESS, "0100", "加锁失败"),

    ;


    private final String specificCode;
    private final String errorType;
    private final String message;

    CommonErrorCode(String errorType, String specificCode, String message) {
        this.specificCode = specificCode;
        this.errorType = errorType;
        this.message = message;
    }

    @Override
    public String getErrorType() {
        return errorType;
    }

    @Override
    public String getSpecificCode() {
        return specificCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getFullCode() {
        return getServiceCode() + errorType + specificCode;
    }

    @Override
    public ErrorCode fromFullCode(String fullCode) {
        if (fullCode == null || fullCode.length() != 9) {
            return null;
        }
        return Arrays.stream(values())
                .filter(error -> error.getFullCode().equals(fullCode))
                .findFirst()
                .orElse(null);
    }
}
