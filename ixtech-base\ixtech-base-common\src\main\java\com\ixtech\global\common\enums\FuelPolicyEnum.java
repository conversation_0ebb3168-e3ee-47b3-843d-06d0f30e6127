package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FuelPolicyEnum implements DictInf {

    /**
     * 满油取还
     */
    FULL_TANK("1", "满油取还"),

    /**
     * 等油取还
     */
    SAME_LEVEL("2", "等油取还"),

    /**
     * 买一箱油套餐
     */
    BUY_A_TANK("3", "买一箱油套餐"),

    /**
     * 送一箱油套餐
     */
    FREE_TANK("4", "送一箱油套餐");

    private final String value;
    private final String label;
}