package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum InsuranceTypeEnum implements DictInf {

    BASIC_INSURANCE(1, "INSC0001", "基本险"),
    FULL_INSURANCE(2, "INSC0002", "全额险"),
    COMPREHENSIVE_INSURANCE(3, "INSC0003", "综合险");

    /**
     * 保险类型值（对应数据库中的insurance_type）
     */
    private final Integer code;

    /**
     * 保险code
     */
    private final String insCode;

    /**
     * 保险类型名称（前端显示用）
     */
    private final String label;

    public String getValue() {
        return String.valueOf(code);
    }

    // 静态Map，用于存储insCode到枚举实例的映射
    private static final Map<String, InsuranceTypeEnum> INS_CODE_MAP =
            Collections.unmodifiableMap(Arrays.stream(values())
                    .collect(Collectors.toMap(InsuranceTypeEnum::getInsCode, Function.identity())));
    private static final Map<Integer, InsuranceTypeEnum> CODE_MAP =
            Collections.unmodifiableMap(Arrays.stream(values())
                    .collect(Collectors.toMap(InsuranceTypeEnum::getCode, Function.identity())));

    public static InsuranceTypeEnum getEnumByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    public static InsuranceTypeEnum getEnumByInsCode(String insCode) {
        return INS_CODE_MAP.get(insCode);
    }

    public static String getLabelByCode(String code) {
        if (code == null) {
            return null;
        }
        for (InsuranceTypeEnum insuranceTypeEnum : values()) {
            if (insuranceTypeEnum.getValue().equals(code)) {
                return insuranceTypeEnum.getLabel();
            }
        }
        return null;
    }
}
