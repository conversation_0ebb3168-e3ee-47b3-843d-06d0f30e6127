<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.iocoder.boot</groupId>
    <artifactId>yudao-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>基础 bom 文件，管理整个项目的依赖版本</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <properties>
        <revision>2.4.1-SNAPSHOT</revision>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <!-- 统一依赖管理 -->
        <spring.boot.version>3.4.4</spring.boot.version>
        <!-- Web 相关 -->
        <springdoc.version>2.7.0</springdoc.version>
        <knife4j.version>4.6.0</knife4j.version>
        <!-- DB 相关 -->
        <druid.version>1.2.24</druid.version>
        <mybatis.version>3.5.17</mybatis.version>
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <mybatis-plus-join.version>1.4.13</mybatis-plus-join.version>
        <easy-trans.version>3.0.6</easy-trans.version>
        <redisson.version>3.41.0</redisson.version>
        <dm8.jdbc.version>*********</dm8.jdbc.version>
        <kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
        <opengauss.jdbc.version>5.1.0</opengauss.jdbc.version>
        <!-- 消息队列 -->
        <rocketmq-spring.version>2.3.1</rocketmq-spring.version>
        <!-- 服务保障相关 -->
        <lock4j.version>2.2.7</lock4j.version>
        <!-- 监控相关 -->
        <skywalking.version>9.0.0</skywalking.version>
        <spring-boot-admin.version>3.4.1</spring-boot-admin.version>
        <opentracing.version>0.33.0</opentracing.version>
        <!-- Test 测试相关 -->
        <podam.version>8.0.2.RELEASE</podam.version>
        <jedis-mock.version>1.1.8</jedis-mock.version>
        <mockito-inline.version>5.2.0</mockito-inline.version>
        <!-- Bpm 工作流相关 -->
        <flowable.version>7.0.1</flowable.version>
        <!-- 工具类相关 -->
        <captcha-plus.version>2.0.3</captcha-plus.version>
        <jsoup.version>1.18.3</jsoup.version>
        <lombok.version>1.18.36</lombok.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <hutool-5.version>5.8.35</hutool-5.version>
        <hutool-6.version>6.0.0-M19</hutool-6.version>
        <easyexcel.verion>4.0.3</easyexcel.verion>
        <velocity.version>2.4.1</velocity.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>33.4.0-jre</guava.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <commons-net.version>3.11.1</commons-net.version>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.9.2</tika-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
        <netty.version>4.1.116.Final</netty.version>
        <mqtt.version>1.2.5</mqtt.version>
        <!-- 三方云服务相关 -->
        <commons-io.version>2.17.0</commons-io.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <aws-java-sdk-s3.version>1.12.777</aws-java-sdk-s3.version>
        <justauth.version>2.0.5</justauth.version>
        <jimureport.version>1.8.1</jimureport.version>
        <weixin-java.version>4.6.0</weixin-java.version>
        <openfeign.version>4.2.0</openfeign.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 统一依赖管理 -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 业务组件 -->
            <dependency>
                <groupId>io.github.mouzt</groupId>
                <artifactId>bizlog-sdk</artifactId>
                <version>${bizlog-sdk.version}</version>
                <exclusions>
                    <exclusion> <!-- 排除掉springboot依赖使用项目的 -->
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-biz-data-permission</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-biz-ip</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- Spring 核心 -->
            <dependency>
                <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!-- Web 相关 -->
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xingfudeshi</groupId> <!-- TODO 芋艿：https://github.com/xiaoymin/knife4j/issues/874 -->
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- DB 相关 -->
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId> <!-- 多数据源 -->
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId> <!-- MyBatis 联表查询 -->
                <version>${mybatis-plus-join.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fhs-opensource</groupId> <!-- VO 数据翻译 -->
                <artifactId>easy-trans-spring-boot-starter</artifactId>
                <version>${easy-trans.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-commons</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-mybatis-plus-extend</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-anno</artifactId>
                <version>${easy-trans.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm8.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.opengauss</groupId>
                <artifactId>opengauss-jdbc</artifactId>
                <version>${opengauss.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.kingbase</groupId>
                <artifactId>kingbase8</artifactId>
                <version>${kingbase.jdbc.version}</version>
            </dependency>

            <!-- Job 定时任务相关 -->
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 消息队列相关 -->
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-mq</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring.version}</version>
            </dependency>

            <!-- 服务保障相关 -->
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-protection</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>redisson-spring-boot-starter</artifactId>
                        <groupId>org.redisson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 监控相关 -->
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-monitor</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-opentracing</artifactId>
                <version>${skywalking.version}</version>
                <!--                <exclusions>-->
                <!--                    <exclusion>-->
                <!--                        <artifactId>opentracing-api</artifactId>-->
                <!--                        <groupId>io.opentracing</groupId>-->
                <!--                    </exclusion>-->
                <!--                    <exclusion>-->
                <!--                        <artifactId>opentracing-util</artifactId>-->
                <!--                        <groupId>io.opentracing</groupId>-->
                <!--                    </exclusion>-->
                <!--                </exclusions>-->
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-noop</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>de.codecentric</groupId>
                        <artifactId>spring-boot-admin-server-cloud</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version> <!-- 支持 Mockito 的 final 类与 static 方法的 mock -->
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.fppt</groupId> <!-- 单元测试，我们采用内嵌的 Redis 数据库 -->
                <artifactId>jedis-mock</artifactId>
                <version>${jedis-mock.version}</version>
            </dependency>

            <dependency>
                <groupId>uk.co.jemos.podam</groupId> <!-- 单元测试，随机生成 POJO 类 -->
                <artifactId>podam</artifactId>
                <version>${podam.version}</version>
            </dependency>

            <!-- 工作流相关 -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-actuator</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <!-- 工作流相关结束 -->

            <!-- 工具类相关 -->
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-spring-boot-starter-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-5.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.hutool</groupId>
                <artifactId>hutool-extra</artifactId>
                <version>${hutool-6.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId> <!-- 文件类型的识别 -->
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId> <!-- 解决 ftp 连接 -->
                <version>${commons-net.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId> <!-- 解决 sftp 连接 -->
                <version>${jsch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-captcha-plus</artifactId>
                <version>${captcha-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <!-- 三方云服务相关 -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-justauth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
                <version>${justauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-mp-spring-boot-starter</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
                <version>${weixin-java.version}</version>
            </dependency>

            <!-- 积木报表-->
            <dependency>
                <groupId>org.jeecgframework.jimureport</groupId>
                <artifactId>jimureport-spring-boot3-starter-fastjson2</artifactId>
                <version>${jimureport.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>druid</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- MQTT -->
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${mqtt.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>bom</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
