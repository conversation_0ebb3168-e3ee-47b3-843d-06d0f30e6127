package com.ixtech.global.common.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Objects;

/**
 * 数据摘要工具类
 *
 * <AUTHOR> hu
 * @date 2025/7/22 14:03
 */
public class DataDigestUtil {

    /**
     * 生成字符串的MD5摘要
     */
    public static String md5Digest(String data) {
        return digest(data, "MD5");
    }

    /**
     * 生成字符串的SHA-256摘要
     */
    public static String sha256Digest(String data) {
        return digest(data, "SHA-256");
    }

    private static String digest(String data, String algorithm) {
        Objects.requireNonNull(data);
        try {
            MessageDigest md = MessageDigest.getInstance(algorithm);
            byte[] hash = md.digest(data.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Unsupported algorithm: " + algorithm, e);
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

}
