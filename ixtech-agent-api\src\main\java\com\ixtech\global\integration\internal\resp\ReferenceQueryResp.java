package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 车辆简单详情req
 *
 * <AUTHOR>
 * @date 2025/3/28
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReferenceQueryResp {


    /**
     * 金额
     */
    private BigDecimal totalAmount;

    /**
     * 门店
     */
    private JipinzucheStoreDTO store;

    /**
     * 车型
     */
    private JipinzucheCarModelDTO carModel;

    /**
     * 品牌
     */
    private JipinzucheCarBrandDTO carBrand;

    /**
     * 保险
     */
    private CarStockInsuranceDTO stockInsurance;

    /**
     * 库存
     */
    private JipinzucheCarStockDTO stock;
}
