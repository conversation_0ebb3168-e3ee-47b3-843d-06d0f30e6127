package com.ixtech.management.domain.service.impl;

import com.ixtech.management.domain.service.ModelService;
import com.ixtech.management.integration.internal.resp.ModelSelectOptionResponse;
import com.ixtech.management.repo.entity.JipinzucheCarList;
import com.ixtech.management.repo.entity.JipinzucheCarModel;
import com.ixtech.management.repo.entity.JipinzucheCarStock;
import com.ixtech.management.repo.repository.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 供应商service
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:14
 */
@Slf4j
@Service
public class ModelServiceImpl implements ModelService {

    @Resource
    private StoreRepository storeRepository;
    @Resource
    private JipinzucheCarModelRepository jipinzucheCarModelRepository;

    @Resource
    private JipinzucheCarStockRepository jipinzucheCarStockRepository;

    @Resource
    private JipinzucheCarListRepository jipinzucheCarListRepository;

    @Override
    public List<ModelSelectOptionResponse> dropdownList(Long storeId) {
        List<JipinzucheCarModel> list = jipinzucheCarModelRepository.selectAll();
        List<ModelSelectOptionResponse> modelSelectOptionResponses = new ArrayList<>();
        list.forEach(jipinzucheCarModel -> {
            ModelSelectOptionResponse modelSelectOptionResponse = new ModelSelectOptionResponse();
            modelSelectOptionResponse.setValue(Math.toIntExact(jipinzucheCarModel.getId()));
            modelSelectOptionResponse.setLabel(jipinzucheCarModel.getName());
            modelSelectOptionResponse.setSeat(jipinzucheCarModel.getSeat());
            modelSelectOptionResponse.setCarcode(jipinzucheCarModel.getCarcode());
            modelSelectOptionResponse.setTransmission(jipinzucheCarModel.getTransmission() == 1 ? "自动档": "手动档");
            List<JipinzucheCarStock> jipinzucheCarStocks = jipinzucheCarStockRepository.selectByModelIdAndStoreId(jipinzucheCarModel.getId(), storeId);
            // 保留门店存在的model
            if (jipinzucheCarStocks != null && jipinzucheCarStocks.size() > 0) {
                List<JipinzucheCarList> jipinzucheCarLists = jipinzucheCarListRepository.selectByStockId(jipinzucheCarStocks.getFirst().getId());
                if (CollectionUtils.isNotEmpty(jipinzucheCarLists)) {
                    modelSelectOptionResponse.setFuelmodel(jipinzucheCarLists.getFirst().getFuelmodel());
                }
                modelSelectOptionResponse.setDoor(jipinzucheCarModel.getDoor());
                modelSelectOptionResponses.add(modelSelectOptionResponse);
            }
        });

        return modelSelectOptionResponses;
    }

}
