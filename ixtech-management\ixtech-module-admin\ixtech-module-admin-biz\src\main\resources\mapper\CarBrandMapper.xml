<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.CarBrandMapper">
    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.CarBrand">
        <!--@mbg.generated-->
        <!--@Table jipinzuche_car_brand-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="active" jdbcType="BIT" property="active"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="time" jdbcType="INTEGER" property="time"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="mid" jdbcType="INTEGER" property="mid"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, active, deleted, create_time, update_time, `name`, country, logo, `time`, ip,
        mid, `status`
    </sql>
    <sql id="Ignore_Deleted">
        status != -1
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from jipinzuche_car_brand
        where id = #{id,jdbcType=INTEGER} AND
        <include refid="Ignore_Deleted"/>
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ixtech.management.repo.entity.CarBrand"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_car_brand (active, deleted, create_time,
        update_time, `name`, country,
        logo, `time`, ip, mid,
        `status`)
        values (#{active,jdbcType=BIT}, #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR},
        #{logo,jdbcType=VARCHAR}, #{time,jdbcType=INTEGER}, #{ip,jdbcType=VARCHAR}, #{mid,jdbcType=INTEGER},
        #{status,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ixtech.management.repo.entity.CarBrand" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_car_brand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="active != null">
                active,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="country != null">
                country,
            </if>
            <if test="logo != null">
                logo,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="mid != null">
                mid,
            </if>
            <if test="status != null">
                `status`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="active != null">
                #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                #{country,jdbcType=VARCHAR},
            </if>
            <if test="logo != null">
                #{logo,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                #{time,jdbcType=INTEGER},
            </if>
            <if test="ip != null">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="mid != null">
                #{mid,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ixtech.management.repo.entity.CarBrand">
        <!--@mbg.generated-->
        update jipinzuche_car_brand
        <set>
            <if test="active != null">
                active = #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                country = #{country,jdbcType=VARCHAR},
            </if>
            <if test="logo != null">
                logo = #{logo,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=INTEGER},
            </if>
            <if test="ip != null">
                ip = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="mid != null">
                mid = #{mid,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ixtech.management.repo.entity.CarBrand">
        <!--@mbg.generated-->
        update jipinzuche_car_brand
        set active = #{active,jdbcType=BIT},
        deleted = #{deleted,jdbcType=BIT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        `name` = #{name,jdbcType=VARCHAR},
        country = #{country,jdbcType=VARCHAR},
        logo = #{logo,jdbcType=VARCHAR},
        `time` = #{time,jdbcType=INTEGER},
        ip = #{ip,jdbcType=VARCHAR},
        mid = #{mid,jdbcType=INTEGER},
        `status` = #{status,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update jipinzuche_car_brand
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="country = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.country,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="logo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.logo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`time` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.time,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.ip,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.mid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update jipinzuche_car_brand
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.active != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deleted != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="country = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.country != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.country,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="logo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.logo != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.logo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`time` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.time != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.time,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ip != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.ip,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.mid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_car_brand
        (active, deleted, create_time, update_time, `name`, country, logo, `time`, ip, mid,
        `status`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.active,jdbcType=BIT}, #{item.deleted,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.name,jdbcType=VARCHAR}, #{item.country,jdbcType=VARCHAR},
            #{item.logo,jdbcType=VARCHAR}, #{item.time,jdbcType=INTEGER}, #{item.ip,jdbcType=VARCHAR},
            #{item.mid,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="selectByPrimaryKeyList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM jipinzuche_car_brand
        <where>
            id IN
            <foreach item="item" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND
            <include refid="Ignore_Deleted"/>
        </where>
    </select>

</mapper>