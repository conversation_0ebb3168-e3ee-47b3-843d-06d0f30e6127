package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 联系方式目标类型枚举类
 * 对应 transfer_contact_detail 表的 target_type 字段
 *
 * <AUTHOR> name
 * @date 2025/7/25
 */
@Getter
public enum TransferContactTargetTypeEnum {

    DRIVER(1, "司机"),
    ORDER_CUSTOMER(2, "订单客户"),
    ;

    TransferContactTargetTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 类型值，对应数据库中的tinyint值
     */
    private final int code;

    /**
     * 类型名称，用于显示
     */
    private final String value;

}
