package com.ixtech.global.common.enums;

import lombok.Getter;

@Getter
public enum SaleStatusEnum {
    /**
     * 删除状态
     */
    ACTIVE(1, "上架", "active"),
    INACTIVE(2, "下架", "inactive"),
    BACKUP(3, "备用", "backup"),
    REMOVED(4, "已归档", "removed");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    private final String ename;

    /**
     * 构造方法
     *
     * @param code 状态码
     * @param name 状态名称
     */
    SaleStatusEnum(Integer code, String name,  String ename) {
        this.code = code;
        this.name = name;
        this.ename = ename;
    }

    public static SaleStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SaleStatusEnum type : SaleStatusEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
