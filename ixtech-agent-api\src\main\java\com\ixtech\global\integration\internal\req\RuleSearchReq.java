package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 规则类型 req
 *
 * @author: Phili
 * @date： 2025/4/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RuleSearchReq extends VendorSearchReq {

    private static final long serialVersionUID = 1L;

    /**
     * 规则类型code
     */
    @NotEmpty(message = "规则类型不能为空")
    private List<String> typeList;

}
