package com.ixtech.global.integration.internal.client;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.integration.internal.req.RuleSearchReq;
import com.ixtech.global.integration.internal.req.VehicleSearchReq;
import com.ixtech.global.integration.internal.req.VendorSearchReq;
import com.ixtech.global.integration.internal.resp.LocationSearchResp;
import com.ixtech.global.integration.internal.resp.RuleSearchResp;
import com.ixtech.global.integration.internal.resp.VehicleSearchResp;
import com.ixtech.global.integration.internal.resp.VendorSearchResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 供应商服务的 OpenFeign 客户端接口
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@FeignClient(name = "vendorbasicsrv", contextId = "vendorbasicsrvV2", path = "/v2/vendorbasicsrv/internal/vendor")
public interface VendorFeignClientV2 {

    /**
     * 供应商静态数据查询接口
     *
     * @param req 供应商查询请求参数
     * @return 包含供应商静态数据的 ApiResponse
     */
    @PostMapping("/vendor_search")
    ApiResponse<List<VendorSearchResp>> vendorSearch(@RequestBody VendorSearchReq req);

    /**
     * 供应商门店静态数据查询接口
     *
     * @param req 供应商查询请求参数
     * @return 包含门店静态数据的 ApiResponse
     */
    @PostMapping("/location_search")
    ApiResponse<List<LocationSearchResp>> locationSearch(@RequestBody VendorSearchReq req);

    /**
     * 供应商车型数据查询接口
     *
     * @param req 车型查询请求参数
     * @return 包含车型数据的 ApiResponse
     */
    @PostMapping("/vehicle_search")
    ApiResponse<List<VehicleSearchResp>> vehicleSearch(@RequestBody VehicleSearchReq req);

    /**
     * 供应商政策规则查询接口
     *
     * @param req 政策规则查询请求参数
     * @return 包含政策规则的 ApiResponse
     */
    @PostMapping("/rule_search")
    ApiResponse<List<RuleSearchResp>> ruleSearch(@RequestBody RuleSearchReq req);
}
