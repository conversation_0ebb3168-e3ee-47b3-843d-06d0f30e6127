package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CurrencyEnum implements DictInf {
    // 阿富汗
    AFN("阿富汗尼", "Afghani", "AFN", "971", "؋"),

    // 欧盟国家
    EUR("欧元", "Euro", "EUR", "978", "€"),

    // 阿尔巴尼亚
    ALL("阿尔巴尼亚列克", "Albanian Lek", "ALL", "008", "Lek"),

    // 阿尔及利亚
    DZD("阿尔及利亚第纳尔", "Algerian Dinar", "DZD", "012", "د.ج"),

    // 安哥拉
    AOA("安哥拉宽扎", "Angolan Kwanza", "AOA", "973", "Kz"),

    // 东加勒比元
    XCD("东加勒比元", "East Caribbean Dollar", "XCD", "951", "$"),

    // 阿根廷
    ARS("阿根廷比索", "Argentine Peso", "ARS", "032", "$"),

    // 亚美尼亚
    AMD("亚美尼亚德拉姆", "Armenian Dram", "AMD", "051", "֏"),

    // 阿鲁巴岛
    AWG("阿鲁巴弗洛林", "Aruban Florin", "AWG", "533", "ƒ"),

    // 澳大利亚
    AUD("澳元", "Australian Dollar", "AUD", "036", "$"),

    // 阿塞拜疆
    AZN("阿塞拜疆马纳特", "Azerbaijani Manat", "AZN", "944", "₼"),

    // 巴哈马群岛
    BSD("巴哈马元", "Bahamian Dollar", "BSD", "044", "$"),

    // 巴林
    BHD("巴林第纳尔", "Bahraini Dinar", "BHD", "048", ".د.ب"),

    // 孟加拉国
    BDT("孟加拉塔卡", "Bangladeshi Taka", "BDT", "050", "৳"),

    // 巴巴多斯
    BBD("巴巴多斯元", "Barbadian Dollar", "BBD", "052", "$"),

    // 白俄罗斯
    BYN("白俄罗斯卢布", "Belarusian Ruble", "BYN", "933", "Br"),

    // 伯利兹城
    BZD("伯利兹元", "Belize Dollar", "BZD", "084", "BZ$"),

    // 西非法郎
    XOF("非洲金融共同体法郎", "CFA Franc BCEAO", "XOF", "952", "CFA"),

    // 百慕大群岛
    BMD("百慕大元", "Bermudian Dollar", "BMD", "060", "$"),

    // 不丹
    BTN("不丹努尔特鲁姆", "Bhutanese Ngultrum", "BTN", "064", "Nu."),

    // 玻利维亚
    BOB("玻利维亚诺", "Bolivian Boliviano", "BOB", "068", "Bs."),
    BOV("玻利维亚资金", "Mvdol", "BOV", "984", "BOV"),

    // 波斯尼亚和黑塞哥维那
    BAM("波斯尼亚可兑换马克", "Bosnia-Herzegovina Convertible Mark", "BAM", "977", "KM"),

    // 博茨瓦纳
    BWP("博茨瓦纳普拉", "Botswanan Pula", "BWP", "072", "P"),

    // 巴西
    BRL("巴西雷亚尔", "Brazilian Real", "BRL", "986", "R$"),

    // 文莱
    BND("文莱元", "Brunei Dollar", "BND", "096", "$"),

    // 保加利亚
    BGN("保加利亚列弗", "Bulgarian Lev", "BGN", "975", "лв"),

    // 布隆迪
    BIF("布隆迪法郎", "Burundian Franc", "BIF", "108", "FBu"),

    // 柬埔寨
    KHR("柬埔寨瑞尔", "Cambodian Riel", "KHR", "116", "៛"),

    // 中非法郎
    XAF("中非金融合作法郎", "CFA Franc BEAC", "XAF", "950", "FCFA"),

    // 加拿大
    CAD("加元", "Canadian Dollar", "CAD", "124", "$"),

    // 开曼群岛
    KYD("开曼群岛元", "Cayman Islands Dollar", "KYD", "136", "$"),

    // 智利
    CLP("智利比索", "Chilean Peso", "CLP", "152", "$"),
    CLF("智利资金", "Unidad de Fomento", "CLF", "990", "UF"),

    // 中国
    CNY("人民币元", "Chinese Yuan", "CNY", "156", "¥"),

    // 哥伦比亚
    COP("哥伦比亚比索", "Colombian Peso", "COP", "170", "$"),
    COU("哥伦比亚Unidad de Valor Real", "Unidad de Valor Real", "COU", "970", "COU"),

    // 科摩罗
    KMF("科摩罗法郎", "Comorian Franc", "KMF", "174", "CF"),

    // 刚果民主共和国
    CDF("刚果法郎", "Congolese Franc", "CDF", "976", "FC"),

    // 新西兰元
    NZD("新西兰元", "New Zealand Dollar", "NZD", "554", "$"),

    // 哥斯达黎加
    CRC("哥斯达黎加科朗", "Costa Rican Colon", "CRC", "188", "₡"),

    // 克罗地亚
    HRK("克罗地亚库纳", "Croatian Kuna", "HRK", "191", "kn"),

    // 古巴
    CUP("古巴比索", "Cuban Peso", "CUP", "192", "$"),
    CUC("古巴可兑换比索", "Cuban Convertible Peso", "CUC", "931", "CUC$"),

    // 荷属安的列斯盾
    ANG("荷属安的列斯盾", "Netherlands Antillean Guilder", "ANG", "532", "ƒ"),

    // 捷克共和国
    CZK("捷克克朗", "Czech Koruna", "CZK", "203", "Kč"),

    // 丹麦
    DKK("丹麦克朗", "Danish Krone", "DKK", "208", "kr"),

    // 吉布提
    DJF("吉布提法郎", "Djiboutian Franc", "DJF", "262", "Fdj"),

    // 多米尼加共和国
    DOP("多米尼加比索", "Dominican Peso", "DOP", "214", "RD$"),

    // 埃及
    EGP("埃及镑", "Egyptian Pound", "EGP", "818", "£"),

    // 厄立特里亚
    ERN("厄立特里亚纳克法", "Eritrean Nakfa", "ERN", "232", "Nfk"),

    // 埃塞俄比亚
    ETB("埃塞俄比亚比尔", "Ethiopian Birr", "ETB", "230", "Br"),

    // 福克兰群岛
    FKP("福克兰群岛镑", "Falkland Islands Pound", "FKP", "238", "£"),

    // 斐济
    FJD("斐济元", "Fijian Dollar", "FJD", "242", "$"),

    // 太平洋法郎
    XPF("太平洋法郎", "CFP Franc", "XPF", "953", "₣"),

    // 冈比亚
    GMD("冈比亚达拉西", "Gambian Dalasi", "GMD", "270", "D"),

    // 格鲁吉亚
    GEL("格鲁吉亚拉里", "Georgian Lari", "GEL", "981", "₾"),

    // 加纳
    GHS("加纳塞地", "Ghanaian Cedi", "GHS", "936", "₵"),

    // 直布罗陀
    GIP("直布罗陀镑", "Gibraltar Pound", "GIP", "292", "£"),

    // 危地马拉
    GTQ("危地马拉格查尔", "Guatemalan Quetzal", "GTQ", "320", "Q"),

    // 根西岛
    GGP("根西镑", "Guernsey Pound", "GGP", null, "£"),

    // 几内亚
    GNF("几内亚法郎", "Guinean Franc", "GNF", "324", "FG"),

    // 圭亚那
    GYD("圭亚那元", "Guyanese Dollar", "GYD", "328", "$"),

    // 海地
    HTG("海地古德", "Haitian Gourde", "HTG", "332", "G"),

    // 洪都拉斯
    HNL("洪都拉斯伦皮拉", "Honduran Lempira", "HNL", "340", "L"),

    // 香港
    HKD("港元", "Hong Kong Dollar", "HKD", "344", "$"),

    // 匈牙利
    HUF("匈牙利福林", "Hungarian Forint", "HUF", "348", "Ft"),

    // 冰岛
    ISK("冰岛克朗", "Icelandic Króna", "ISK", "352", "kr"),

    // 印度
    INR("印度卢比", "Indian Rupee", "INR", "356", "₹"),

    // 印度尼西亚
    IDR("印尼盾", "Indonesian Rupiah", "IDR", "360", "Rp"),

    // 特别提款权
    XDR("特别提款权", "Special Drawing Rights", "XDR", "960", "XDR"),

    // 伊朗
    IRR("伊朗里亚尔", "Iranian Rial", "IRR", "364", "﷼"),

    // 伊拉克
    IQD("伊拉克第纳尔", "Iraqi Dinar", "IQD", "368", "ع.د"),

    // 马恩岛
    IMP("马恩岛镑", "Manx Pound", "IMP", null, "£"),

    // 以色列
    ILS("以色列新谢克尔", "Israeli New Shekel", "ILS", "376", "₪"),

    // 牙买加
    JMD("牙买加元", "Jamaican Dollar", "JMD", "388", "J$"),

    // 日本
    JPY("日元", "Japanese Yen", "JPY", "392", "¥"),

    // 泽西岛
    JEP("泽西岛镑", "Jersey Pound", "JEP", null, "£"),

    // 约旦
    JOD("约旦第纳尔", "Jordanian Dinar", "JOD", "400", "د.ا"),

    // 哈萨克斯坦
    KZT("哈萨克斯坦坚戈", "Kazakhstani Tenge", "KZT", "398", "₸"),

    // 肯尼亚
    KES("肯尼亚先令", "Kenyan Shilling", "KES", "404", "KSh"),

    // 朝鲜
    KPW("朝鲜圆", "North Korean Won", "KPW", "408", "₩"),

    // 韩国
    KRW("韩元", "South Korean Won", "KRW", "410", "₩"),

    // 科威特
    KWD("科威特第纳尔", "Kuwaiti Dinar", "KWD", "414", "د.ك"),

    // 吉尔吉斯斯坦
    KGS("吉尔吉斯斯坦索姆", "Kyrgystani Som", "KGS", "417", "с"),

    // 老挝
    LAK("老挝基普", "Laotian Kip", "LAK", "418", "₭"),

    // 黎巴嫩
    LBP("黎巴嫩镑", "Lebanese Pound", "LBP", "422", "ل.ل"),

    // 莱索托
    LSL("莱索托洛蒂", "Lesotho Loti", "LSL", "426", "L"),
    // 利比里亚
    LRD("利比里亚元", "Liberian Dollar", "LRD", "430", "$"),

    // 利比亚
    LYD("利比亚第纳尔", "Libyan Dinar", "LYD", "434", "ل.د"),

    // 瑞士
    CHF("瑞士法郎", "Swiss Franc", "CHF", "756", "CHF"),

    // 澳门
    MOP("澳门元", "Macanese Pataca", "MOP", "446", "MOP$"),

    // 北马其顿
    MKD("北马其顿第纳尔", "Macedonian Denar", "MKD", "807", "ден"),

    // 马达加斯加
    MGA("马达加斯加阿里亚里", "Malagasy Ariary", "MGA", "969", "Ar"),

    // 马拉维
    MWK("马拉维克瓦查", "Malawian Kwacha", "MWK", "454", "MK"),

    // 马来西亚
    MYR("马来西亚令吉", "Malaysian Ringgit", "MYR", "458", "RM"),

    // 马尔代夫
    MVR("马尔代夫拉菲亚", "Maldivian Rufiyaa", "MVR", "462", "Rf"),

    // 毛里塔尼亚
    MRU("毛里塔尼亚乌吉亚", "Mauritanian Ouguiya", "MRU", "929", "UM"),

    // 毛里求斯
    MUR("毛里求斯卢比", "Mauritian Rupee", "MUR", "480", "₨"),

    // 墨西哥
    MXN("墨西哥比索", "Mexican Peso", "MXN", "484", "$"),
    MXV("墨西哥资金", "Mexican Investment Unit", "MXV", "979", "MXV"),

    // 摩尔多瓦
    MDL("摩尔多瓦列伊", "Moldovan Leu", "MDL", "498", "L"),

    // 蒙古
    MNT("蒙古图格里克", "Mongolian Tugrik", "MNT", "496", "₮"),

    // 摩洛哥
    MAD("摩洛哥迪拉姆", "Moroccan Dirham", "MAD", "504", "د.م."),

    // 莫桑比克
    MZN("莫桑比克梅蒂卡尔", "Mozambican Metical", "MZN", "943", "MT"),

    // 缅甸
    MMK("缅甸元", "Myanmar Kyat", "MMK", "104", "K"),

    // 纳米比亚
    NAD("纳米比亚元", "Namibian Dollar", "NAD", "516", "$"),

    // 尼泊尔
    NPR("尼泊尔卢比", "Nepalese Rupee", "NPR", "524", "₨"),

    // 尼加拉瓜
    NIO("尼加拉瓜科多巴", "Nicaraguan Cordoba", "NIO", "558", "C$"),

    // 尼日利亚
    NGN("尼日利亚奈拉", "Nigerian Naira", "NGN", "566", "₦"),

    // 挪威
    NOK("挪威克朗", "Norwegian Krone", "NOK", "578", "kr"),

    // 阿曼
    OMR("阿曼里亚尔", "Omani Rial", "OMR", "512", "ر.ع."),

    // 巴基斯坦
    PKR("巴基斯坦卢比", "Pakistani Rupee", "PKR", "586", "₨"),

    // 巴拿马
    PAB("巴拿马巴波亚", "Panamanian Balboa", "PAB", "590", "B/."),

    // 巴布亚新几内亚
    PGK("巴布亚新几内亚基那", "Papua New Guinean Kina", "PGK", "598", "K"),

    // 巴拉圭
    PYG("巴拉圭瓜拉尼", "Paraguayan Guarani", "PYG", "600", "₲"),

    // 秘鲁
    PEN("秘鲁新索尔", "Peruvian Nuevo Sol", "PEN", "604", "S/."),

    // 菲律宾
    PHP("菲律宾比索", "Philippine Peso", "PHP", "608", "₱"),

    // 波兰
    PLN("波兰兹罗提", "Polish Zloty", "PLN", "985", "zł"),

    // 卡塔尔
    QAR("卡塔尔里亚尔", "Qatari Rial", "QAR", "634", "ر.ق"),

    // 罗马尼亚
    RON("罗马尼亚列伊", "Romanian Leu", "RON", "946", "lei"),

    // 俄罗斯
    RUB("俄罗斯卢布", "Russian Ruble", "RUB", "643", "₽"),

    // 卢旺达
    RWF("卢旺达法郎", "Rwandan Franc", "RWF", "646", "FRw"),

    // 圣赫勒拿
    SHP("圣赫勒拿镑", "Saint Helena Pound", "SHP", "654", "£"),

    // 萨摩亚
    WST("萨摩亚塔拉", "Samoan Tala", "WST", "882", "T"),

    // 圣多美和普林西比
    STN("圣多美和普林西比多布拉", "Sao Tomean Dobra", "STN", "930", "Db"),

    // 沙特阿拉伯
    SAR("沙特里亚尔", "Saudi Riyal", "SAR", "682", "ر.س"),

    // 塞尔维亚
    RSD("塞尔维亚第纳尔", "Serbian Dinar", "RSD", "941", "дин"),

    // 塞舌尔
    SCR("塞舌尔卢比", "Seychellois Rupee", "SCR", "690", "₨"),

    // 塞拉利昂
    SLL("塞拉利昂利昂", "Sierra Leonean Leone", "SLL", "694", "Le"),

    // 新加坡
    SGD("新加坡元", "Singapore Dollar", "SGD", "702", "$"),

    // 所罗门群岛
    SBD("所罗门群岛元", "Solomon Islands Dollar", "SBD", "090", "$"),

    // 索马里
    SOS("索马里先令", "Somali Shilling", "SOS", "706", "S"),

    // 南非
    ZAR("南非兰特", "South African Rand", "ZAR", "710", "R"),

    // 南苏丹
    SSP("南苏丹镑", "South Sudanese Pound", "SSP", "728", "£"),

    // 斯里兰卡
    LKR("斯里兰卡卢比", "Sri Lankan Rupee", "LKR", "144", "Rs"),

    // 苏丹
    SDG("苏丹镑", "Sudanese Pound", "SDG", "938", "ج.س."),

    // 苏里南
    SRD("苏里南元", "Surinamese Dollar", "SRD", "968", "$"),

    // 瑞典
    SEK("瑞典克朗", "Swedish Krona", "SEK", "752", "kr"),

    // 瑞士补充
    CHE("WIR欧元", "WIR Euro", "CHE", "947", "CHE"),
    CHW("WIR法郎", "WIR Franc", "CHW", "948", "CHW"),

    // 叙利亚
    SYP("叙利亚镑", "Syrian Pound", "SYP", "760", "£S"),

    // 台湾(中国的一个省份)
    TWD("新台币", "New Taiwan Dollar", "TWD", "901", "NT$"),

    // 塔吉克斯坦
    TJS("塔吉克斯坦索莫尼", "Tajikistani Somoni", "TJS", "972", "ЅМ"),

    // 坦桑尼亚
    TZS("坦桑尼亚先令", "Tanzanian Shilling", "TZS", "834", "TSh"),

    // 泰国
    THB("泰铢", "Thai Baht", "THB", "764", "฿"),

    // 汤加
    TOP("汤加潘加", "Tongan Pa'anga", "TOP", "776", "T$"),

    // 特立尼达和多巴哥
    TTD("特立尼达和多巴哥元", "Trinidad and Tobago Dollar", "TTD", "780", "TT$"),

    // 突尼斯
    TND("突尼斯第纳尔", "Tunisian Dinar", "TND", "788", "د.ت"),

    // 土耳其
    TRY("土耳其里拉", "Turkish Lira", "TRY", "949", "₺"),

    // 土库曼斯坦
    TMT("土库曼斯坦马纳特", "Turkmenistani Manat", "TMT", "934", "m"),

    // 乌干达
    UGX("乌干达先令", "Ugandan Shilling", "UGX", "800", "USh"),

    // 乌克兰
    UAH("乌克兰格里夫纳", "Ukrainian Hryvnia", "UAH", "980", "₴"),

    // 阿拉伯联合酋长国
    AED("阿联酋迪拉姆", "UAE Dirham", "AED", "784", "د.إ"),

    // 英国
    GBP("英镑", "British Pound", "GBP", "826", "£"),

    // 美国
    USD("美元", "US Dollar", "USD", "840", "$"),
    USN("美元（次日）", "US Dollar (Next day)", "USN", "997", "USN"),

    // 乌拉圭
    UYU("乌拉圭比索", "Uruguayan Peso", "UYU", "858", "$U"),
    UYI("乌拉圭比索", "Uruguay Peso en Unidades Indexadas", "UYI", "940", "UYI"),

    // 乌兹别克斯坦
    UZS("乌兹别克斯坦索姆", "Uzbekistan Som", "UZS", "860", "so'm"),

    // 瓦努阿图
    VUV("瓦努阿图瓦图", "Vanuatu Vatu", "VUV", "548", "VT"),

    // 委内瑞拉
    VES("委内瑞拉玻利瓦尔", "Venezuelan Bolivar", "VES", "928", "Bs.S"),

    // 越南
    VND("越南盾", "Vietnamese Dong", "VND", "704", "₫"),

    // 也门
    YER("也门里亚尔", "Yemeni Rial", "YER", "886", "﷼"),

    // 赞比亚
    ZMW("赞比亚克瓦查", "Zambian Kwacha", "ZMW", "967", "ZK"),

    // 津巴布韦
    ZWL("津巴布韦元", "Zimbabwean Dollar", "ZWL", "932", "Z$");

    private final String chineseName;
    private final String englishName;
    private final String currencyCode;
    private final String numericCode;
    private final String symbol;

    @Override
    public String getLabel() {
        return currencyCode;
    }

    @Override
    public String getValue() {
        return currencyCode;
    }

    public String getNumericCode() {
        return numericCode;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public static CurrencyEnum fromCode(String currencyCode) {
        for (CurrencyEnum currency : values()) {
            if (currency.currencyCode.equalsIgnoreCase(currencyCode)) {
                return currency;
            }
        }
        return null;
    }

    public static CurrencyEnum fromNumericCode(String numericCode) {
        for (CurrencyEnum currency : values()) {
            if (currency.numericCode.equals(numericCode)) {
                return currency;
            }
        }
        return null;
    }
}