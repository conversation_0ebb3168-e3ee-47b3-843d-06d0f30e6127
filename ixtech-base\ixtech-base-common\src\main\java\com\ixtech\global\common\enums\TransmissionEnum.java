package com.ixtech.global.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TransmissionEnum {

    AUTOMATIC(1, "自动挡"),
    MANUAL(2, "手动挡"),
    SEMI_AUTOMATIC(3, "手自一体");

    /**
     * 数据库存储值 (对应 transmission 字段)
     */
    private final Integer value;

    /**
     * 中文描述
     */
    private final String label;

    /**
     * 根据数值获取枚举实例
     * 
     * @param value 数据库存储值
     * @return 对应的枚举实例，找不到返回null
     */
    public static TransmissionEnum of(Integer value) {
        if (value == null) {
            return null;
        }
        for (TransmissionEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据数值获取中文描述
     * 
     * @param value 数据库存储值
     * @return 对应的中文描述，找不到返回null
     */
    public static String getLabelByValue(Integer value) {
        TransmissionEnum type = of(value);
        return type != null ? type.getLabel() : null;
    }
    
    /**
     * 根据枚举名获取中文描述（不区分大小写）
     * 
     * @param name 枚举名称
     * @return 对应的中文描述，找不到返回null
     */
    public static String getLabelByCode(String name) {
        if (name == null) {
            return null;
        }
        try {
            return TransmissionEnum.valueOf(name.toUpperCase()).getLabel();
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}