package com.ixtech.global.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * @description 渠道性质枚举
 * <AUTHOR>
 * @date 2025-3-18
 */
@Getter
@ToString
@AllArgsConstructor
public enum EnumChannelNature {

    /**
     * 默认渠道
     */
    DEFAULT(1, "默认"),

    /**
     * 自有品牌渠道
     */
    OWN_BRAND(2, "自有品牌渠道");

    private final int code;
    private final String desc;

    /**
     * 根据code获取枚举
     * @param code code
     * @return 渠道性质枚举
     */
    public static EnumChannelNature getByCode(int code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode() == code)
                .findFirst()
                .orElse(null);
    }
}