package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 金额信息 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AmountResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 金额
     */
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 货币
     */
    private String currency;
}
