package com.ixtech.global.aop;

import com.ixtech.global.I18nTranslationExecutor;
import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.dto.ErrorData;
import com.ixtech.global.config.I18nProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.core.MethodParameter;
import org.springframework.core.Ordered;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 国际化响应体处理切面：自动翻译 ApiResponse 中的错误消息。
 *
 * <AUTHOR> hu
 * @date 2025/6/10 16:41
 */
@Slf4j
@ConditionalOnBean({I18nTranslationExecutor.class, I18nProperties.class})
@RestControllerAdvice
public class I18nResponseBodyAdvice implements ResponseBodyAdvice<Object>, Ordered {

    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{\\{(.*?)}}");

    /**
     * 全局异常处理器对错误文案默认加的前缀及其对应的翻译资源key
     */
    private static final Map<String, String> INNER_ERROR_PREFIX;

    static {
        INNER_ERROR_PREFIX = Map.of(
                "请求参数缺失:", "yd.err.119",
                "请求参数类型错误:", "yd.err.120",
                "请求参数不正确:", "yd.err.121",
                "请求地址不存在:", "yd.err.122",
                "请求方法不正确:", "yd.err.123"
        );
    }

    private I18nTranslationExecutor i18nTranslationExecutor;
    private I18nProperties i18nProperties;

    public I18nResponseBodyAdvice(I18nTranslationExecutor i18nTranslationExecutor, I18nProperties i18nProperties) {
        this.i18nTranslationExecutor = i18nTranslationExecutor;
        this.i18nProperties = i18nProperties;
    }

    @Override
    public int getOrder() {
        return i18nProperties.getI18nResponseBodyAdviceOrder();
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        if (body instanceof ApiResponse<?>) {
            // 数据返回之前进行翻译
            return translateBeforeBodyWrite((ApiResponse<?>) body);
        }
        return body;
    }

    /**
     * 数据返回之前进行翻译
     *
     * @param body
     * @return
     */
    private Object translateBeforeBodyWrite(ApiResponse<?> body) {

        ErrorData error = body.getError();
        if (error == null) {
            // 没有错误文案，无需翻译
            return body;
        }

        String message = error.getMessage();
        Set<String> resourceKeys = new HashSet<>();

        String prefixMessage = null;
        // 全局异常处理器中的错误文案前缀对应的翻译资源key
        String prefixResourceKey = null;
        if (StringUtils.isNotBlank(message)) {
            for (Map.Entry<String, String> entry : INNER_ERROR_PREFIX.entrySet()) {
                String key = entry.getKey();
                if (message.startsWith(key)) {
                    prefixMessage = key;
                    message = message.substring(key.length());
                    prefixResourceKey = entry.getValue();
                    resourceKeys.add(prefixResourceKey);
                    break;
                }
            }
        }

        // 获取翻译资源key
        String resourceKey = error.getResourceKey();
        if (StringUtils.isBlank(resourceKey) && StringUtils.isNotBlank(message)) {
            Matcher matcher = PLACEHOLDER_PATTERN.matcher(message);
            if (matcher.find()) {
                // 获取占位符内的内容，例如 {{name}} 中的 name
                resourceKey = matcher.group(1);
            }
        }
        if (StringUtils.isNotBlank(resourceKey)) {
            resourceKeys.add(resourceKey);
        }

        if (resourceKeys.isEmpty()) {
            return body;
        }

        try {

            // 获取翻译文案
            Map<String, String> translations = i18nTranslationExecutor.getTranslations(resourceKeys);

            String prefix = prefixMessage, translation = message;
            if (MapUtils.isNotEmpty(translations)) {
                if (prefixResourceKey != null) {
                    String prefixTranslation = translations.get(prefixResourceKey);
                    if (StringUtils.isNotBlank(prefixTranslation)) {
                        prefix = prefixTranslation;
                    }
                }
                if (resourceKey != null) {
                    String translationText = translations.get(resourceKey);
                    if (StringUtils.isNotBlank(translationText)) {
                        translation = translationText;
                    }
                }
            }

            StringBuilder messageTranslation = new StringBuilder();
            if (prefix != null) {
                messageTranslation.append(prefix);
            }
            if (translation != null) {
                messageTranslation.append(translation);
            }
            error.setMessage(messageTranslation.toString());

        } catch (Exception e) {
            log.error("翻译错误响应message失败，{}", e.getMessage(), e);
        }

        return body;

    }

}
