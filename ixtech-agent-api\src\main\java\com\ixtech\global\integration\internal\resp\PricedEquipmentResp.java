package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备列表项resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PricedEquipmentResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 费用信息
  */
 private Charge charge;

 /**
  * 设备类型
  */
 private String equipType;

 /**
  * 数量
  */
 private Integer quantity;

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class Charge {

  /**
   * 设备费用
   */
  private AmountResp chargeEquip;

  /**
   * 是否包含在预估总价中
   */
  private Boolean includedInEstTotalInd;

  /**
   * 税费金额
   */
  private TaxAmountResp taxAmount;
 }
}
