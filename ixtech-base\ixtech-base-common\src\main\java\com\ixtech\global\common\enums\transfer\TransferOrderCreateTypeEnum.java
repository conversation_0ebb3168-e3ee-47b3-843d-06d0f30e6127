package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 接送机订单创建方式
 *
 * <AUTHOR> hu
 * @date 2025/7/24 21:08
 */
@Getter
public enum TransferOrderCreateTypeEnum {

    USER_CREATE(1, "用户创建"),
    API(2, "API同步"),
    SPIDER(3, "爬取"),
    IMPORT(4, "订单导入"),
    ;

    TransferOrderCreateTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 附加服务类型code
     */
    private final Integer code;

    /**
     * 附加服务类型描述
     */
    private final String value;

}
