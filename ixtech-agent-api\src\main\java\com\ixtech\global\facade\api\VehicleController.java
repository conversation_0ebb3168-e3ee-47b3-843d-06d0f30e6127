package com.ixtech.global.facade.api;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.util.ErrorRespUtils;
import com.ixtech.global.domain.service.IVehicleService;
import com.ixtech.global.integration.internal.req.ReferenceQueryReq;
import com.ixtech.global.integration.internal.req.VehicleAvailListQueryReq;
import com.ixtech.global.integration.internal.req.VehicleDetailQueryReq;
import com.ixtech.global.integration.internal.resp.ReferenceQueryResp;
import com.ixtech.global.integration.internal.resp.VehicleAvailQueryResp;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * agentapi 车辆相关API
 * /agentopenapi/api/veh_avail_rate
 * /agentopenapi/api/veh_res
 * <AUTHOR>
 * @date 2025-03-24
 */
@Slf4j
@RestController
@RequestMapping("/agentopenapi/api")
public class VehicleController {

    //全局计数器-失败
    private static final Counter QUERY_VEHICLE_FAILED_COUNTER = Counter
            .builder("agent_query_vehicle_avail_list_failed_count")  // 指标名
            .description("Total number of failed queryVehicleAvailList")  // 描述
            .tag("result", "agent_query_vehicle_avail_list_failed")          // 自定义标签
            .register(Metrics.globalRegistry);  // 注册到全局Registry

    //全局计数器-无结果
    private static final Counter QUERY_VEHICLE_NO_RESULT_COUNTER = Counter
            .builder("agent_query_vehicle_avail_no_result_count")  // 指标名
            .description("Total number of no result queryVehicleAvailList")  // 描述
            .tag("result", "agent_query_vehicle_avail_list_no_result")          // 自定义标签
            .register(Metrics.globalRegistry);  // 注册到全局Registry

    @Resource(name = "vehicleServiceV2")
    private IVehicleService vehicleService;

    /**
     * 车辆搜索
     */
    @PostMapping("/veh_avail_rate")
    @Timed(
            value = "agent_veh_avail_rate_request_seconds",
            description = "agent_veh_avail_rate request latency",
            extraTags = {"api_url", "/agentopenapi/api/veh_avail_rate"},  // 与实际路径一致
            histogram = true
    )
    public ApiResponse<VehicleAvailQueryResp> vehicleList(@RequestBody @Valid VehicleAvailListQueryReq request) {
        ApiResponse<VehicleAvailQueryResp> response = vehicleService.queryVehicleAvailList(request);
        if(!response.isSuccess()){
            QUERY_VEHICLE_FAILED_COUNTER.increment();
        } else if (response.getResult() == null
                || CollectionUtils.isEmpty(response.getResult().getVehVendorAvails())
                || CollectionUtils.isEmpty(response.getResult().getVehVendorAvails().getFirst().getVehAvailCores())
                || response.getResult().getVehRentalCore() == null) {
            QUERY_VEHICLE_NO_RESULT_COUNTER.increment();
        }
       return ErrorRespUtils.checkServiceResponse(response, "车辆搜索失败");
    }

    /**
     * 车辆详情
     */
    @Timed(
            value = "agent_veh_rate_rule_request_seconds",
            description = "veh_rate_rule request latency",
            extraTags = {"api_url", "/agentopenapi/api/veh_rate_rule"},  // 与实际路径一致
            histogram = true
    )
    @PostMapping("/veh_rate_rule")
    public ApiResponse<VehicleAvailQueryResp> vehicleDetail(@RequestBody @Valid VehicleDetailQueryReq request) {
        ApiResponse<VehicleAvailQueryResp> resp = vehicleService.queryVehicleAvailDetail(request);
       return ErrorRespUtils.checkServiceResponse(resp, "车辆详情查询失败");
    }

    /**
     * 车辆详情 referenceId查找
     */
    @PostMapping("/ref_query")
    public ApiResponse<ReferenceQueryResp> refQuery(@RequestBody @Valid ReferenceQueryReq request) {
        ApiResponse<ReferenceQueryResp> resp = vehicleService.queryVehicleByReferenceId(request);
       return ErrorRespUtils.checkServiceResponse(resp, "车辆详情 referenceId 查询失败");
    }

}
