package com.ixtech.management.domain.service.impl;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ixtech.management.common.constants.RedisConstant;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.common.enums.BaseEnum;
import com.ixtech.management.common.enums.SettlementCurrencyEnum;
import com.ixtech.management.common.enums.SettlementModeEnum;
import com.ixtech.management.common.exception.BizException;
import com.ixtech.management.common.utils.ContentLengthValidateUtils;
import com.ixtech.management.common.utils.DateUtils;
import com.ixtech.management.domain.service.ChannelService;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.ChannelAddResp;
import com.ixtech.management.integration.internal.resp.ChannelInfoResp;
import com.ixtech.management.integration.internal.resp.ChannelListInfoResp;
import com.ixtech.management.repo.entity.CarOrderSource;
import com.ixtech.management.repo.repository.CarOrderSourceRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 渠道service
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:14
 */
@Slf4j
@Service
public class ChannelServiceImpl implements ChannelService {

    @Resource
    private CarOrderSourceRepository carOrderSourceRepository;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public PageResponse<ChannelListInfoResp> list(ChannelListQueryReq channelListQueryReq) {

        int pageNum = channelListQueryReq.getPageNum(), pageSize = channelListQueryReq.getPageSize();
        Page<Object> page = PageHelper.startPage(pageNum, pageSize, channelListQueryReq.isNeedTotalCount());

        List<CarOrderSource> channels = carOrderSourceRepository.searchByCondition(channelListQueryReq.getId(), channelListQueryReq.getChannelName(), channelListQueryReq.getCompanyFullName());
        List<ChannelListInfoResp> list = CollectionUtils.emptyIfNull(channels).stream()
                .map(this::convert2ChannelListInfoResp).toList();

        return PageResponse.success(list, page.getTotal(), pageNum);
    }

    /**
     * 将channel实体对象转换为ChannelListInfoResp对象
     *
     * @param channel
     * @return
     */
    private ChannelListInfoResp convert2ChannelListInfoResp(CarOrderSource channel) {
        if (channel == null) {
            return null;
        }
        ChannelListInfoResp channelListInfoResp = new ChannelListInfoResp();
        BeanUtils.copyProperties(channel, channelListInfoResp);
        channelListInfoResp.setChannelName(channel.getTitle());
        // 结算模式
        channelListInfoResp.setSettlementModeStr(showSettlementModeStr(channel.getSettlementMode()));
        channelListInfoResp.setCreateTime(DateUtils.toUtcDate(channel.getCreateTime()));
        return channelListInfoResp;
    }

    /**
     * 将结算模式code转化为名称
     *
     * @param settlementMode
     * @return
     */
    private String showSettlementModeStr(Byte settlementMode) {
        if (settlementMode == null) {
            return null;
        }
        SettlementModeEnum enumSettlementMode = BaseEnum.fromCode(SettlementModeEnum.class, settlementMode);
        return Optional.ofNullable(enumSettlementMode)
                .map(SettlementModeEnum::getName).orElse(null);
    }

    @Override
    public ChannelInfoResp info(Long id) {
        CarOrderSource channel = carOrderSourceRepository.selectById(id);
        ChannelInfoResp channelInfoResp = convert2ChannelInfoResp(channel);
        return channelInfoResp;
    }

    /**
     * 将channel实体对象转换为ChannelInfoResp对象
     *
     * @param channel
     * @return
     */
    private ChannelInfoResp convert2ChannelInfoResp(CarOrderSource channel) {
        if (channel == null) {
            return null;
        }
        ChannelInfoResp channelInfoResp = new ChannelInfoResp();
        BeanUtils.copyProperties(channel, channelInfoResp);
        channelInfoResp.setChannelName(channel.getTitle());
        // 结算模式
        channelInfoResp.setSettlementModeStr(showSettlementModeStr(channel.getSettlementMode()));
        return channelInfoResp;
    }

    @Override
    public ChannelAddResp add(ChannelAddReq channelAddReq) {

        // 报价币种、结算币种和结算模式校验
        validateCurrencyAndSettlement(channelAddReq.getQuoteCurrency(), channelAddReq.getSettlementCurrency(), channelAddReq.getSettlementMode());
        // 渠道名称和公司全称校验
        validateContentLength(channelAddReq.getChannelName(), channelAddReq.getCompanyFullName());

        CarOrderSource channel = new CarOrderSource();
        BeanUtils.copyProperties(channelAddReq, channel);
        channel.setTitle(channelAddReq.getChannelName());
        // 初始化api对接key
        //channel.setStage();
        //channel.setCredentialKey(UUID.randomUUID().toString(true));

        // 插入渠道
        Long id = null;
        try {
            carOrderSourceRepository.insertSelective(channel);
            id = channel.getId();
            // 删除缓存
            clearCache();
        } catch (Exception e) {
            log.error("新增渠道出错，渠道参数：{}", JSONObject.toJSONString(channelAddReq), e);
            handleInsertException(e);
        }

        ChannelAddResp channelAddResp = new ChannelAddResp();
        channelAddResp.setId(id);
        return channelAddResp;
    }

    /**
     * 报价币种、结算币种和结算模式校验
     *
     * @param quoteCurrency      报价币种
     * @param settlementCurrency 结算币种
     * @param settlementMode     结算模式
     */
    private void validateCurrencyAndSettlement(String quoteCurrency, String settlementCurrency, Byte settlementMode) {
        if (quoteCurrency != null && !SettlementCurrencyEnum.USD.getCode().equals(quoteCurrency) && !SettlementCurrencyEnum.HKD.getCode().equals(quoteCurrency)) {
            throw new BizException("报价货币当前只支持USD和HKD");
        }
        if (settlementCurrency != null && !SettlementCurrencyEnum.USD.getCode().equals(settlementCurrency) && !SettlementCurrencyEnum.HKD.getCode().equals(settlementCurrency)) {
            throw new BizException("结算货币当前只支持USD和HKD");
        }
        if (settlementMode != null && !SettlementModeEnum.BASE_PRICE.getCode().equals(settlementMode)) {
            throw new BizException("结算模式当前只支持底价模式");
        }
    }

    /**
     * 渠道简称和公司全称校验
     *
     * @param channelName     渠道简称
     * @param companyFullName 公司全称
     */
    private void validateContentLength(String channelName, String companyFullName) {
        String validateFailMessage = channelName != null ? ContentLengthValidateUtils.validate(channelName, 4, 20) : null;
        if (validateFailMessage != null) {
            throw new BizException("渠道简称校验失败，" + validateFailMessage);
        }
        validateFailMessage = companyFullName != null ? ContentLengthValidateUtils.validate(companyFullName, 4, 128) : null;
        if (validateFailMessage != null) {
            throw new BizException("公司全称校验失败，" + validateFailMessage);
        }
    }

    /**
     * 处理新增渠道异常
     *
     * @param e
     * @throws BizException
     */
    private void handleInsertException(Exception e) throws BizException {
        String errorMsg = "业务异常，请稍后再试";
        // 提取并验证是否为数据库完整性约束违反异常
        Throwable cause = e.getCause();
        if (!(cause instanceof SQLIntegrityConstraintViolationException sqlEx)) {
            throw new BizException(errorMsg);
        }
        // 解析并处理错误信息
        String errMsg = sqlEx.getMessage();
        if (errMsg != null && errMsg.contains("Duplicate entry")) {
            errorMsg = errMsg.contains("company_full_name") ? "已存在相同的公司全称，请修改"
                    : errMsg.contains("channel_name") ? "已存在相同的渠道简称，请修改"
                    : errMsg.contains("title") ? "已存在相同的渠道简称，请修改"
                    : "已存在相同的渠道数据，请修改";
        }
        throw new BizException(errorMsg);
    }

    @Override
    public void update(ChannelUpdateReq channelUpdateReq) {

        // 报价币种、结算币种和结算模式校验
        validateCurrencyAndSettlement(channelUpdateReq.getQuoteCurrency(), channelUpdateReq.getSettlementCurrency(), channelUpdateReq.getSettlementMode());
        // 渠道名称和公司全称校验
        validateContentLength(channelUpdateReq.getChannelName(), channelUpdateReq.getCompanyFullName());

        Long id = channelUpdateReq.getId();
        CarOrderSource oldChannel = carOrderSourceRepository.selectById(id);
        if (oldChannel == null) {
            throw new BizException("渠道不存在！");
        }

        CarOrderSource channel = new CarOrderSource();
        BeanUtils.copyProperties(channelUpdateReq, channel);
        channel.setTitle(channelUpdateReq.getChannelName());

        try {
            carOrderSourceRepository.updateByPrimaryKeySelective(channel);
            // 删除渠道信息缓存
            clearCache();
        } catch (Exception e) {
            log.error("更新渠道出错，渠道参数：{}", JSONObject.toJSONString(channelUpdateReq), e);
            handleUpdateException(e);
        }

    }

    /**
     * 处理更新渠道异常
     *
     * @param e
     * @throws BizException
     */
    private void handleUpdateException(Exception e) throws BizException {
        handleInsertException(e);
    }

    @Override
    public void setActive(ChannelSetActiveReq channelSetActiveReq) {

        Long id = channelSetActiveReq.getId();
        CarOrderSource oldChannel = carOrderSourceRepository.selectById(id);
        if (oldChannel == null) {
            throw new BizException("渠道不存在！");
        }

        CarOrderSource channel = new CarOrderSource();
        channel.setId(channelSetActiveReq.getId());
        channel.setActive(channelSetActiveReq.getActive());
        carOrderSourceRepository.updateByPrimaryKeySelective(channel);
        // 删除渠道信息缓存
        clearCache();
    }

    @Override
    public List<SelectOptionResponse<Long>> dropdownList(ChannelDropdownReq channelDropdownReq) {
        List<CarOrderSource> list = carOrderSourceRepository.searchByCondition(null, channelDropdownReq.getChannelName(), null);
        return CollectionUtils.emptyIfNull(list).stream()
                .map(c -> new SelectOptionResponse<>(c.getId(), c.getTitle())).toList();
    }

    /**
     * 删除渠道信息缓存
     */
    private void clearCache() {
        try {
            // 删除AgentApi服务中的渠道信息缓存
            String cacheKey = RedisConstant.ORDER_SOURCE_QUERY_KEY;
            redissonClient.getBucket(cacheKey).delete();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
