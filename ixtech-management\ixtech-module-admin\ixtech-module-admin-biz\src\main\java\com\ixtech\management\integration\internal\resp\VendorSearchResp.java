package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 供应商搜索 resp
 *
 * @author: Phili
 * @date： 2025/3/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VendorSearchResp implements Serializable {
 
 private static final long serialVersionUID = 1L;

 /**
  * 供应商代码
  */
 private String code;

 /**
  * 供应商名称
  */
 private String companyName;
}
