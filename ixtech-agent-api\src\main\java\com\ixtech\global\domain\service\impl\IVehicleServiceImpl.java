package com.ixtech.global.domain.service.impl;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.domain.service.IVehicleService;
import com.ixtech.global.integration.internal.client.ProductsrvFeignClient;
import com.ixtech.global.integration.internal.req.ReferenceQueryReq;
import com.ixtech.global.integration.internal.req.VehicleAvailListQueryReq;
import com.ixtech.global.integration.internal.req.VehicleDetailQueryReq;
import com.ixtech.global.integration.internal.resp.ReferenceQueryResp;
import com.ixtech.global.integration.internal.resp.VehicleAvailQueryResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("vehicleService")
public class IVehicleServiceImpl implements IVehicleService {

    @Resource
    private ProductsrvFeignClient productsrvFeignClient;

    @Override
    public ApiResponse<VehicleAvailQueryResp> queryVehicleAvailList(VehicleAvailListQueryReq request) {
        return productsrvFeignClient.vehicleList(request);
    }

    @Override
    public ApiResponse<VehicleAvailQueryResp> queryVehicleAvailDetail(VehicleDetailQueryReq request) {
        return productsrvFeignClient.vehicleDetail(request);
    }

    @Override
    public ApiResponse<ReferenceQueryResp> queryVehicleByReferenceId(ReferenceQueryReq request) {
        return productsrvFeignClient.refQuery(request);
    }
}
