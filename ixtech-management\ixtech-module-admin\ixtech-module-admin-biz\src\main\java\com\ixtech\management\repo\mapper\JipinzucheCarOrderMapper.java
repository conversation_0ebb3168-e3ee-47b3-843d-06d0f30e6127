package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.integration.internal.req.OrderQueryReq;
import com.ixtech.management.repo.entity.JipinzucheCarOrder;
import com.ixtech.management.repo.entity.JipinzucheCarOrderPO;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【jipinzuche_car_order(车辆订单表)】的数据库操作Mapper
* @createDate 2025-04-21 16:51:28
* @Entity generator.domain.JipinzucheCarOrder
*/
@DS("ix")
@Mapper
public interface JipinzucheCarOrderMapper {

    int insertSelective(JipinzucheCarOrderPO record);

    JipinzucheCarOrderPO selectById(Long id);

    JipinzucheCarOrder selectBySourceOrderCode(String sourceOrdercode);

    Long selectCountBySourceOrderCode(String sourceOrdercode);

    JipinzucheCarOrderPO selectBySourceAndSourceOrderCode(OrderQueryReq orderQueryReq);
}




