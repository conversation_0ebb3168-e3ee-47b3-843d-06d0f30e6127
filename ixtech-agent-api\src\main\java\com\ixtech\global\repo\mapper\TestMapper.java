package com.ixtech.global.repo.mapper;

import com.ixtech.global.repo.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 测试mapper
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Mapper
public interface TestMapper {

    /**
     * 根据条件参数查询用户信息
     */
    List<User> selectUsersByParams(@Param("first") int first, @Param("limit") int limit);
}
