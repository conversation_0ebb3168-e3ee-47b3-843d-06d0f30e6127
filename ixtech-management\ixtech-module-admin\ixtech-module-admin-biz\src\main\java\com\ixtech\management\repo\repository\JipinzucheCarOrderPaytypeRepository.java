package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.JipinzucheCarModel;
import com.ixtech.management.repo.entity.JipinzucheCarOrderPaytype;
import com.ixtech.management.repo.mapper.JipinzucheCarModelMapper;
import com.ixtech.management.repo.mapper.JipinzucheCarOrderPaytypeMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class JipinzucheCarOrderPaytypeRepository {

    @Resource
    private JipinzucheCarOrderPaytypeMapper jipinzucheCarOrderPaytypeMapper;

    public JipinzucheCarOrderPaytype selectById(Long id){
        return jipinzucheCarOrderPaytypeMapper.selectById(id);
    }

    public List<JipinzucheCarOrderPaytype> selectAll(){
        return jipinzucheCarOrderPaytypeMapper.selectAll();
    }

}
