<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/ixtech-base/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-order/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-product/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-agent-api/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-merchants/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-management/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-yudao-base/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-translation/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-vendor-basic/pom.xml" />
        <option value="$PROJECT_DIR$/ixtech-vendor-management/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_21" default="true" project-jdk-name="temurin-21" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>