package com.ixtech.global.common.utils;

import jodd.bean.BeanCopy;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

/**
 * Bean copy 工具类
 *
 * @author: JP
 * @date： 2025/3/28
 */
public class BeanUtils {

    public static void copyA2B(Object a, Object b) {
        if (a == null) {
            return;
        }
        BeanCopy.fromBean(a).toBean(b).ignoreNulls(true).copy();
    }

    public static <T> T copyA2B(Object a, Class<T> bClass) {
        if (a == null) {
            return null;
        }
        try {
            T t = bClass.newInstance();
            copyA2B(a, t);
            return t;
        } catch (Exception e) {
            throw new IllegalStateException("instance fail:" + bClass, e);
        }
    }

    public static <S, T> List<T> copyA2B(List<S> source, Class<T> targetType) {
        if (source == null) {
            return null;
        }
        return CollUtils.convertList(source, s -> copyA2B(s, targetType));
    }

    public static <S, T> List<T> toBean(List<S> source, Class<T> targetType, Consumer<T> peek) {
        List<T> list = copyA2B(source, targetType);
        if (list != null) {
            list.forEach(peek);
        }
        return list;
    }

    public static <A, B> List<B> parseFromAs2Bs(List<A> aList, Class<B> clazz) {
        if (CollectionUtils.isEmpty(aList)) {
            return Collections.<B>emptyList();
        }
        List<B> bList = new ArrayList<B>();
        for (A a : aList) {
            bList.add(copyA2B(a, clazz));
        }
        return bList;
    }

    /**
     * 序列化 反序列化深拷贝
     */
    public static <T> T deepCopy(Object a, Class<T> clazz) {
        if (a == null) {
            return null;
        }
        return JsonUtils.map2ObjectForMilliSec(JsonUtils.stringifyForMilliSec(a), clazz);
    }

    public static <T> List<T> deepCopyArray(Object a, Class<T> clazz) {
        if (a == null) {
            return null;
        }
        return JsonUtils.parseArrayForMilliSec(JsonUtils.stringifyForMilliSec(a), clazz);
    }
}
