package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PickupInformationResp {

    // 预订车型
    private String appointModel;
    // 实际提车（车牌号）
    private String actualPickupCar;
    // 提车里程
    private String pickupMileage;
    // 实际提车时间
    private String actualPickupTime;
    // 预计提车时间
    private String estimatedPickupTime;
    // 提车车型
    private String pickupModel;
    // 提车油/电量
    private String pickupFuel;
    // 经办人
    private String handler;

    /**
     * 车辆前方图片
     */
    private String frontImg;

    /**
     * 车辆后方图片
     */
    private String backImg;

    /**
     * 车辆右侧图片
     */
    private String rightImg;

    /**
     * 车辆左侧图片
     */
    private String leftImg;

    /**
     * etc图片
     */
    private List<String> etcImgs;
    /**
     * 油量图片
     */
    private List<String> fuleImgs;

    /**
     * 灯光检查图片
     */
    private List<String> lightImgs;

    /**
     * 车检证图片
     */
    private List<String> vehicleInspectionImgs;
    /**
     * 车损图片
     */
    private List<String> vehicleDamageImgs;

    /**
     * 车损说明
     */
    private String vehicleDamageDesc;
    // 押金金额
    private BigDecimal depositAmount;
    // 预授权方式
    private String preAuthMethod;
    // 有效天数
    private Integer validDays;
    // 授权时间
    private String authTime;

}
