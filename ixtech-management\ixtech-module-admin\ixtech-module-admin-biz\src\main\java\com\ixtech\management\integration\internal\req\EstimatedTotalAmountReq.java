package com.ixtech.management.integration.internal.req;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 预估总金额对象，包含金额和货币信息。
 */
@Data
public class EstimatedTotalAmountReq {

    /**
     * 金额，必填
     */
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    /**
     * 货币代码，必填
     */
    @NotEmpty(message = "货币代码不能为空")
    private String currency;
}
