package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 渠道详情响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:42
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChannelInfoResp {

    /**
     * 渠道id
     */
    private Long id;

    /**
     * 渠道简称
     */
    private String channelName;

    /**
     * 公司全称
     */
    private String companyFullName;

    /**
     * 报价货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    private String quoteCurrency;

    /**
     * 结算货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    private String settlementCurrency;

    /**
     * 结算模式 1：底价模式 2：抽佣模式
     */
    private Byte settlementMode;

    /**
     * 结算模式（字符串形式）
     */
    private String settlementModeStr;

    /**
     * 比例x%
     */
    private BigDecimal rate;

    /**
     * 渠道stage
     */
    private String stage;

    /**
     * 上下线状态 true：上线 false：下线
     */
    private Boolean active;

    /**
     * create_time in UTC
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private Date createTime;

}
