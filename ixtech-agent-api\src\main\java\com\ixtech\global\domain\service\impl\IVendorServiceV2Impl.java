package com.ixtech.global.domain.service.impl;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.domain.service.IVendorService;
import com.ixtech.global.integration.internal.client.VendorFeignClientV2;
import com.ixtech.global.integration.internal.req.RuleSearchReq;
import com.ixtech.global.integration.internal.req.VehicleSearchReq;
import com.ixtech.global.integration.internal.req.VendorSearchReq;
import com.ixtech.global.integration.internal.resp.LocationSearchResp;
import com.ixtech.global.integration.internal.resp.RuleSearchResp;
import com.ixtech.global.integration.internal.resp.VehicleSearchResp;
import com.ixtech.global.integration.internal.resp.VendorSearchResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("vendorServiceV2")
public class IVendorServiceV2Impl implements IVendorService {

    @Resource
    private VendorFeignClientV2 vendorFeignClientV2;

    @Override
    public ApiResponse<List<VendorSearchResp>> vendorSearch(VendorSearchReq request) {
        return vendorFeignClientV2.vendorSearch(request);
    }

    @Override
    public ApiResponse<List<LocationSearchResp>> locationSearch(VendorSearchReq request) {
        return vendorFeignClientV2.locationSearch(request);
    }

    @Override
    public ApiResponse<List<VehicleSearchResp>> vehicleSearch(VehicleSearchReq request) {
        return vendorFeignClientV2.vehicleSearch(request);
    }

    @Override
    public ApiResponse<List<RuleSearchResp>> ruleSearch(RuleSearchReq request) {
        return vendorFeignClientV2.ruleSearch(request);
    }
}
