package com.ixtech.global.redis;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;
import java.util.concurrent.TimeUnit;

/**
 * Redisson分布式锁工具service
 *
 * <AUTHOR>
 * @date 2025-3-12
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class RedisLockService {

    /**
     * 等待时间
     */
    private static final long WAIT_TIME = 10;

    /**
     * 持有锁时间
     */
    private static final long LOCK_RUNTIME = 5;

    private static final byte[] LOCK = new byte[0];

    private static final String LOCK_KEY = "API-LOCK";

    private final RedissonClient redissonClient;


    /**
     * synchronized 加锁执行
     */
    public void runSynchronized(Runnable runnable) {
        synchronized (LOCK) {
            runnable.run();
        }
    }

    /**
     * redission 加锁执行
     */
    public RedissonClient run(Runnable runnable) {
        return run(LOCK_KEY, runnable);
    }

    /**
     * Redis分布式加锁执行
     *
     * @param lockKey  锁key
     * @param runnable runnable
     * @return
     */
    public RedissonClient run(String lockKey, Runnable runnable) {
        return run(lockKey, WAIT_TIME, LOCK_RUNTIME, TimeUnit.SECONDS, runnable);
    }

    /**
     * Redis分布式加锁执行
     *
     * @param lockKey     锁key
     * @param lockRunTime 加锁时间
     * @param runnable    runnable
     * @return
     */
    public RedissonClient run(String lockKey, long lockRunTime, Runnable runnable) {
        return run(lockKey, WAIT_TIME, lockRunTime, TimeUnit.SECONDS, runnable);
    }

    /**
     * Redis分布式加锁执行
     *
     * @param lockKey         锁key
     * @param waitGetLockTime 获取锁的等待时间
     * @param lockRunTime     加锁时间
     * @param runnable        runnable
     * @return
     */
    public RedissonClient run(String lockKey, long waitGetLockTime, long lockRunTime, Runnable runnable) {
        return run(lockKey, waitGetLockTime, lockRunTime, TimeUnit.SECONDS, runnable);
    }

    /**
     * Redis分布式加锁执行
     *
     * @param lockKey         锁key
     * @param waitGetLockTime 获取锁的等待时间
     * @param lockRunTime     加锁时间
     * @param timeUnit        时间单位
     * @param runnable        runnable
     * @return
     */
    public RedissonClient run(String lockKey, long waitGetLockTime, long lockRunTime, TimeUnit timeUnit, Runnable runnable) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(waitGetLockTime, lockRunTime, timeUnit)) {
                runnable.run();
            }
        } catch (Exception e) {
            // log.info("LockUtil Runnable执行异常,{}",e);此处抛出错误,是为了全局捕获异常提示信息返回前端
            throw new IllegalArgumentException(e.getMessage());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return redissonClient;
    }

    //================================= boolean返回值 =============================

    /**
     * redission 加锁执行
     */
    public boolean run(Supplier<Boolean> supplier){
        return run(LOCK_KEY,supplier);
    }


    /**
     * Redis分布式加锁执行
     * @param lockKey 锁key
     * @param supplier supplier
     * @return
     */
    public boolean run(String lockKey, Supplier<Boolean> supplier){
        return run(lockKey, WAIT_TIME, LOCK_RUNTIME,TimeUnit.SECONDS,supplier);
    }

    /**
     * Redis分布式加锁执行
     * @param lockKey 锁key
     * @param lockRunTime 加锁时间
     * @param supplier supplier
     * @return
     */
    public boolean run(String lockKey,long lockRunTime, Supplier<Boolean> supplier){
        return run(lockKey, WAIT_TIME,lockRunTime,TimeUnit.SECONDS,supplier);
    }

    /**
     * Redis分布式加锁执行
     * @param lockKey 锁key
     * @param waitGetLockTime 获取锁的等待时间
     * @param lockRunTime 加锁时间
     * @param supplier run
     * @return
     */
    public boolean run(String lockKey,long waitGetLockTime,long lockRunTime, Supplier<Boolean> supplier){
        return run(lockKey,waitGetLockTime,lockRunTime,TimeUnit.SECONDS,supplier);
    }

    /**
     * Redis分布式加锁执行
     * @param lockKey 锁key
     * @param waitGetLockTime 获取锁的等待时间
     * @param lockRunTime 加锁时间
     * @param timeUnit 时间单位
     * @param supplier supplier
     * @return
     */
    public boolean run(String lockKey,long waitGetLockTime,long lockRunTime, TimeUnit timeUnit,Supplier<Boolean> supplier){
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(waitGetLockTime,lockRunTime, timeUnit)){
                return supplier.get();
            }
        }catch (Exception e){
            // log.info("LockUtil Supplier执行异常,{}",e); 此处抛出错误,是为了全局捕获异常提示信息返回前端
            throw new IllegalArgumentException(e.getMessage());
        }finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()){
                lock.unlock();
            }
        }
        return false;
    }

    /**
     * 获取 Redis 锁（可重入锁）
     *
     * @param key 键
     * @return Lock
     */
    public RLock getLock(String key) {
        return redissonClient.getLock(key);
    }

    /**
     * 获取 Redis 锁（公平锁）
     *
     * @param key 键
     * @return Lock
     */
    public RLock getFairLock(String key) {
        return redissonClient.getFairLock(key);
    }

    /**
     * 获取 Redis 锁（读写锁）
     *
     * @param key 键
     * @return RReadWriteLock
     */
    public RReadWriteLock getReadWriteLock(String key) {
        return redissonClient.getReadWriteLock(key);
    }

    /**
     * 获取 Redis 信号量
     *
     * @param key 键
     * @return RSemaphore
     */
    public RSemaphore getSemaphore(String key) {
        return redissonClient.getSemaphore(key);
    }

    /**
     * 获取 Redis 过期信号量
     * <p>
     * 基于Redis的Redisson的分布式信号量（Semaphore）Java对象RSemaphore采用了与java.util.concurrent.Semaphore相似的接口和用法。
     * 同时还提供了异步（Async）、反射式（Reactive）和RxJava2标准的接口。
     *
     * @param key 键
     * @return RPermitExpirableSemaphore
     */
    public RPermitExpirableSemaphore getPermitExpirableSemaphore(String key) {
        return redissonClient.getPermitExpirableSemaphore(key);
    }

    /**
     * 闭锁
     *
     * @param key 键
     * @return RCountDownLatch
     */
    public RCountDownLatch getCountDownLatch(String key) {
        return redissonClient.getCountDownLatch(key);
    }

    /**
     * 布隆过滤器
     *
     * @param key 键
     * @param <T> 存放对象
     * @return 返回结果
     */
    public <T> RBloomFilter<T> getBloomFilter(String key) {
        return redissonClient.getBloomFilter(key);
    }

}
