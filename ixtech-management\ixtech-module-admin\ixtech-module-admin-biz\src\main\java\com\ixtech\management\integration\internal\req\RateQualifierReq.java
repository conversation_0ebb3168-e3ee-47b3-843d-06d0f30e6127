package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 费率限定信息对象，包含可选的费率相关信息。
 */
@Data
public class RateQualifierReq {

    /**
     * 合同代码，可选
     */
    @JsonProperty("contract_code")
    private String contractCode;

    /**
     * 公司折扣号，可选
     */
    @JsonProperty("promotion_code")
    private String promotionCode;

    /**
     * 促销代码，可选
     */
    @JsonProperty("rate_category")
    private String rateCategory;

    /**
     * 费率类别，可选
     */
    @JsonProperty("rate_qualifier")
    private String rateQualifier;

    /**
     * 费率限定代码，可选
     */
    @JsonProperty("corp_discount_nmbr")
    private String corpDiscountNmbr;
}
