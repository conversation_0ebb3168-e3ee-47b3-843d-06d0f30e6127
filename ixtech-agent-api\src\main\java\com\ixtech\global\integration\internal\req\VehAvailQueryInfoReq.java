package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 门店地点req
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehAvailQueryInfoReq implements Serializable {

    private static final long serialVersionUID = 7985504938627085943L;

    /**
    * 客户驾驶员年龄
    */
    private Integer customerDriverAge;

     /**
      * 客户国籍代码
      */
     private String customerCitizenCountryCode;
}
