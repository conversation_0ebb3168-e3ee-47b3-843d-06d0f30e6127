package com.ixtech.global.common.utils;

import com.aliyun.oss.*;
import com.ixtech.global.common.exception.ServerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.OSS;
import com.aliyun.oss.common.auth.*;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import java.net.URL;
import com.aliyun.oss.model.OSSObject;

import java.io.*;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;

import static com.ixtech.global.common.exception.enums.CommonErrorCode.FILE_ERROR;

@Slf4j
@ConditionalOnClass(OSS.class)
@Component
public class FileUtils {

    @Value("${aliyun.oss.file.end-point}")
    private String endpoint;

    @Value("${aliyun.oss.file.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.oss.file.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.file.bucket-name}")
    private String bucketName;

    public String uploadFile(MultipartFile file, String path) throws Exception{
        // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
        String objectName = path;
        // 填写Bucket所在地域。以华东1（杭州）为例，Region填写为cn-hangzhou。
        String region = "cn-hongkong";

        log.info("信息为："+endpoint+"-"+accessKeyId+"-"+accessKeySecret);

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            InputStream inputStream = file.getInputStream();
            ossClient.putObject(bucketName, objectName, inputStream);
            // 设置图片有效时间 30年
            Date expiration = new Date(System.currentTimeMillis() + 946080000 * 1000);
            // 把上传的文件路径返回
            String url = ossClient.generatePresignedUrl(bucketName, objectName, expiration).toString();
            return url.substring(0, url.indexOf("?"));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
            throw ServerException.of(FILE_ERROR);
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
            throw ServerException.of(FILE_ERROR);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 从 OSS 下载文件到内存（不保存到本地）
     * @param objectKey OSS 文件路径（如 "exampledir/example.txt"）
     * @return 文件内容的字节数组
     */
    public byte[] downloadFileToMemory(String objectKey) throws Exception {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 获取文件流
            OSSObject ossObject = ossClient.getObject(bucketName, objectKey);
            try (InputStream inputStream = ossObject.getObjectContent()) {
                byte[] buffer = new byte[8192]; // 8KB 缓冲区
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            return outputStream.toByteArray();
        } catch (OSSException | ClientException e) {
            log.error("OSS 文件下载失败: {}", e.getMessage());
            throw ServerException.of(FILE_ERROR, "文件下载失败");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 获取 OSS 文件的输入流（需自行关闭）
     * @param objectKey OSS 文件路径
     * @return 文件输入流（调用方需负责关闭）
     */
    public InputStream downloadFileAsStream(String objectKey) throws Exception {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            OSSObject ossObject = ossClient.getObject(bucketName, objectKey);
            return new FilterInputStream(ossObject.getObjectContent()) {
                @Override
                public void close() throws IOException {
                    super.close();
                    ossClient.shutdown(); // 关闭 OSSClient
                }
            };
        } catch (Exception e) {
            ossClient.shutdown();
            throw ServerException.of(FILE_ERROR, "文件流获取失败");
        }
    }

    /**
     * 从 OSS 完整 URL 中提取 Bucket 名称和 Object Key
     * @param fullUrl 示例: "https://bucket-name.oss-cn-hangzhou.aliyuncs.com/path/to/file.jpg"
     * @return String[0]=Bucket名称, String[1]=Object Key
     */
    private String[] parseOssUrl(String fullUrl) {
        try {
            // 移除协议头和参数（如签名）
            String cleanUrl = fullUrl.split("\\?")[0];
            // 分割出 Bucket 和路径
            String[] parts = cleanUrl.replace("https://", "")
                    .replace("http://", "")
                    .split("\\.oss-[a-z0-9-]+\\.aliyuncs\\.com/", 2);
            if (parts.length != 2) {
                throw new IllegalArgumentException("无效的 OSS URL 格式");
            }
            return new String[]{parts[0], parts[1]};
        } catch (Exception e) {
            throw new IllegalArgumentException("OSS URL 解析失败: " + e.getMessage());
        }
    }

    /**
     * 通过完整 OSS URL 下载文件到内存
     * @param fullUrl 示例: "https://bucket-name.oss-cn-hangzhou.aliyuncs.com/path/to/file.jpg"
     * @return 文件内容的字节数组
     */
    public byte[] downloadFileFromUrl(String fullUrl) throws Exception {
        try {
            String[] bucketAndKey = parseOssUrl(fullUrl);
            return downloadFileToMemory(bucketAndKey[1]);
        } catch (IllegalArgumentException e) {
            log.error("OSS URL 解析失败: {}", fullUrl);
            throw ServerException.of(FILE_ERROR, "文件路径无效");
        }
    }

    /**
     * 上传byte数组到OSS并返回URL
     *
     * @param bytes 要上传的字节数组
     * @param objectName 存储在OSS上的文件名（可以包含路径）
     * @param expireTime URL过期时间（单位：毫秒）
     * @return 文件的访问URL
     */
    public String uploadBytesToOSS(byte[] bytes, String objectName, long expireTime) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 创建ByteArrayInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);

            // 创建PutObjectRequest对象
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream);

            // 上传文件
            ossClient.putObject(putObjectRequest);

            // 设置URL过期时间
            Date expiration = new Date(System.currentTimeMillis() + expireTime);

            // 生成URL
            URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);

            return url.toString();
        } finally {
            // 关闭OSSClient
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 上传byte数组到OSS并返回永久可访问的URL（需要设置Bucket为公共读）
     *
     * @param bytes 要上传的字节数组
     * @param objectName 存储在OSS上的文件名（可以包含路径）
     * @return 文件的访问URL
     */
    public String uploadBytesToOSSWithPublicUrl(byte[] bytes, String objectName) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 创建ByteArrayInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);

            // 创建PutObjectRequest对象
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream);

            // 上传文件
            ossClient.putObject(putObjectRequest);

            // 生成公共读URL（前提是Bucket已经设置为公共读）
            String url = "https://" + bucketName + "." + endpoint.replace("https://", "") + "/" + objectName;

            return url;
        } finally {
            // 关闭OSSClient
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


    public String uploadFileByOutPutStream(ByteArrayOutputStream byteArrayOutputStream, String path) throws Exception{
        // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
        String objectName = path;
        // 填写Bucket所在地域。以华东1（杭州）为例，Region填写为cn-hangzhou。
        String region = "cn-hongkong";

        log.info("信息为："+endpoint+"-"+accessKeyId+"-"+accessKeySecret);

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            InputStream inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            ossClient.putObject(bucketName, objectName, inputStream);
            // 设置图片有效时间 30年
            Date expiration = new Date(System.currentTimeMillis() + 946080000 * 1000);
            // 把上传的文件路径返回
            String url = ossClient.generatePresignedUrl(bucketName, objectName, expiration).toString();
            return url.substring(0, url.indexOf("?"));
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
            throw ServerException.of(FILE_ERROR);
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
            throw ServerException.of(FILE_ERROR);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
}