apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-service
  namespace: default
  labels:
    app: agent-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-service
  template:
    metadata:
      labels:
        app: agent-service
    spec:
      containers:
        - name: agent-service
          image: ${IMAGE}
          ports:
            - containerPort: 8080
#        resources:
#          limits:
#            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: agent-service
  namespace: default
  labels:
    app: agent-service
spec:
  selector:
    app: agent-service
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 8080
    - name: https
      protocol: TCP
      port: 443
      targetPort: 8080
#  type: NodePort
