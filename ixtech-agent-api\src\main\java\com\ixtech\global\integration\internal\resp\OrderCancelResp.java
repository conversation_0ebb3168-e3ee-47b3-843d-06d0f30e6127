package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单响应对象
 * 用于返回订单相关的响应数据
 *
 * @since 2025-03-25
 */
@Data
public class OrderCancelResp {

    /**
     * 取消金金额
     * 表示订单取消时需要支付的金额
     */
    @JsonProperty("cancel_penalty_amount")
    private BigDecimal cancelPenaltyAmount;

    /**
     * 取消金货币代码
     * 表示取消金额的货币单位（如 USD, CNY）
     */
    @JsonProperty("cancel_penalty_currency")
    private String cancelPenaltyCurrency;

    /**
     * IX平台订单code
     * IX平台生成的订单标识
     */
    @JsonProperty("ix_order_code")
    private String ixOrderCode;

    /**
     * IX平台订单确认码
     * IX平台生成的订单确认标识
     */
    @JsonProperty("ix_confirm_code")
    private String ixConfirmCode;

}
