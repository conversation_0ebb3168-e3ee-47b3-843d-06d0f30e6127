package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 费用类型枚举
 * 对应表: platform_order_charge_detail, 字段: charge_type
 * (1-租金，2-保险，3-附加设备，4-取车门店加班费，5-还车门店加班费，6-异地取还，7-年龄费用，8-里程费用，9-押金)
 */
@Getter
@AllArgsConstructor
public enum OrderChargeTypeEnum implements DictInf {

    RENTAL_FEE(1, "租金"),
    INSURANCE(2, "保险"),
    ADDITIONAL_EQUIPMENT(3, "附加设备"),
    PICKUP_STORE_OVERTIME_FEE(4, "取车门店加班费"),
    DROPOFF_STORE_OVERTIME_FEE(5, "还车门店加班费"),
    OFFSITE_PICKUP_DROPOFF(6, "异地取还"),
    AGE_FEE(7, "年龄费用"),
    MILEAGE_FEE(8, "里程费用"),
    DEPOSIT(9, "押金"),
    FUEL_FEE(10,"燃油费"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static OrderChargeTypeEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
