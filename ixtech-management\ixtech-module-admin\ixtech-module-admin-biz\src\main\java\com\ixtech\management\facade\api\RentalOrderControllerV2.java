package com.ixtech.management.facade.api;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.domain.service.RentalOrderService;
import com.ixtech.management.integration.internal.req.MerchantRentalOrderDetailReq;
import com.ixtech.management.integration.internal.req.OrderPageQueryReq;
import com.ixtech.management.integration.internal.req.OrderTransferReq;
import com.ixtech.management.integration.internal.req.common.SelectOption;
import com.ixtech.management.integration.internal.req.common.SelectOptionReq;
import com.ixtech.management.integration.internal.resp.MerchantRentalOrderResp;
import com.ixtech.management.integration.internal.resp.OrderQueryResp;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 车辆信息接口
 */
@RestController
@RequestMapping("/v2/managementsrv/api/rental_order")
public class RentalOrderControllerV2 {

    @Resource
    private RentalOrderService rentalOrderService;
    @PostMapping("/list")
    public ApiResponse<PageResult<OrderQueryResp>> list(@RequestBody OrderPageQueryReq req) {
        return rentalOrderService.listByPage(req);
    }

    /**
     * 门店下拉框
     */
    @PostMapping("/store_list")
    public ApiResponse<List<SelectOption>> storeList(@RequestBody SelectOptionReq req) {
        return rentalOrderService.storeList(req);
    }

    /**
     * 车型组下拉框
     */
    @PostMapping("/vehicle_group_list")
    public ApiResponse<List<SelectOption>> vehicheGroupList(@RequestBody SelectOptionReq req) {
        return rentalOrderService.vehicleGroupList(req);
    }


    /**
     * 订单详情
     * @param req
     * @return
     */
    @PostMapping("/channel_list")
    public ApiResponse<List<SelectOption>> channelList(@RequestBody SelectOptionReq req) {
        return rentalOrderService.getChannelList(req);
    }


    /**
     * 订单详情
     * @param req
     * @return
     */
    @PostMapping("/detail")
    public ApiResponse<MerchantRentalOrderResp> getOrderDetail(@RequestBody MerchantRentalOrderDetailReq req) {
        return rentalOrderService.getOrderDetail(req);
    }

    /**
     * 订单迁移接口
     * @param req
     * @return
     */
    @PostMapping("/history_order_transfer")
    public ApiResponse<Boolean> orderTransfer(@RequestBody OrderTransferReq req) {
        return rentalOrderService.orderTransfer(req);
    }

}
