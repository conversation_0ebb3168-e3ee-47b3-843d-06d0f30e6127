package com.ixtech.management.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum OrderTypeEnum {
    /**
     * 1-满油取还
     */
    PTDD("普通订单",1),

    /**
     * 2-同油量取还
     */
    SZ("闪租",2),

    WYZ("无忧租",3);


    private final String name;
    private final int code;

    private static final Map<Integer, OrderTypeEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(OrderTypeEnum::getCode, Function.identity()));

    private static final Map<String, OrderTypeEnum> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(OrderTypeEnum::getName, Function.identity()));

    OrderTypeEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 对应的枚举，找不到返回null
     */
    public static OrderTypeEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据名称获取枚举
     * @param name 名称
     * @return 对应的枚举，找不到返回null
     */
    public static OrderTypeEnum getByName(String name) {
        return NAME_MAP.get(name);
    }

    /**
     * 检查编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    public static boolean containsCode(Integer code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 检查名称是否存在
     * @param name 名称
     * @return 是否存在
     */
    public static boolean containsName(String name) {
        return NAME_MAP.containsKey(name);
    }

    /**
     * 获取所有编码
     * @return 编码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(values()).map(OrderTypeEnum::getCode).toArray(Integer[]::new);
    }

    /**
     * 获取所有名称
     * @return 名称数组
     */
    public static String[] getAllNames() {
        return Arrays.stream(values()).map(OrderTypeEnum::getName).toArray(String[]::new);
    }

    @Override
    public String toString() {
        return "InsuranceEnum{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
