C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\annotation\I18nField.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\constant\I18nConstants.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\feign\client\TranslationFeignClient.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\config\I18nAutoConfiguration.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\cache\I18nTranslationCacheInitializer.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\annotation\I18nTranslation.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\interceptor\I18nValidateMessageInterpolator.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\config\TranslationFeignClientConfiguration.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\util\RequestUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\aop\I18nResponseBodyAdvice.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\aop\I18nTranslationAopAspect.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\feign\req\TranslationReq.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\cache\I18nTranslationCacheManager.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\config\I18nProperties.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\I18nTranslationExecutor.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\feign\resp\TranslationResp.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\interceptor\AcceptLanguageFilter.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\interceptor\AcceptLanguageFeignInterceptor.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-starters\ixtech-base-i18n-starter\src\main\java\com\ixtech\global\interceptor\LanguageContextHolder.java
