package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 车辆租赁核心信息 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehRentalCoreResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 取车时间
  */
 private String pickUpDateTime;

 /**
  * 还车时间
  */
 private String returnDateTime;

 /**
  * 取车地点
  */
 private LocationResp pickupLocation;

 /**
  * 还车地点
  */
 private LocationResp returnLocation;
}
