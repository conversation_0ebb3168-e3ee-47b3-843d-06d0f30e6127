package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 司机分配状态枚举
 *
 * <AUTHOR> hu
 * @date 2025/7/23
 */
@Getter
public enum TransferDispatchStatusEnum {

    UNASSIGNED(1, "未分配"),
    ASSIGNED(2, "已分配"),
    ;

    TransferDispatchStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 分配状态编码
     */
    private final Integer code;

    /**
     * 分配状态描述
     */
    private final String value;
}
    