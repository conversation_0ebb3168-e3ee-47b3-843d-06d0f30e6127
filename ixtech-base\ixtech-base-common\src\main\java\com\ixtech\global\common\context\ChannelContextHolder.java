package com.ixtech.global.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.ixtech.global.common.dto.IxChannelInfo;

/**
 * 渠道 Holder
 */
public class ChannelContextHolder {

    public static final String CHANNEL_ID_HEADER = "Channel-Id";
    public static final String CHANNEL_CODE_HEADER = "Channel-Code"; // 可以考虑增加渠道代码的 Header 常量

    /**
     * 当前渠道编号
     */
    private static final ThreadLocal<Long> CHANNEL_ID = new TransmittableThreadLocal<>();

    /**
     * 当前渠道信息
     */
    private static final ThreadLocal<IxChannelInfo> CHANNEL_INFO = new TransmittableThreadLocal<>();

    /**
     * 当前渠道代码
     */
    private static final ThreadLocal<String> CHANNEL_CODE = new TransmittableThreadLocal<>(); // 步骤 1: 新增 ThreadLocal

    /**
     * 获得渠道编号
     *
     * @return 渠道编号
     */
    public static Long getChannelId() {
        return CHANNEL_ID.get();
    }

    /**
     * 获得渠道编号。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 渠道编号
     */
    public static Long getRequiredChannelId() {
        Long channelId = getChannelId();
        if (channelId == null) {
            throw new NullPointerException("ChannelContextHolder 不存在渠道编号！");
        }
        return channelId;
    }

    /**
     * 设置渠道编号
     *
     * @param channelId 渠道编号
     */
    public static void setChannelId(Long channelId) {
        CHANNEL_ID.set(channelId);
    }

    /**
     * 获得渠道信息
     *
     * @return 渠道信息
     */
    public static IxChannelInfo getChannelInfo() {
        return CHANNEL_INFO.get();
    }

    /**
     * 获得渠道信息。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 渠道信息
     */
    public static IxChannelInfo getRequiredChannelInfo() {
        IxChannelInfo channelInfo = getChannelInfo();
        if (channelInfo == null) {
            throw new NullPointerException("ChannelContextHolder 不存在渠道信息！");
        }
        return channelInfo;
    }

    /**
     * 设置渠道信息
     *
     * @param channelInfo 渠道信息
     */
    public static void setChannelInfo(IxChannelInfo channelInfo) {
        CHANNEL_INFO.set(channelInfo);
    }

    /**
     * 获得渠道代码
     *
     * @return 渠道代码
     */
    public static String getChannelCode() { // 步骤 2: 新增 getChannelCode 方法
        return CHANNEL_CODE.get();
    }

    /**
     * 获得渠道代码。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 渠道代码
     */
    public static String getRequiredChannelCode() { // 步骤 3: 新增 getRequiredChannelCode 方法
        String channelCode = getChannelCode();
        if (channelCode == null) {
            throw new NullPointerException("ChannelContextHolder 不存在渠道代码！");
        }
        return channelCode;
    }

    /**
     * 设置渠道代码
     *
     * @param channelCode 渠道代码
     */
    public static void setChannelCode(String channelCode) { // 步骤 4: 新增 setChannelCode 方法
        CHANNEL_CODE.set(channelCode);
    }


    /**
     * 清除上下文
     */
    public static void clear() {
        CHANNEL_ID.remove();
        CHANNEL_INFO.remove();
        CHANNEL_CODE.remove(); // 步骤 5: 修改 clear 方法
    }
}