package com.ixtech.global.common.util;

import com.ixtech.global.common.dto.ApiResponse;

public class ErrorRespUtils {
    /**
     * 检查服务调用结果，失败时抛出异常
     *
     * @return
     */
    public static  <T> ApiResponse<T> checkServiceResponse(ApiResponse<T> response, String errorMessage) {
        if (response == null || !response.isSuccess()) {
            assert response != null;
            return ApiResponse.fail(response.getError().getCode(), errorMessage);
        }
        return response;
    }
}
