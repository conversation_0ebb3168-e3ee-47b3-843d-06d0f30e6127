package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 门店
 *
 * <AUTHOR>
 * @since 2025-03-24 18:52:13
 */
@Data
public class JipinzucheStoreDTO {

    @JsonProperty("id")
    private Long id; // 修改：从 Integer 改为 Long

    /**
     * 国家id
     */
    @JsonProperty("country_id")
    private Long countryid; // 修改：从 Integer 改为 Long

    /**
     * 省id
     */
    @JsonProperty("province_id")
    private Long provinceid; // 修改：从 Integer 改为 Long

    /**
     * 城市id
     */
    @JsonProperty("city_id")
    private Long cityid; // 修改：从 Integer 改为 Long

    /**
     * 区id
     */
    @JsonProperty("county_id")
    private Long countyid; // 修改：从 Integer 改为 Long

    /**
     * 详细地址
     */
    @JsonProperty("address")
    private String address;

    /**
     * 纬度
     */
    @JsonProperty("lat")
    private BigDecimal lat;

    /**
     * 经度
     */
    @JsonProperty("lon")
    private BigDecimal lon;

    /**
     * 门店名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 门店图片
     */
    @JsonProperty("litpic")
    private String litpic;

    /**
     * 地理位置-小图
     */
    @JsonProperty("map_img_small")
    private String mapImgSmall;

    /**
     * 地理位置-大图
     */
    @JsonProperty("map_img_large")
    private String mapImgLarge;

    /**
     * 简介
     */
    @JsonProperty("intro")
    private String intro;

    /**
     * 负责人
     */
    @JsonProperty("principal")
    private String principal;

    /**
     * 负责人电话
     */
    @JsonProperty("principal_mobile")
    private String principalMobile;

    /**
     * 货币单位
     */
    @JsonProperty("currency_unit")
    private String currencyUnit;

    /**
     * 单日里程限制km(ps:0->不限制)
     */
    @JsonProperty("day_mileage")
    private BigDecimal dayMileage;

    /**
     * 门店代码(唯一)
     */
    @JsonProperty("code")
    private String code;

    /**
     * 营业时间
     */
    @JsonProperty("open_time")
    private String opentime;

    /**
     * 1->城市；2->机场；3->酒店
     */
    @JsonProperty("type")
    private Integer type;

    /**
     * 机场代码
     */
    @JsonProperty("iata")
    private String iata;

    /**
     * 邮政编码
     */
    @JsonProperty("postal_code")
    private String postalCode;

    /**
     * 取车指引
     */
    @JsonProperty("how_to_get")
    private String howToGet;

    /**
     * 还车指引
     */
    @JsonProperty("how_to_go")
    private String howToGo;

    /**
     * 汇率(人民币：当地货币)
     */
    @JsonProperty("exchange_rate")
    private BigDecimal exchangeRate;

    /**
     * 最短租期
     */
    @JsonProperty("min_rent_days")
    private Integer minRentDays;

    /**
     * 租车订单前缀
     */
    @JsonProperty("order_code_pre")
    private String ordercodePre;

    /**
     * 添加时间
     */
    @JsonProperty("time")
    private LocalDateTime time; // 修改：从 Integer 改为 LocalDateTime

    /**
     * ip
     */
    @JsonProperty("ip")
    private String ip;

    /**
     * 添加人
     */
    @JsonProperty("mid")
    private Long mid; // 修改：从 Integer 改为 Long

    /**
     * 1->有效；-1->删除
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 超出部分多少钱1公里
     */
    @JsonProperty("every_price")
    private BigDecimal everyPrice;

    /**
     * 货币单位
     */
    @JsonProperty("monetary_unit")
    private String monetaryUnit;

    /**
     * 公里单位
     */
    @JsonProperty("kilometers")
    private String kilometers;

    /**
     * 英文门店名
     */
    @JsonProperty("en_name")
    private String enname;

    /**
     * 英文门店地址
     */
    @JsonProperty("en_address")
    private String enaddress;

    /**
     * 工作日可加班时间晚上
     */
    @JsonProperty("weekday_evening")
    private String weekdayevening;

    /**
     * 工作日可加班时间早上
     */
    @JsonProperty("weekday_morning")
    private String weekdaymoring;

    /**
     * 非营业可加班时间收费标准多少钱一次
     */
    @JsonProperty("fee_scale")
    private BigDecimal feescale;

    /**
     * 非工作日可加班时间
     */
    @JsonProperty("weekend_evening")
    private String weekendevening;

    /**
     * 非工作日可加班时间晚上
     */
    @JsonProperty("weekend_morning")
    private String weekendmoring;

    /**
     * 携程超时费用标准化
     */
    @JsonProperty("standard_charge")
    private String standardcharge;

    /**
     * 1->在接口中显示门店信息 -1->在接口中不显示门店信息
     */
    @JsonProperty("display_status")
    private Integer displaystatus;

    /**
     * 儿童座椅费用
     */
    @JsonProperty("child_seat")
    private BigDecimal childseat;

    /**
     * 还车时间延迟
     */
    @JsonProperty("time_delay")
    private BigDecimal timeDelay;

    /**
     * pos机支持的卡种
     */
    @JsonProperty("card_type")
    private String cardType;

    /**
     * 该门店是否支持vcc订单 0->不支持 1->支持
     */
    @JsonProperty("is_vcc")
    private Integer isVcc;

    /**
     * 刷信誉权的额度区间
     */
    @JsonProperty("quota_interval")
    private String quotaInterval;

    /**
     * 财务BI链接
     */
    @JsonProperty("url")
    private String url;

    /**
     * 订单条款
     */
    @JsonProperty("order_terms")
    private String orderTerms;

    /**
     * 注意事项
     */
    @JsonProperty("order_precautions")
    private String orderPrecautions;

    /**
     * 是否立即确认 0->否；1->是
     */
    @JsonProperty("confirm_now")
    private String confirmNow;

    /**
     * 最短提前预定时间
     */
    @JsonProperty("min_book_hour")
    private Integer minBookHour;

    /**
     * 系统专用货币单位
     */
    @JsonProperty("local_currency_unit")
    private String localcurrencyUnit;
}
