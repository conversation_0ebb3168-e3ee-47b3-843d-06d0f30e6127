<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.VendorMapper">
    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.Vendor">
        <!--@mbg.generated-->
        <!--@Table ix_vendor-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="active" jdbcType="BIT" property="active"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="company_full_name" jdbcType="VARCHAR" property="companyFullName"/>
        <result column="quote_currency" jdbcType="VARCHAR" property="quoteCurrency"/>
        <result column="settlement_currency" jdbcType="VARCHAR" property="settlementCurrency"/>
        <result column="settlement_mode" jdbcType="TINYINT" property="settlementMode"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="principal" jdbcType="VARCHAR" property="principal"/>
        <result column="contact_number" jdbcType="VARCHAR" property="contactNumber"/>
        <result column="contact_number_code" jdbcType="VARCHAR" property="contactNumberCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, active, deleted, create_time, update_time, code, `name`, logo, description, company_full_name,
        quote_currency, settlement_currency, settlement_mode, rate, principal, contact_number, contact_number_code
    </sql>
    <sql id="Ignore_Deleted">
        deleted = 0
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from ix_vendor
        where id = #{id,jdbcType=BIGINT} AND
        <include refid="Ignore_Deleted"/>
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ixtech.management.repo.entity.Vendor"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into ix_vendor (active, deleted, create_time,
        update_time, code, `name`,
        logo, description, company_full_name,
        quote_currency, settlement_currency, settlement_mode,
        rate, principal, contact_number, contact_number_code
        )
        values (#{active,jdbcType=BIT}, #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
        #{logo,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{companyFullName,jdbcType=VARCHAR},
        #{quoteCurrency,jdbcType=VARCHAR}, #{settlementCurrency,jdbcType=VARCHAR}, #{settlementMode,jdbcType=TINYINT},
        #{rate,jdbcType=DECIMAL}, #{principal,jdbcType=VARCHAR}, #{contactNumber,jdbcType=VARCHAR}, #{contactNumberCode,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ixtech.management.repo.entity.Vendor" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into ix_vendor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="active != null">
                active,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="logo != null">
                logo,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="companyFullName != null">
                company_full_name,
            </if>
            <if test="quoteCurrency != null">
                quote_currency,
            </if>
            <if test="settlementCurrency != null">
                settlement_currency,
            </if>
            <if test="settlementMode != null">
                settlement_mode,
            </if>
            <if test="rate != null">
                rate,
            </if>
            <if test="principal != null">
                principal,
            </if>
            <if test="contactNumber != null">
                contact_number,
            </if>
            <if test="contactNumberCode != null">
                contact_number_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="active != null">
                #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="logo != null">
                #{logo,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="companyFullName != null">
                #{companyFullName,jdbcType=VARCHAR},
            </if>
            <if test="quoteCurrency != null">
                #{quoteCurrency,jdbcType=VARCHAR},
            </if>
            <if test="settlementCurrency != null">
                #{settlementCurrency,jdbcType=VARCHAR},
            </if>
            <if test="settlementMode != null">
                #{settlementMode,jdbcType=TINYINT},
            </if>
            <if test="rate != null">
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="principal != null">
                #{principal,jdbcType=VARCHAR},
            </if>
            <if test="contactNumber != null">
                #{contactNumber,jdbcType=VARCHAR},
            </if>
            <if test="contactNumberCode != null">
                #{contactNumberCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ixtech.management.repo.entity.Vendor">
        <!--@mbg.generated-->
        update ix_vendor
        <set>
            <if test="active != null">
                active = #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="logo != null">
                logo = #{logo,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="companyFullName != null">
                company_full_name = #{companyFullName,jdbcType=VARCHAR},
            </if>
            <if test="quoteCurrency != null">
                quote_currency = #{quoteCurrency,jdbcType=VARCHAR},
            </if>
            <if test="settlementCurrency != null">
                settlement_currency = #{settlementCurrency,jdbcType=VARCHAR},
            </if>
            <if test="settlementMode != null">
                settlement_mode = #{settlementMode,jdbcType=TINYINT},
            </if>
            <if test="rate != null">
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="principal != null">
                principal = #{principal,jdbcType=VARCHAR},
            </if>
            <if test="contactNumber != null">
                contact_number = #{contactNumber,jdbcType=VARCHAR},
            </if>
            <if test="contactNumberCode != null">
                contact_number_code = #{contactNumberCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ixtech.management.repo.entity.Vendor">
        <!--@mbg.generated-->
        update ix_vendor
        set active = #{active,jdbcType=BIT},
        deleted = #{deleted,jdbcType=BIT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        code = #{code,jdbcType=VARCHAR},
        `name` = #{name,jdbcType=VARCHAR},
        logo = #{logo,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        company_full_name = #{companyFullName,jdbcType=VARCHAR},
        quote_currency = #{quoteCurrency,jdbcType=VARCHAR},
        settlement_currency = #{settlementCurrency,jdbcType=VARCHAR},
        settlement_mode = #{settlementMode,jdbcType=TINYINT},
        rate = #{rate,jdbcType=DECIMAL},
        principal = #{principal,jdbcType=VARCHAR},
        contact_number = #{contactNumber,jdbcType=VARCHAR}
        contact_number_code = #{contactNumberCode,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update ix_vendor
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.active,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.code,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="logo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.logo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="company_full_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.companyFullName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quote_currency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.quoteCurrency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="settlement_currency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.settlementCurrency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="settlement_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.settlementMode,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rate,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="principal = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.principal,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="contact_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.contactNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="contact_number_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.contactNumberCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update ix_vendor
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.active != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.active,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deleted != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.deleted,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.code != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.code,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="logo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.logo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.logo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.description != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="company_full_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.companyFullName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.companyFullName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="quote_currency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.quoteCurrency != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.quoteCurrency,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="settlement_currency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.settlementCurrency != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.settlementCurrency,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="settlement_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.settlementMode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.settlementMode,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.rate != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.rate,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="principal = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.principal != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.principal,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contact_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contactNumber != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.contactNumber,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contact_number_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contactNumberCode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.contactNumberCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into ix_vendor
        (active, deleted, create_time, update_time, code, `name`, logo, description, company_full_name,
        quote_currency, settlement_currency, settlement_mode, rate, principal, contact_number, contact_number_code
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.active,jdbcType=BIT}, #{item.deleted,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.logo,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
            #{item.companyFullName,jdbcType=VARCHAR},
            #{item.quoteCurrency,jdbcType=VARCHAR}, #{item.settlementCurrency,jdbcType=VARCHAR},
            #{item.settlementMode,jdbcType=TINYINT}, #{item.rate,jdbcType=DECIMAL}, #{item.principal,jdbcType=VARCHAR},
            #{item.contactNumber,jdbcType=VARCHAR}, #{item.contactNumberCode,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectByPrimaryKeyList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ix_vendor
        <where>
            id IN
            <foreach item="item" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND
            <include refid="Ignore_Deleted"/>
        </where>
    </select>

    <select id="searchByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ix_vendor
        <where>
            <include refid="Ignore_Deleted"/>
            <if test="id != null">
                AND id = #{id,jdbcType=BIGINT}
            </if>
            <if test="name != null and name !=''">
                AND `name` LIKE CONCAT('%', REPLACE(#{name,jdbcType=VARCHAR}, '_', '\\_'), '%')
            </if>
            <if test="companyFullName != null and companyFullName !=''">
                AND company_full_name LIKE CONCAT('%', REPLACE(#{companyFullName,jdbcType=VARCHAR}, '_', '\\_'), '%')
            </if>
        </where>
        ORDER BY id DESC
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ix_vendor
        <where>
            active = 1 and deleted = 0
        </where>
    </select>

</mapper>