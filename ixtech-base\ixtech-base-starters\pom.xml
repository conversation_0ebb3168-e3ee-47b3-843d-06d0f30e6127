<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ixtech.global</groupId>
        <artifactId>ixtech-base-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../ixtech-base-parent/pom.xml</relativePath>
    </parent>

    <artifactId>ixtech-base-starters</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>ixtech-base-mysql-starter</module>
        <module>ixtech-base-redis-starter</module>
        <module>ixtech-base-feign-starter</module>
        <module>ixtech-base-i18n-starter</module>
    </modules>

    <properties>
        <project.basedir>${basedir}/..</project.basedir>
    </properties>

    <dependencies>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
