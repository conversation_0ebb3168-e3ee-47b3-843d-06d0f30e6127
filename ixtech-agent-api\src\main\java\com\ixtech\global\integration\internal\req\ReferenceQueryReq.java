package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 车辆简单详情req
 *
 * <AUTHOR>
 * @date 2025/3/28
 */
@Data
public class ReferenceQueryReq {

    /**
     * 搜索唯一的维度：供应商code:门店code:SIPP:车辆modelId:保险code
     */
    @NotBlank
    @JsonProperty("reference_id")
    private String referenceId;

    /**
     * 取车时间
     */
    @NotBlank(message = "取车时间不能为空")
    @JsonProperty("pick_up_date_time")
    private String pickUpDateTime;

    /**
     * 还车时间
     */
    @NotBlank(message = "还车时间 不能为空")
    @JsonProperty("return_date_time")
    private String returnDateTime;

    /**
     * 取车地点
     */
    @NotNull(message = "取车地点 不能为空")
    @JsonProperty("pickup_location")
    private LocationReq pickupLocation;

    /**
     * 还车地点
     */
    @NotNull(message = "还车地点 不能为空")
    @JsonProperty("return_location")
    private LocationReq returnLocation;
}
