package com.ixtech.management.repo.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 订单-客户信息表
 * @TableName ix_car_order_customer
 */
@Data
public class IxCarOrderCustomer implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * 订单id
     */
    private Long carOrderId;

    /**
     * 司机年龄（必填）
     */
    private Integer driverAge;

    /**
     * 客户国籍代码（ISO3166 标准）（必填）
     */
    private String citizenCountryCode;

    /**
     * 电子邮件地址（必填）
     */
    private String emailAddress;

    /**
     * 名字（必填）
     */
    private String givenName;

    /**
     * 姓氏（必填）
     */
    private String surname;

    /**
     * 区号（必填）
     */
    private String mobileAreaCityCode;

    /**
     * 电话号码（必填）
     */
    private String mobilePhoneNumber;

    private static final long serialVersionUID = 1L;
}