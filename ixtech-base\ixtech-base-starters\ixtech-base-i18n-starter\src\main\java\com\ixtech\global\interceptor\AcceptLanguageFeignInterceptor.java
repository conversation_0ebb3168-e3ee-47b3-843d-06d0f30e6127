package com.ixtech.global.interceptor;

import com.ixtech.global.constant.I18nConstants;
import com.ixtech.global.util.RequestUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpHeaders;

import java.util.List;

/**
 * feign调用时传递Accept-Language请求头
 *
 * <AUTHOR> hu
 * @date 2025/6/9 13:34
 */
@Slf4j
public class AcceptLanguageFeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {

        // 检查是否已存在 Accept-Language 请求头（避免覆盖显式指定的值）
        if (requestTemplate.headers().containsKey(HttpHeaders.ACCEPT_LANGUAGE)) {
            // 已指定，不处理
            return;
        }

        // 从当前请求上下文中获取 Accept-Language
        List<RequestUtils.LanguagePriority> languagePriorities = LanguageContextHolder.getLanguages();
        if (CollectionUtils.isEmpty(languagePriorities)) {
            return;
        }

        // 添加 Accept-Language 请求头到 Feign 请求中
        StringBuilder acceptLanguage = new StringBuilder();
        for (RequestUtils.LanguagePriority languagePriority : languagePriorities) {
            acceptLanguage.append(languagePriority.languageCode())
                    .append(I18nConstants.LANG_SEPARATOR)
                    .append(languagePriority.quality())
                    .append(I18nConstants.DOT);
        }
        acceptLanguage.deleteCharAt(acceptLanguage.length() - 1);
        requestTemplate.header(HttpHeaders.ACCEPT_LANGUAGE, acceptLanguage.toString());

    }

}
