package com.ixtech.global.domain.service;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.integration.internal.req.RuleSearchReq;
import com.ixtech.global.integration.internal.req.VehicleSearchReq;
import com.ixtech.global.integration.internal.req.VendorSearchReq;
import com.ixtech.global.integration.internal.resp.LocationSearchResp;
import com.ixtech.global.integration.internal.resp.RuleSearchResp;
import com.ixtech.global.integration.internal.resp.VehicleSearchResp;
import com.ixtech.global.integration.internal.resp.VendorSearchResp;

import java.util.List;

/**
 * 供应商相关接口
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IVendorService {

    /**
     * 供应商搜索
     *
     * @param request 供应商查询请求参数
     * @return 包含供应商搜索结果的 ApiResponse
     */
    ApiResponse<List<VendorSearchResp>> vendorSearch(VendorSearchReq request);

    /**
     * 门店搜索
     * 目前查询所有门店
     *
     * @param request 供应商查询请求参数
     * @return 包含门店搜索结果的 ApiResponse
     */
    ApiResponse<List<LocationSearchResp>> locationSearch(VendorSearchReq request);

    /**
     * 门店车型搜索
     *
     * @param request 车型查询请求参数
     * @return 包含车型搜索结果的 ApiResponse
     */
    ApiResponse<List<VehicleSearchResp>> vehicleSearch(VehicleSearchReq request);

    /**
     * 规则搜索
     *
     * @param request 规则查询请求参数
     * @return 包含规则搜索结果的 ApiResponse
     */
    ApiResponse<List<RuleSearchResp>> ruleSearch(RuleSearchReq request);
}
