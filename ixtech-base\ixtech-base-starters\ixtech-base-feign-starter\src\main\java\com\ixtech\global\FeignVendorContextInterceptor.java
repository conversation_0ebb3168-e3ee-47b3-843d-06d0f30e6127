package com.ixtech.global;

import com.ixtech.global.common.context.ChannelContextHolder;
import com.ixtech.global.common.context.VendorContextHolder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.util.ObjectUtils;

/**
 * 渠道上下文 feign拦截器
 */
public class FeignVendorContextInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        // vendorId 从上下文中获取
        Long vendorId = VendorContextHolder.getVendorId();
        if (vendorId != null) {
            template.header(VendorContextHolder.VENDOR_ID_HEADER, vendorId.toString());
        }

        // 步骤 1 & 2: 获取并设置 userId
        Long userId= VendorContextHolder.getUserId();
        if (userId != null) { // 检查userId是否为空
            template.header(VendorContextHolder.USER_ID_HEADER, userId.toString());
        }
    }
}
