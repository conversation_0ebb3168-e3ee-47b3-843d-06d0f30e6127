package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单详情
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MerchantRentalOrderDetailReq {

    @NotNull(message = "订单id不能为空")
    private Long id;

    @NotNull(message = "供应商id不能为空")
    private Long vendorId;

    private String orderCode;

    private String creatorName;
}
