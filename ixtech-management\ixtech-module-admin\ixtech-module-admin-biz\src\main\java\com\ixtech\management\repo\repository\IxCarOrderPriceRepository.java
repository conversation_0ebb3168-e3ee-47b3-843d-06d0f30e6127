package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.IxCarOrderPrice;
import com.ixtech.management.repo.mapper.IxCarOrderPriceMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class IxCarOrderPriceRepository {

    @Resource
    private IxCarOrderPriceMapper ixCarOrderPriceMapper;

    public IxCarOrderPrice selectByCarOrderId(Long carOrderId) {
        return ixCarOrderPriceMapper.selectByCarOrderId(carOrderId);
    }

    public Long insertOne(IxCarOrderPrice ixCarOrderPrice) {
        return ixCarOrderPriceMapper.insertOne(ixCarOrderPrice);
    }

}
