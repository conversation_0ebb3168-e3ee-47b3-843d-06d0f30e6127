# 使用 JDK 21 官方镜像
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/eclipse-temurin:21-jdk-jammy

# 维护者信息
LABEL maintainer="<EMAIL>"

RUN ls

# 复制 JAR 文件和配置文件
COPY target/ixtech-agent-api-1.0.0-SNAPSHOT.jar ixtech-agent-api.jar

# 暴露端口 (根据实际应用修改)
EXPOSE 16001

# 启动应用 (添加 -XX:+EnableDynamicAgentLoading 以支持 JDK 21 的动态代理)
ENTRYPOINT ["java", "-XX:+EnableDynamicAgentLoading", "-jar", "/ixtech-agent-api.jar", "--spring.profiles.active=prod"]