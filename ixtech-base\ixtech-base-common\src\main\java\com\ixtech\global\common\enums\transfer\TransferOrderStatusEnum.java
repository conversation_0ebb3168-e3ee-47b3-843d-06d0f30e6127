package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 接送机订单状态
 *
 * <AUTHOR> hu
 * @date 2025/7/20 18:34
 */
@Getter
public enum TransferOrderStatusEnum {

    CANCELLED(0, "已取消"),
    PENDING_CONFIRMATION(1, "待确认"),
    CONFIRMED(2, "已确认"),
    ASSIGNED(3, "已指派"),
    COMPLETED(4, "已完成"),
    ;

    TransferOrderStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 状态code
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String value;

    public static TransferOrderStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferOrderStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }


    /**
     * 根据code直接获取对应的value
     */
    public static String getValueByCode(Integer code) {
        TransferOrderStatusEnum status = fromCode(code);
        return status != null ? status.value : null;
    }


}
