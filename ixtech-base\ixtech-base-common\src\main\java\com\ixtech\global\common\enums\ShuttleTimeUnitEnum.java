package com.ixtech.global.common.enums;
import com.ixtech.global.common.enums.inf.DictInf;
import lombok.Getter;
import lombok.AllArgsConstructor;

/**
 * 接驳预计时间单位枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ShuttleTimeUnitEnum implements DictInf {

    /**
     * 小时
     */
    HOUR("1", "小时"),

    /**
     * 分钟（默认）
     */
    MINUTE("2", "分钟");

    /**
     * 单位值
     */
    private final String value;

    /**
     * 单位名称
     */
    private final String label;

}