package com.ixtech.global.interceptor;

import com.ixtech.global.util.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 语言上下文持有者：使用 ThreadLocal 存储当前请求的语言信息
 *
 * <AUTHOR> hu
 * @date 2025/6/16 19:11
 */
@Slf4j
public class LanguageContextHolder {

    private static final ThreadLocal<List<RequestUtils.LanguagePriority>> LANGUAGE_HOLDER =
            ThreadLocal.withInitial(() -> null);

    /**
     * 设置当前请求的语言列表
     */
    public static void setLanguages(List<RequestUtils.LanguagePriority> languages) {
        LANGUAGE_HOLDER.set(languages);
    }

    /**
     * 获取当前请求的首选语言
     */
    public static RequestUtils.LanguagePriority getPrimaryLanguage() {
        List<RequestUtils.LanguagePriority> languages = getLanguages();
        return CollectionUtils.isEmpty(languages) ? null : languages.getFirst();
    }

    /**
     * 获取当前请求的所有语言（按优先级排序）
     */
    public static List<RequestUtils.LanguagePriority> getLanguages() {
        List<RequestUtils.LanguagePriority> languages = LANGUAGE_HOLDER.get();
        if (CollectionUtils.isEmpty(languages)) {
            try {
                languages = RequestUtils.getRequestLanguage();
                if (CollectionUtils.isEmpty(languages)) {
                    languages = List.of(RequestUtils.defaultLanguagePriority());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return languages;
    }

    /**
     * 清除上下文
     */
    public static void clear() {
        LANGUAGE_HOLDER.remove();
    }

}
