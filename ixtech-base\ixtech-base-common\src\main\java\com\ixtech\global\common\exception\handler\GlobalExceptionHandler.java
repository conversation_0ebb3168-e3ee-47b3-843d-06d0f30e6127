package com.ixtech.global.common.exception.handler;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.exception.ServerException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.core.io.buffer.DataBufferLimitException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import static com.ixtech.global.common.exception.enums.CommonErrorCode.CLIENT_ERROR;
import static com.ixtech.global.common.exception.enums.CommonErrorCode.SYSTEM_ERROR;


/**
 * 全局异常处理器，将 Exception 翻译成 ApiResponse + 对应的异常编号
 */
@RestControllerAdvice
@AllArgsConstructor
@Slf4j
@ConditionalOnMissingBean(name = "globalExceptionHandler")
public class GlobalExceptionHandler {

    /**
     * 请求体数据量过大 异常
     */
    @ExceptionHandler(value = DataBufferLimitException.class)
    public ApiResponse<?> dataBufferLimitExceptionHandler(DataBufferLimitException ex) {
        log.warn("[dataBufferLimitExceptionHandler]", ex);
        return ApiResponse.fail(SYSTEM_ERROR, "请求体数据量过大！");
    }

    /**
     * 处理 上传文件数据量过大 异常
     */
    @ExceptionHandler(value = MaxUploadSizeExceededException.class)
    public ApiResponse<?> maxUploadSizeExceededExceptionHandler(MaxUploadSizeExceededException ex) {
        log.warn("[maxUploadSizeExceededExceptionHandler]", ex);
        return ApiResponse.fail(SYSTEM_ERROR, "上传文件数据量过大！");
    }

    /**
     * 处理 redisson解锁 异常
     */
    @ExceptionHandler(value = IllegalMonitorStateException.class)
    public ApiResponse<?> illegalMonitorStateExceptionHandler(IllegalMonitorStateException ex) {
        log.warn("[illegalMonitorStateExceptionHandler]", ex);
        return ApiResponse.fail(SYSTEM_ERROR, "系统繁忙，请稍后重试！");
    }

    /**
     * 处理 SpringMVC 请求参数缺失
     * <p>
     * 例如说，接口上设置了 @RequestParam("xx") 参数，结果并未传递 xx 参数
     */
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public ApiResponse<?> missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException ex) {
        log.warn("[missingServletRequestParameterExceptionHandler]", ex);
        return ApiResponse.fail(CLIENT_ERROR, String.format("请求参数缺失:%s", ex.getParameterName()));
    }

    /**
     * 处理 SpringMVC 请求参数类型错误
     * <p>
     * 例如说，接口上设置了 @RequestParam("xx") 参数为 Integer，结果传递 xx 参数类型为 String
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ApiResponse<?> methodArgumentTypeMismatchExceptionHandler(MethodArgumentTypeMismatchException ex) {
        log.warn("[missingServletRequestParameterExceptionHandler]", ex);
        return ApiResponse.fail(CLIENT_ERROR, String.format("请求参数类型错误:%s", ex.getMessage()));
    }

    /**
     * 处理 SpringMVC 参数校验不正确
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<?> methodArgumentNotValidExceptionExceptionHandler(MethodArgumentNotValidException ex) {
        log.warn("[methodArgumentNotValidExceptionExceptionHandler]", ex);
        FieldError fieldError = ex.getBindingResult().getFieldError();
        assert fieldError != null; // 断言，避免告警
        return ApiResponse.fail(CLIENT_ERROR, String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
    }

    /**
     * 处理 SpringMVC 参数绑定不正确，本质上也是通过 Validator 校验
     */
    @ExceptionHandler(BindException.class)
    public ApiResponse<?> bindExceptionHandler(BindException ex) {
        log.warn("[handleBindException]", ex);
        FieldError fieldError = ex.getFieldError();
        assert fieldError != null; // 断言，避免告警
        return ApiResponse.fail(CLIENT_ERROR, String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
    }

    // /**
    //  * 处理 SpringMVC 参数格式错误
    //  */
    // @ExceptionHandler(InvalidFormatException.class)
    // public ApiResponse<?> invalidFormatExceptionHandler(InvalidFormatException ex) {
    //     log.warn("[invalidFormatExceptionHandler]", ex);
    //
    //     // 获取字段路径
    //     List<JsonMappingException.Reference> path = ex.getPath();
    //     String fieldName = "unknown"; // 默认值，避免路径为空
    //     if (!path.isEmpty()) {
    //         // 获取最后一个路径节点的字段名
    //         fieldName = path.getLast().getFieldName();
    //     }
    //
    //
    //     // 构造详细的错误信息
    //     String errorMessage = String.format("请求参数不正确，字段 '%s' 格式错误", fieldName);
    //
    //     return ApiResponse.fail(CLIENT_ERROR, errorMessage);
    // }

    // /**
    //  * 处理业务异常 BizException
    //  * <p>
    //  * 例如说，商品库存不足，用户手机号已存在。
    //  */
    // @ExceptionHandler(value = BizException.class)
    // public ApiResponse<?> serviceExceptionHandler(BizException ex) {
    //     log.info("[bizExceptionHandler]", ex);
    //     return ApiResponse.fail(ex.getMessage(), ex.getCode());
    // }

    /**
     * 处理业务异常 ServerException
     * <p>
     * 例如说，商品库存不足，用户手机号已存在。
     */
    @ExceptionHandler(value = ServerException.class)
    public ApiResponse<?> serviceExceptionHandler(ServerException ex) {
        log.info("[serverExceptionHandler]", ex);
        return ApiResponse.fail(ex.getErrorCode(), ex.getMessage());
    }

    /**
     * 处理系统异常，兜底处理所有的一切
     */
    @ExceptionHandler(value = Exception.class)
    public ApiResponse<?> defaultExceptionHandler(HttpServletRequest req, Throwable ex) {

        // 情况三：处理异常
        log.warn("[defaultExceptionHandler]", ex);
        // 返回 ERROR ApiResponse
        return ApiResponse.fail(SYSTEM_ERROR, ex.getMessage());
    }

}
