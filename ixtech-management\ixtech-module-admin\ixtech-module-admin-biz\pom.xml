<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>ixtech-module-admin</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ixtech-module-admin-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        admin模块，主要实现订单、车辆等功能。
    </description>
    <dependencies>  <!-- 5. 新增依赖，这里引入的都是比较常用的业务组件、技术组件 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>ixtech-module-admin-api</artifactId>
            <version>2.4.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-spring-boot-starter-mybatis</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-system-biz</artifactId>
            <version>2.4.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
            <version>${loadbalancer.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId> <!-- 多数据源 -->
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-undertow</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>net.iakovlev</groupId>
            <artifactId>timeshape</artifactId>
            <version>2024a.25</version>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>com.ixtech.global</groupId>-->
<!--            <artifactId>ixtech-base-parent</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->

        <!-- Apache POI - Excel 处理核心库 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.3</version> <!-- 处理.xls -->
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.3</version> <!-- 处理.xlsx -->
        </dependency>

<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-spring-boot-starter-security</artifactId>-->
<!--        </dependency>-->

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-web</artifactId>
            <version>2.4.1-SNAPSHOT</version>
        </dependency>
        <!-- 邮箱验证 -->
        <dependency>
            <groupId>commons-validator</groupId>
            <artifactId>commons-validator</artifactId>
            <version>1.7</version>
        </dependency>

        <!-- DB 相关 -->

        <!-- Test 测试相关 -->
<!--        <dependency>-->
<!--            <groupId>cn.iocoder.boot</groupId>-->
<!--            <artifactId>yudao-spring-boot-starter-excel</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.4.0-jre</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.19</version>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-excel</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>