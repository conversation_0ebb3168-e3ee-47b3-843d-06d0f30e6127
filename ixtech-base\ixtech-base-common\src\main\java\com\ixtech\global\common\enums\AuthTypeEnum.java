package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 认证方式枚举
 * 对应表: rental_vendor_channel_config, 字段: auth_type (虽然数据库字段为VARCHAR, 但根据描述按数值型枚举处理)
 * (1-API_KEY)
 */
@Getter
@AllArgsConstructor
public enum AuthTypeEnum implements DictInf {

    API_KEY(1, "API_KEY"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }
}