package com.ixtech.global.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商信息
 */
@Data
@NoArgsConstructor
public class VendorInfo implements Serializable {
    /**
     *
     */
    private Long id;

    /**
     * 供应商代码
     */
    private String code;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 供应商logo
     */
    private String logo;

    /**
     * 供应商描述
     */
    private String description;

    /**
     * 公司全称
     */
    private String companyFullName;

    /**
     * 报价货币
     */
    private String quoteCurrency;

    /**
     * 结算货币
     */
    private String settlementCurrency;

    /**
     * 结算模式 1:底价模式 2:抽佣模式
     */
    private Byte settlementMode;

    /**
     * 比例x%
     */
    private BigDecimal rate;

    /**
     * 负责人
     */
    private String principal;

    /**
     * 联系电话
     */
    private String contactNumber;
}
