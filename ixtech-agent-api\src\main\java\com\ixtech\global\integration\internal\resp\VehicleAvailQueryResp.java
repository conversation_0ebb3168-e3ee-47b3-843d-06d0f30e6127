package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 车辆查询结果 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehicleAvailQueryResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 车辆租赁核心信息
  */
 private VehRentalCoreResp vehRentalCore;

 /**
  * 车辆供应商可用信息列表
  */
 private List<VehVendorAvailResp> vehVendorAvails;
}
