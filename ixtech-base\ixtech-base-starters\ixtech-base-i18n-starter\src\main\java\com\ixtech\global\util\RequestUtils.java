package com.ixtech.global.util;

import com.ixtech.global.constant.I18nConstants;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 获取当前请求中的国际化语言
 *
 * <AUTHOR> hu
 * @date 2025/6/10 16:50
 */
public class RequestUtils {

    /**
     * 获取请求中的语言列表，按优先级从高到低排序
     */
    public static List<LanguagePriority> getRequestLanguage() {

        List<LanguagePriority> result = new ArrayList<>();
        ServletRequestAttributes attributes = (ServletRequestAttributes)
                RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return result;
        }

        HttpServletRequest request = attributes.getRequest();
        // 从Accept-Language头解析（提取优先级最高的语言）
        return getRequestLanguage(request);
    }

    /**
     * 获取请求中的语言列表，按优先级从高到低排序
     *
     * @param request
     * @return
     */
    public static List<LanguagePriority> getRequestLanguage(HttpServletRequest request) {

        List<LanguagePriority> result = new ArrayList<>();

        String acceptLanguage = request.getHeader(HttpHeaders.ACCEPT_LANGUAGE);
        if (StringUtils.isBlank(acceptLanguage)) {
            return result;
        }
        String[] langArr = acceptLanguage.split(I18nConstants.DOT);
        if (langArr.length == 0) {
            return result;
        }

        // 解析语言代码和权重
        for (String lang : langArr) {
            lang = lang.trim();
            if (lang.isEmpty()) {
                continue;
            }
            String[] langParts = lang.split(I18nConstants.LANG_SEPARATOR);
            if (langParts.length == 0) {
                continue;
            }
            String languageCode = langParts[0].trim();
            double quality = 1.0;
            if (langParts.length > 1) {
                try {
                    quality = Double.parseDouble(langParts[1].trim());
                } catch (NumberFormatException e) {
                    // 忽略无效权重，使用默认值
                }
            }
            result.add(new LanguagePriority(languageCode, quality));
        }

        // 按权重降序排序
        result.sort(Comparator.comparingDouble(LanguagePriority::quality).reversed());

        return result;
    }

    /**
     * 语言权重类
     *
     * @param languageCode 语言code
     * @param quality      权重优先级，越大优先级越高
     */
    public record LanguagePriority(String languageCode, double quality) {
    }

    /**
     * 获取默认语言
     *
     * @return
     */
    public static LanguagePriority defaultLanguagePriority() {
        return new RequestUtils.LanguagePriority(I18nConstants.DEFAULT_LANG, I18nConstants.DEFAULT_LANG_QUALITY);
    }

}
