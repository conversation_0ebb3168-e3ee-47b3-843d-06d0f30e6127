package com.ixtech.management.integration.internal.resp;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OrderLogResq {


    /**
     * 操作类型：1-创建订单，2-确认订单，3-分配车辆，4-确认提车，5-确认还车，6-用户取消订单，7-拒单，8-商户取消订单
     */
    private String operationType;
    private Long operatorId;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 操作时间
     */
    private String operationTime;
    /**
     * 操作ip地址
     */
    private String operationIp;

    /**
     * 操作描述
     */
    private String operationRemark;
}
