package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.Area;
import com.ixtech.management.repo.mapper.AreaMapper;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class AreaRepository {

    @Resource
    private AreaMapper areaMapper;

    /**
     * 批量查询地区信息
     *
     * @param areaIds 区域id列表
     * @return
     */
    public Map<Long, Area> selectByIds(Collection<Long> areaIds) {
        if (CollectionUtils.isEmpty(areaIds)) {
            return new HashMap<>();
        }
        List<Area> areaList = areaMapper.selectByPrimaryKeyList(areaIds);
        return CollectionUtils.emptyIfNull(areaList).stream()
                .collect(Collectors.toMap(Area::getId, Function.identity()));
    }

    /**
     * 获取所有门店覆盖区域id
     *
     * @return
     */
    public List<Area> queryAllStoreArea() {
        return areaMapper.queryAllStoreArea();
    }

}
