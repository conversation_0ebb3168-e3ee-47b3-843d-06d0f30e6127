package com.ixtech.merchant.resp;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 供应商主表(IxVendor)实体类
 *
 * <AUTHOR>
 * @since 2025-06-14 09:53:12
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IxVendorResp {

    private Long id;
    /**
     * active
     */
    private Boolean active;
    /**
     * deleted
     */
    private Boolean deleted;
    /**
     * create_time in UTC
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * update_time in UTC
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 供应商代码
     */
    private String code;
    /**
     * 供应商名称
     */
    private String name;
    /**
     * 供应商logo
     */
    private String logo;
    /**
     * 供应商描述
     */
    private String description;
    /**
     * 公司全称
     */
    private String companyFullName;
    /**
     * 报价货币
     */
    private String quoteCurrency;
    /**
     * 结算货币
     */
    private String settlementCurrency;
    /**
     * 结算模式 1:底价模式 2:抽佣模式
     */
    private Integer settlementMode;
    /**
     * 比例x%
     */
    private BigDecimal rate;
    /**
     * 负责人
     */
    private String principal;
    /**
     * 联系电话
     */
    private String contactNumber;
    /**
     * 联系电话区号
     */
    private String contactNumberCode;

}

