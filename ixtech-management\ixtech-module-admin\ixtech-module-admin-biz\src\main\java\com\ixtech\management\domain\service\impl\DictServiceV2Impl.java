package com.ixtech.management.domain.service.impl;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.domain.service.DictServiceV2;
import com.ixtech.management.integration.internal.client.VendormanagementsrvClientV2;
import com.ixtech.management.integration.internal.req.DictTypeListReq;
import com.ixtech.management.integration.internal.resp.DictTypeListResp;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;


@Service
public class DictServiceV2Impl implements DictServiceV2 {
    @Resource
    private VendormanagementsrvClientV2 dictClient;

    public ApiResponse<DictTypeListResp> getDictByTypeList(@RequestBody DictTypeListReq req) {
        return dictClient.getDictByTypeList(req);
    }

}
