package com.ixtech.global.redis;

import com.ixtech.global.common.context.SpringContextHolder;
import lombok.RequiredArgsConstructor;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Redisson工具类
 *
 * <AUTHOR>
 * @date 2025-3-12
 */
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@DependsOn("springContextHolder")
public class RedisService {

    private static final int DEFAULT_TIMEOUT = Integer.parseInt(SpringContextHolder.getProperty("cache.timeout", "60"));
    private static final TimeUnit DEFAULT_TIME_UNIT = TimeUnit.valueOf(SpringContextHolder.getProperty("cache.timeunit", "SECONDS"));
    private final RedissonClient redissonClient;

    // 将脚本定义为静态常量
    private static final String GET_MULTI_FROM_HASH_IF_EXISTS_LUA =
            "if redis.call('EXISTS', KEYS[1]) == 1 then " +
                    "return redis.call('HMGET', KEYS[1], unpack(ARGV)) " +
                    "else " +
                    "return nil " +
                    "end";

    // [新增] 用于原子性获取整个Hash的Lua脚本
    private static final String GET_ALL_FROM_HASH_IF_EXISTS_LUA =
            "if redis.call('EXISTS', KEYS[1]) == 1 then " +
                    "return redis.call('HGETALL', KEYS[1]) " +
                    "else " +
                    "return nil " +
                    "end";

    // [新增]用于实现原子性的右出左进rightPopAndLeftPush的Lua脚本
    private static final String RIGHT_POP_AND_LEFT_PUSH_LUA =
            "local element = redis.call('RPOP', KEYS[1]) " +
                    "if element then " +
                    "    redis.call('LPUSH', KEYS[2], element) " +
                    "    return element " +
                    "else " +
                    "    return nil " +
                    "end";

    /**
     * 将值存储到Redis中
     *
     * @param key   键
     * @param value 值
     */
    public <T> void setString(String key, T value) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        bucket.set(value);
    }

    /**
     * 将值存储到Redis中
     *
     * @param key      键
     * @param value    值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    public <T> void setString(String key, T value, long timeout, TimeUnit timeUnit) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        bucket.set(value, timeout, timeUnit);
    }

    /**
     * 将值存储到Redis中
     *
     * @param key   键
     * @param value 值
     */
    public <T> void setStringWithDefaultTime(String key, T value) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        bucket.set(value, DEFAULT_TIMEOUT, DEFAULT_TIME_UNIT);
    }

    /**
     * 根据键获取Redis中的值
     *
     * @param key 键
     * @return 值
     */
    public <T> T getString(String key) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    // ============================= Hash类型操作 ============================

    /**
     * 将值存储到Redis中
     *
     * @param key   键
     * @param field hash键
     * @param value 值
     */
    public <T> boolean addToHash(String key, Object field, T value) {
        RMap<Object, T> hash = redissonClient.getMap(key);
        return hash.fastPut(field, value);
    }

    /**
     * 将值存储到Redis中
     *
     * @param key      键
     * @param field    hash键
     * @param value    值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    public <T> boolean addToHash(String key, Object field, T value, long timeout, ChronoUnit timeUnit) {
        RMap<Object, T> hash = redissonClient.getMap(key);
        boolean fastPut = hash.fastPut(field, value);
        boolean expire = hash.expire(Instant.now().plus(timeout, timeUnit));
        return fastPut && expire;
    }

    /**
     * 根据键和Hash键获取Redis中的值
     *
     * @param key   键
     * @param field hash键
     * @return 值
     */
    public <K, T> T getFromHash(String key, K field) {
        RMap<K, T> hash = redissonClient.getMap(key);
        return hash.get(field);
    }

    /**
     * 根据键获取Redis中的值
     *
     * @param key 键
     * @return 值
     */
    public <T> Map<Object, T> getFromHash(String key) {
        RMap<Object, T> hash = redissonClient.getMap(key);
        return hash.readAllMap();
    }

    /**
     * 从单个Redis键的Hash中批量获取多个字段的值
     *
     * @param key    键
     * @param fields hash字段列表
     * @return Map格式：{field1=value1, field2=value2, ...}（不存在的field不会出现在结果中）
     */
    public <K, T> Map<K, T> getMultiFromHash(String key, Set<K> fields) {
        if (CollectionUtils.isEmpty(fields)) {
            return Collections.emptyMap();
        }
        RMap<K, T> hash = redissonClient.getMap(key);
        return hash.getAll(fields);
    }

    /**
     * [原子操作] 如果key存在，则批量获取Hash中的字段；如果key不存在，则返回null。
     *
     * @param key    Hash的键
     * @param fields 要获取的字段集合
     * @param <K>    字段的类型
     * @param <T>    值的类型
     * @return 如果key存在，返回一个Map，其中仅包含在Hash中实际找到的字段和值。
     * 如果key不存在，直接返回null。
     */
    public <K, T> Map<K, T> getMultiFromHashIfExists(String key, Set<K> fields) {
        if (CollectionUtils.isEmpty(fields)) {
            return Collections.emptyMap();
        }

        RScript script = redissonClient.getScript();
        List<K> orderedFields = new ArrayList<>(fields);

        Object result = script.eval(
                RScript.Mode.READ_ONLY,
                GET_MULTI_FROM_HASH_IF_EXISTS_LUA, // 使用静态常量
                RScript.ReturnType.MULTI,
                Collections.singletonList(key),
                orderedFields.toArray()
        );

        if (result == null) {
            return null;
        }

        List<T> values = (List<T>) result;
        return IntStream.range(0, orderedFields.size()) // 创建一个从0到size-1的索引流
                .filter(i -> values.get(i) != null)      // 过滤掉值是null的索引
                .boxed()                                 // 将int索引转换为Integer对象
                .collect(Collectors.toMap(
                        orderedFields::get,              // 根据索引获取Key
                        values::get,                     // 根据索引获取Value
                        (v1, v2) -> v2,                  // 合并函数（理论上不会出现重复key）
                        () -> new HashMap<>(orderedFields.size()) // 提供一个预设容量的Map
                ));
    }

    /**
     * [更正] [原子操作] 增强版的getMultiFromHashIfExists。
     * 如果fields集合不为空，则行为与 getMultiFromHashIfExists 完全相同。
     * 如果fields集合为空或null，则尝试获取并返回整个Hash Map。
     *
     * @param key    Hash的键
     * @param fields 要获取的字段集合。如果为空，则获取所有字段。
     * @param <K>    字段的类型
     * @param <T>    值的类型
     * @return 如果key存在，返回一个Map。如果fields不为空，Map中仅包含找到的字段；如果fields为空，Map中包含所有字段。
     * 如果key不存在，直接返回null。
     */
    public <K, T> Map<K, T> getAllOrMultiFromHashIfExists(String key, Set<K> fields) {
        // 如果提供了具体的字段，则重用原有的高效方法
        if (!CollectionUtils.isEmpty(fields)) {
            return getMultiFromHashIfExists(key, fields);
        }

        // 如果未提供字段，则尝试获取整个Hash
        RScript script = redissonClient.getScript();

        // 使用Lua脚本保证原子性。HGETALL 的结果是 [key1, value1, key2, value2, ...] 的列表
        // 正确的返回类型是 MULTI
        Object result = script.eval(
                RScript.Mode.READ_ONLY,
                GET_ALL_FROM_HASH_IF_EXISTS_LUA,
                RScript.ReturnType.MULTI, // 使用 MULTI 来接收列表
                Collections.singletonList(key)
        );

        if (result == null) {
            // 如果脚本返回null（即Key不存在），则直接返回null
            return null;
        }

        List<Object> resultList = (List<Object>) result;
        // 使用 IntStream 生成对的索引，然后收集到 Map 中
        return IntStream.range(0, resultList.size() / 2) // 1. 生成索引流: 0, 1, 2, ...
                .boxed() // 2. 将 int 转换为 Integer
                .collect(Collectors.toMap(
                        i -> (K) resultList.get(i * 2),     // 3. 根据索引 i*2 获取Key
                        i -> (T) resultList.get(i * 2 + 1), // 4. 根据索引 i*2+1 获取Value
                        (v1, v2) -> v2                      // 5. (可选) 合并函数，以防key重复
                ));
    }

    /**
     * [原子操作] 批量写入Hash并设置过期时间
     *
     * @param key      RMap的键
     * @param entries  包含多个字段和值的Map
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     * @param <F>      字段的类型
     * @param <V>      值的类型
     */
    public <F, V> void multiAddToHashAndExpire(String key, Map<F, V> entries, long timeout, TimeUnit timeUnit) {
        RBatch batch = redissonClient.createBatch();
        RMapAsync<F, V> map = batch.getMap(key);

        // 如果传入的map不为空，则正常添加
        if (!CollectionUtils.isEmpty(entries)) {
            map.putAllAsync(entries);
        }

        // 无论map是否为空，都为这个key设置过期时间。
        // 这会创建一个空的Hash key（如果它不存在），并设置TTL，从而防止缓存穿透
        map.expireAsync(Duration.of(timeout, timeUnit.toChronoUnit()));

        batch.execute();
    }

    /**
     * [原子操作] 批量写入Hash并设置过期时间 使用默认时间和时间单位
     *
     * @param key     RMap的键
     * @param entries 包含多个字段和值的Map
     * @param <F>     字段的类型
     * @param <V>     值的类型
     */
    public <F, V> void multiAddToHashAndExpire(String key, Map<F, V> entries) {
        multiAddToHashAndExpire(key, entries, DEFAULT_TIMEOUT, DEFAULT_TIME_UNIT);
    }

    /**
     * [批量写] 将多个字段和值写入到指定的Hash中（使用HMSET命令，效率高）
     *
     * @param key     RMap的键
     * @param entries 包含多个字段和值的Map
     * @param <F>     字段的类型
     * @param <V>     值的类型
     */
    public <F, V> void multiAddToHash(String key, Map<F, V> entries) {
        if (CollectionUtils.isEmpty(entries)) {
            return;
        }
        RMap<F, V> hash = redissonClient.getMap(key);
        hash.putAll(entries);
    }

    /**
     * 根据键和Hash键更新Redis中的值
     *
     * @param key   键
     * @param field hash键
     * @param value 值
     * @return 更新成功返回true，否则返回false
     */
    public <T> boolean updateToHash(String key, Object field, T value) {
        RMap<Object, T> hash = redissonClient.getMap(key);
        return hash.fastReplace(field, value);
    }

    /**
     * 根据Key，删除Hash类型的数据
     *
     * @param key      键
     * @param hashKeys hash键
     * @return 删除成功的数量
     */
    public <T> long removeFromHash(String key, Set<T> hashKeys) {
        RMap<Object, T> hash = redissonClient.getMap(key);
        return hash.fastRemove(hashKeys.toArray());
    }

    // ============================= RMapCache类型操作 ============================

    /**
     * 将值存储到Redis Hash（支持字段级过期时间）
     *
     * @param key      键
     * @param field    hash键
     * @param value    值
     * @param timeout  过期时间（统一应用于字段）
     * @param timeUnit 时间单位
     * @return 写入成功返回true，否则返回false
     */
    public <K, V> boolean addToMapCache(String key, K field, V value, long timeout, TimeUnit timeUnit) {
        RMapCache<K, V> mapCache = redissonClient.getMapCache(key);
        return mapCache.fastPut(field, value, timeout, timeUnit);
    }

    /**
     * 将值存储到Redis Hash（默认10秒过期时间）
     *
     * @param key   键
     * @param field hash键
     * @param value 值
     * @return 写入成功返回true，否则返回false
     */
    public <K, V> boolean addToMapCacheDefault(String key, K field, V value) {
        return addToMapCache(key, field, value, DEFAULT_TIMEOUT, DEFAULT_TIME_UNIT);
    }

    /**
     * 批量将值存储到Redis Hash（所有字段使用统一的过期时间）
     *
     * @param key      键
     * @param entries  键值对，格式：{field1=value1, field2=value2, ...}
     * @param timeout  过期时间（统一应用于所有字段）
     * @param timeUnit 时间单位
     * @return 写入成功的字段数量
     */
    public <K, V> int multiAddToMapCache(String key, Map<K, V> entries, long timeout, TimeUnit timeUnit) {
        if (CollectionUtils.isEmpty(entries)) {
            return 0;
        }
        RBatch batch = redissonClient.createBatch();
        RMapCacheAsync<K, V> mapCache = batch.getMapCache(key);
        entries.forEach((field, value) -> mapCache.fastPutAsync(field, value, timeout, timeUnit));
        BatchResult<?> result = batch.execute();
        return result.getResponses().size();
    }

    /**
     * 批量将值存储到Redis Hash（所有字段默认10秒过期时间）
     *
     * @param key     键
     * @param entries 键值对，格式：{field1=value1, field2=value2, ...}
     * @return 写入成功的字段数量
     */
    public <K, V> int multiAddToMapCacheDefault(String key, Map<K, V> entries) {
        return multiAddToMapCache(key, entries, DEFAULT_TIMEOUT, DEFAULT_TIME_UNIT);
    }

    /**
     * 从Redis Hash中批量获取多个字段的值（支持字段级过期时间）
     *
     * @param key    键
     * @param fields hash字段列表
     * @return Map格式：{field1=value1, field2=value2, ...}（不存在或已过期的field不会出现在结果中）
     */
    public <K, V> Map<K, V> getMultiFromMapCache(String key, Set<K> fields) {
        if (CollectionUtils.isEmpty(fields)) {
            return Collections.emptyMap();
        }
        RMapCache<K, V> mapCache = redissonClient.getMapCache(key);
        return mapCache.getAll(fields);
    }

    /**
     * 根据键和Hash键更新Redis Hash中的值（支持字段级过期时间）
     *
     * @param key      键
     * @param field    hash键
     * @param value    值
     * @param timeout  过期时间（统一应用于字段）
     * @param timeUnit 时间单位
     * @return 更新成功返回true，否则返回false
     */
    public <K, V> boolean updateToMapCache(String key, K field, V value, long timeout, TimeUnit timeUnit) {
        RMapCache<K, V> mapCache = redissonClient.getMapCache(key);
        return mapCache.fastPut(field, value, timeout, timeUnit);
    }

    /**
     * 根据键和Hash键更新Redis Hash中的值（默认10秒过期时间）
     *
     * @param key   键
     * @param field hash键
     * @param value 值
     * @return 更新成功返回true，否则返回false
     */
    public <K, V> boolean updateToMapCacheDefault(String key, K field, V value) {
        return updateToMapCache(key, field, value, DEFAULT_TIMEOUT, DEFAULT_TIME_UNIT);
    }

    /**
     * 从Redis Hash中删除指定字段（支持字段级过期时间）
     *
     * @param key      键
     * @param hashKeys hash键集合
     * @return 删除成功的数量
     */
    public <K> long removeFromMapCache(String key, Set<K> hashKeys) {
        RMapCache<Object, Object> mapCache = redissonClient.getMapCache(key);
        return mapCache.fastRemove(hashKeys.toArray());
    }

    /**
     * 设置Redis Hash的整体过期时间
     *
     * @param key      键
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     * @return 设置成功返回true，否则返回false
     */
    public boolean expireMapCache(String key, long timeout, TimeUnit timeUnit) {
        RMapCache<Object, Object> mapCache = redissonClient.getMapCache(key);
        return mapCache.expire(timeout, timeUnit);
    }

    // ============================= List类型操作 ============================

    /**
     * 向List数据类型中添加值
     *
     * @param key   键
     * @param value 值
     */
    public <T> boolean addToList(String key, T value) {
        RList<T> list = redissonClient.getList(key);
        return list.add(value);
    }

    /**
     * 向List数据类型中添加值
     *
     * @param key   键
     * @param value 值
     */
    public <T> boolean addToList(String key, List<T> value) {
        RList<T> list = redissonClient.getList(key);
        return list.addAll(value);
    }

    /**
     * 向List数据类型中添加值
     *
     * @param key      键
     * @param value    值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    public <T> boolean addToList(String key, List<T> value, long timeout, ChronoUnit timeUnit) {
        RList<T> list = redissonClient.getList(key);
        list.addAll(value);
        return list.expire(Instant.now().plus(timeout, timeUnit));
    }

    /**
     * 向List数据类型中添加值
     *
     * @param key      键
     * @param value    值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    public <T> boolean addToList(String key, T value, long timeout, ChronoUnit timeUnit) {
        RList<T> list = redissonClient.getList(key);
        list.add(value);
        return list.expire(Instant.now().plus(timeout, timeUnit));
    }

    /**
     * 从List数据类型中获取值
     *
     * @param key   键
     * @param start 起始位置
     * @param end   结束位置
     * @return 值
     */
    public <T> List<T> getFromList(String key, int start, int end) {
        RList<T> list = redissonClient.getList(key);
        return list.range(start, end);
    }

    /**
     * 获取List数据类型中的所有值
     *
     * @param key 键
     * @return 值
     */
    public <T> List<T> getFromList(String key) {
        RList<T> list = redissonClient.getList(key);
        return list.readAll();
    }


    /**
     * 移除集合左侧第一个元素
     *
     * @param key 键
     */
    public void removeListLeft(String key) {
        RList<Object> list = redissonClient.getList(key);
        list.fastRemove(0);
    }

    /**
     * 移除集合右侧第一个元素
     *
     * @param key 键
     */
    public void removeListRight(String key) {
        RList<Object> list = redissonClient.getList(key);
        list.fastRemove(list.size() - 1);
    }

    /**
     * 移除集合指定位置元素
     *
     * @param key   键
     * @param index 索引
     */
    public void removeFromList(String key, int index) {
        RList<Object> list = redissonClient.getList(key);
        list.fastRemove(index);
    }

    /**
     * 移除集合指定元素
     *
     * @param key   键
     * @param value 值
     */
    public <T> boolean removeFromList(String key, T value) {
        RList<T> list = redissonClient.getList(key);
        return list.removeIf(o -> o.equals(value));
    }

    // ============================= Set类型操作 ============================

    /**
     * 添加值到Set数据类型中
     *
     * @param key   键
     * @param value 值
     */
    public <T> boolean addToSet(String key, T value) {
        RSet<T> set = redissonClient.getSet(key);
        return set.add(value);
    }

    /**
     * 添加值到Set数据类型中
     *
     * @param key      键
     * @param value    值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     * @return 是否成功
     */
    public <T> boolean addToSet(String key, T value, long timeout, ChronoUnit timeUnit) {
        RSet<T> set = redissonClient.getSet(key);
        boolean add = set.add(value);
        boolean expire = set.expire(Instant.now().plus(timeout, timeUnit));
        return add && expire;
    }

    /**
     * 添加值到Set数据类型中
     *
     * @param key    键
     * @param values 值
     * @return 是否成功
     */
    public <T> boolean addToSet(String key, List<T> values) {
        RSet<T> set = redissonClient.getSet(key);
        return set.addAll(values);
    }

    /**
     * 添加值到Set数据类型中
     *
     * @param key      键
     * @param values   值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     * @return 是否成功
     */
    public <T> boolean addToSet(String key, List<T> values, long timeout, ChronoUnit timeUnit) {
        RSet<T> set = redissonClient.getSet(key);
        set.addAllCounted(values);
        return set.expire(Instant.now().plus(timeout, timeUnit));
    }

    /**
     * 获取Set的所有元素。
     *
     * @param key 键
     * @return 所有值
     */
    public <T> Set<T> getFromSet(String key) {
        RSet<T> set = redissonClient.getSet(key);
        return set.readAll();
    }

    /**
     * 从Set数据类型中删除值
     *
     * @param key    键
     * @param values 值
     */
    public <T> void removeFromSet(String key, List<T> values) {
        RSet<T> set = redissonClient.getSet(key);
        values.forEach(set::remove);
    }

    /**
     * 从Set数据类型中删除值
     *
     * @param key   键
     * @param value 值
     */
    public <T> boolean removeFromSet(String key, T value) {
        RSet<T> set = redissonClient.getSet(key);
        return set.remove(value);
    }

    // ============================= ZSet类型操作 ============================

    /**
     * 添加值到ZSet数据类型中
     *
     * @param key   键
     * @param value 值
     * @param score 分值
     */
    public <T> void addToZSet(String key, T value, double score) {
        RScoredSortedSet<T> sortedSet = redissonClient.getScoredSortedSet(key);
        sortedSet.add(score, value);
    }

    /**
     * 在ZSet数据类型中添加值
     *
     * @param key      键
     * @param value    值
     * @param score    分值
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     */
    public <T> void addToZSet(String key, T value, double score, long timeout, ChronoUnit timeUnit) {
        RScoredSortedSet<T> sortedSet = redissonClient.getScoredSortedSet(key);
        sortedSet.add(score, value);
        sortedSet.expire(Instant.now().plus(timeout, timeUnit));
    }

    /**
     * 获取ZSet的范围元素。
     *
     * @param key   键
     * @param start 起始位置
     * @param end   结束位置
     * @return Set类型的值
     */
    public <T> Set<Object> getFromZSet(String key, int start, int end) {
        RScoredSortedSet<T> sortedSet = redissonClient.getScoredSortedSet(key);
        return new HashSet<>(sortedSet.valueRange(start, end));
    }

    /**
     * 删除ZSet数据类型中的值
     *
     * @param key    键
     * @param values 值
     */
    public <T> void removeFromZSet(String key, List<T> values) {
        RScoredSortedSet<T> sortedSet = redissonClient.getScoredSortedSet(key);
        sortedSet.removeAll(values);
    }

    /**
     * 删除ZSet数据类型中的值
     *
     * @param key   键
     * @param value 值
     */
    public <T> void removeFromZSet(String key, T value) {
        RScoredSortedSet<T> sortedSet = redissonClient.getScoredSortedSet(key);
        sortedSet.remove(value);
    }

    // ============================= Common ============================

    /**
     * 判断Key是否存在
     *
     * @param key 键
     * @return 存在返回true，否则返回false
     */
    public boolean exists(String key) {
        return redissonClient.getBucket(key).isExists();
    }

    /**
     * 删除Key
     *
     * @param key 键
     */
    public boolean remove(String key) {
        long delete = redissonClient.getKeys().delete(key);
        return delete > 0;
    }

    /**
     * 设置Key的过期时间
     *
     * @param key      键
     * @param timeout  过期时间
     * @param timeUnit 时间单位
     * @return 设置成功返回true，否则返回false
     */
    public boolean expire(String key, long timeout, ChronoUnit timeUnit) {
        return redissonClient.getBucket(key).expire(Instant.now().plus(timeout, timeUnit));
    }

    /**
     * 设置Key的过期时间（使用默认配置）
     *
     * @param key 键
     * @return 设置成功返回true，否则返回false
     */
    public boolean expire(String key) {
        return expire(key, DEFAULT_TIMEOUT, DEFAULT_TIME_UNIT.toChronoUnit());
    }

    /**
     * 获取Key的过期时间
     *
     * @param key 键
     * @return 过期时间
     */
    public Long getExpire(String key) {
        return redissonClient.getBucket(key).getExpireTime();
    }

    /**
     * 递增操作
     *
     * @param key   键
     * @param delta 增加的值
     * @return 递增后的值，如果键不存在，则返回-1
     */
    public long increment(String key, long delta) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        return atomicLong.addAndGet(delta);
    }

    /**
     * 递减操作
     *
     * @param key   键
     * @param delta 减少的值
     * @return 递减后的值，如果键不存在，则返回-1
     */
    public long decrement(String key, long delta) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        return atomicLong.decrementAndGet();
    }

    /**
     * 递增操作
     *
     * @param key   键
     * @param delta 增加的值
     * @return 递增后的值，如果键不存在，则返回-1
     */
    public double increment(String key, double delta) {
        RAtomicDouble atomicDouble = redissonClient.getAtomicDouble(key);
        return atomicDouble.addAndGet(delta);
    }

    /**
     * 递减操作
     *
     * @param key   键
     * @param delta 减少的值
     * @return 递减后的值，如果键不存在，则返回-1
     */
    public double decrement(String key, double delta) {
        RAtomicDouble atomicDouble = redissonClient.getAtomicDouble(key);
        return atomicDouble.decrementAndGet();
    }

    /**
     * 批量获取String类型的值（使用RBatch优化性能）
     *
     * @param keys 键列表
     * @return Map格式：{key1=value1, key2=value2, ...}（不存在的key不会出现在结果中）
     */
    public <T> Map<String, T> multiGetString(List<String> keys) {
        RBatch batch = redissonClient.createBatch();
        keys.forEach(key -> batch.getBucket(key).getAsync());
        BatchResult<?> result = batch.execute();

        Map<String, T> resultMap = new HashMap<>();
        List<?> responses = result.getResponses();
        if (CollectionUtils.isEmpty(responses)) {
            return resultMap;
        }
        for (int i = 0; i < keys.size(); i++) {
            if (responses.get(i) != null) {
                resultMap.put(keys.get(i), (T) responses.get(i));
            }
        }
        return resultMap;
    }

    /**
     * 批量设置String类型的值
     *
     * @param keyValueMap 键值对 {key1=value1, key2=value2, ...}
     */
    public <T> void multiSetString(Map<String, T> keyValueMap) {
        RBatch batch = redissonClient.createBatch();
        keyValueMap.forEach((key, value) -> batch.getBucket(key).setAsync(value));
        batch.execute();
    }

    /**
     * 批量设置String类型的值（带过期时间）
     *
     * @param keyValueMap 键值对
     * @param timeout     过期时间
     * @param timeUnit    时间单位
     */
    public <T> void multiSetString(Map<String, T> keyValueMap, long timeout, TimeUnit timeUnit) {
        RBatch batch = redissonClient.createBatch();
        keyValueMap.forEach((key, value) -> batch.getBucket(key).setAsync(value, timeout, timeUnit));
        batch.execute();
    }

    /**
     * 原子性进行右出左进操作
     *
     * @param sourceKey
     * @param destinationKey
     * @return
     */
    public String rightPopAndLeftPush(String sourceKey, String destinationKey) {
        RScript script = redissonClient.getScript();
        Object result = script.eval(
                RScript.Mode.READ_WRITE,
                RIGHT_POP_AND_LEFT_PUSH_LUA,
                RScript.ReturnType.VALUE,
                Arrays.asList(sourceKey, destinationKey)
        );
        return result != null ? result.toString() : null;
    }

    /**
     * 从列表中移除匹配的元素
     *
     * @param key   Redis键
     * @param count 删除数量：
     *              >0 从左往右删除最多count个匹配项
     *              <0 从右往左删除最多count个匹配项
     *              =0 删除所有匹配项
     * @param value 要匹配的值
     * @return 是否执行了删除操作（注意：与Redis原生LREM不同，不返回实际删除数量）
     */
    public <T> boolean removeFromList(String key, int count, T value) {
        RList<T> list = redissonClient.getList(key);
        return list.remove(value, count);
    }

    /**
     * 获取并返回实际删除的元素数量（扩展方法）
     *
     * @param key   Redis键
     * @param value 要匹配的值
     * @return 实际删除的元素数量
     */
    public <T> long removeAndCountFromList(String key, T value) {
        // 先获取原始列表长度
        long originalSize = redissonClient.getList(key).size();

        // 执行删除所有匹配项
        boolean changed = redissonClient.getList(key).remove(value, 0);

        if (!changed) {
            return 0;
        }

        // 计算删除数量
        return originalSize - redissonClient.getList(key).size();
    }

    /**
     * 原子性地移除并返回列表首个元素
     */
    public <T> T removeAndGetFirst(String key) {
        RList<T> list = redissonClient.getList(key);
        T value = list.getFirst();
        if (value != null && list.remove(value)) {
            return value;
        }
        return null;
    }

    public RedissonClient getRedissonClient(){
        return redissonClient;
    }

}
