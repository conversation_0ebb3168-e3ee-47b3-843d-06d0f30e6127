package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 租车费用支付状态枚举
 * 对应表: rental_order_charge, 字段: payment_status
 * (1-待支付，2-已支付)
 */
@Getter
@AllArgsConstructor
public enum RentalPaymentStatusEnum implements DictInf {

    PENDING_PAYMENT(1, "待支付"),
    PAID(2, "已支付"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static RentalPaymentStatusEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
