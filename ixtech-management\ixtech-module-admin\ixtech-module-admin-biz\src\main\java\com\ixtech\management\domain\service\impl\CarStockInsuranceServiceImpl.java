package com.ixtech.management.domain.service.impl;

import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.CarStockInsuranceService;
import com.ixtech.management.domain.service.ModelService;
import com.ixtech.management.integration.internal.req.CarStockListReq;
import com.ixtech.management.integration.internal.resp.ModelSelectOptionResponse;
import com.ixtech.management.repo.entity.*;
import com.ixtech.management.repo.repository.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.ixtech.management.common.enums.ErrorCodeConstants.INVALID_MODELID_STOREID;
import static com.ixtech.management.common.enums.ErrorCodeConstants.INVALID_ORDER_STATUS;

/**
 * 供应商service
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:14
 */
@Slf4j
@Service
public class CarStockInsuranceServiceImpl implements CarStockInsuranceService {

    @Resource
    private JipinzucheInsuranceRepository jipinzucheInsuranceRepository;
    @Resource
    private JipinzucheCarStockInsuranceRepository jipinzucheCarStockInsuranceRepository;

    @Resource
    private JipinzucheCarStockRepository jipinzucheCarStockRepository;

    @Override
    public List<SelectOptionResponse<Long>> dropdownList(CarStockListReq req) {
        if (req == null || (req.getModelid() == null && req.getStoreid() == null)) {
            throw exception(INVALID_MODELID_STOREID, "车型和取车门店缺失");
        }
        List<JipinzucheCarStock> carStock = jipinzucheCarStockRepository.selectByModelIdAndStoreId(req.getModelid(), req.getStoreid());
        if (carStock == null || carStock.isEmpty()) {
            throw exception(INVALID_MODELID_STOREID, "车型或取车门店不存在");
        }
        List<JipinzucheCarStockInsurance> list = jipinzucheCarStockInsuranceRepository.selectByStockId(carStock.getFirst().getId());

        List<SelectOptionResponse<Long>> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JipinzucheCarStockInsurance j = list.get(i);
            JipinzucheInsurance jipinzucheInsurance = jipinzucheInsuranceRepository.selectById(Long.valueOf(j.getInsid()));
            SelectOptionResponse<Long> longSelectOptionResponse = new SelectOptionResponse<>();
            longSelectOptionResponse.setValue(Long.valueOf(j.getId()));
            if (jipinzucheInsurance != null) {
                longSelectOptionResponse.setLabel(jipinzucheInsurance.getTitle());
                result.add(longSelectOptionResponse);
            }
        }

        return result;
    }

}
