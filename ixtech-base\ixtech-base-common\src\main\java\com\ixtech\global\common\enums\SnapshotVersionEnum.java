package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单快照版本枚举
 * 统一管理所有业务快照的版本标识
 */
@Getter
@AllArgsConstructor
public enum SnapshotVersionEnum implements DictInf {

    // 来自 rental_order_common_snapshot 表
    STORE_BASE("STOREBASE_1.0", "门店信息快照"),
    VEHICLE_MODEL("VEHICLEMODEL_1.0", "预定车辆信息快照"),
    STORE_RULE("STORERULE_1.0", "门店服务规则快照"),
    AGE_RULE("AGERULE_1.0", "年龄规则快照"),
    CANCEL_RULE("CANCELRULE_1.0", "取消规则快照"),
    MILEAGE_RULE("MILEAGERULE_1.0", "里程政策快照"),
    OFFSITE_RULE("OFFSITERULE_1.0", "异地取还规则快照"),
    OPERATION_TIME("OPERATIONTIME_1.0", "常规营业时间快照"),
    SPECIAL_OPERATION_TIME("SPECIALOPERATIONTIME_1.0", "特殊营业时间快照"),

    // 来自 rental_order_rate_snapshot 表
    ORDER_RATE("ORDERRATE_1.0", "订单价格组快照"),

    // 来自 rental_order_insurance_snapshot 表
    ORDER_INSURANCE("ORDERINSURANCE_1.0", "订单保险快照"),

    // 来自 rental_order_special_equip_snapshot 表
    ORDER_EQUIP("ORDEREQUIP_1.0", "订单附加设备快照"),
    ;

    /**
     * 版本号，对应数据库中的 version 字段或 JSON 中的 version 属性
     */
    private final String code;

    /**
     * 版本描述
     */
    private final String label;

    /**
     * DictInf 接口实现，返回版本号
     * @return String a
     */
    @Override
    public String getValue() {
        return this.code;
    }

    /**
     * 通过版本号字符串查找对应的枚举实例
     * @param code 版本号字符串, e.g., "ORDERRATE_1.0"
     * @return SnapshotVersionEnum 实例
     */
    public static SnapshotVersionEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
