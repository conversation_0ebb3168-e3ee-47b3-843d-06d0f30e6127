package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 客户信息对象，包含租车客户的详细信息。
 */
@Data
public class CustomerReq {

    /**
     * 司机年龄，必填
     */
    @NotNull(message = "司机年龄不能为空")
    @JsonProperty("driver_age")
    private Integer driverAge;

    /**
     * 客户国籍代码（ISO3166 标准），必填
     */
    @NotBlank(message = "客户国籍代码不能为空")
    @JsonProperty("citizen_country_code")
    private String citizenCountryCode;

    /**
     * 电子邮件地址，必填
     */
    @NotBlank(message = "电子邮件地址不能为空")
    @JsonProperty("email_address")
    private String emailAddress;

    /**
     * 名字，必填
     */
    @NotBlank(message = "名字不能为空")
    @JsonProperty("given_name")
    private String givenName;

    /**
     * 姓氏，必填
     */
    @NotBlank(message = "姓氏不能为空")
    @JsonProperty("surname")
    private String surname;

    /**
     * 区号，必填
     */
    @NotBlank(message = "区号不能为空")
    @JsonProperty("mobile_area_city_code")
    private String mobileAreaCityCode;

    /**
     * 电话号码，必填
     */
    @NotBlank(message = "电话号码不能为空")
    @JsonProperty("mobile_phone_number")
    private String mobilePhoneNumber;
}
