package com.ixtech.global.common.dto;

import com.ixtech.global.common.constant.IxtechConstants;
import com.ixtech.global.common.exception.ApiException;
import com.ixtech.global.common.exception.inf.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.slf4j.MDC;

import java.io.Serializable;

/**
 * 数据传输结果集response
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
@AllArgsConstructor
public class ApiResponse<T> implements Serializable {

    private static final long serialVersionUID = 2326196004883935529L;
    private boolean success = true;

    // /**
    //  * 0 成功, 1 失败
    //  */
    // private int code;
    //
    // private String message = "";

    private T result;

    private ErrorData error;

    /** 用于跨服务追踪请求的唯一标识符 */
    private String traceId; // 新增此字段


    public ApiResponse(boolean success, int code, String message, T result) {
        this.success = success;
        // this.code = code;
        // this.message = message;
        this.result = result;
        error = new ErrorData();
        error.setMessage(message);
        error.setCode(String.valueOf(code));
        this.traceId = MDC.get(IxtechConstants.TRACE_ID2); // 初始化traceId
    }

    // 完整构造方法
    public ApiResponse(boolean success, T result, ErrorData error) {
        this.success = success;
        this.result = result;
        this.error = error;
        this.traceId = MDC.get(IxtechConstants.TRACE_ID2); // 初始化traceId
    }

    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>();
    }

    public static <T> ApiResponse<T> success(T result) {
        ApiResponse<T> res = new ApiResponse<>();
        res.setResult(result);
        return res;
    }

    public static <T> ApiResponse<T> success(T result, String message) {
        ApiResponse<T> res = new ApiResponse<>();
        // res.setMessage(message);
        res.setResult(result);
        return res;
    }

    public static <T> ApiResponse<T> fail(String message) {
        ApiResponse<T> res = new ApiResponse<>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage(message);
        errorData.setCode(String.valueOf(1));
        res.setError(errorData);
        // res.setCode(1);
        // res.setMessage(message);
        return res;
    }

    public static <T> ApiResponse<T> fail(String message, int code) {
        ApiResponse<T> res = new ApiResponse<>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage(message);
        errorData.setCode(String.valueOf(code));
        res.setError(errorData);
        return res;
    }

    public static <T> ApiResponse<T> fail(String message, T result) {
        ApiResponse<T> res = new ApiResponse<>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage(message);
        errorData.setCode(String.valueOf(1));
        res.setError(errorData);
        return res;
    }

    public static <T> ApiResponse<T> fail(T result) {
        ApiResponse<T> res = new ApiResponse<>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage("未定义的错误");
        errorData.setCode("1");
        res.setError(errorData);
        res.setResult(result);
        return res;
    }

    public static <T> ApiResponse<T> fail(T result, String message) {
        ApiResponse<T> res = new ApiResponse<>();
        res.setSuccess(false);
        ErrorData errorData = new ErrorData();
        errorData.setMessage(message);
        errorData.setCode("1");
        res.setError(errorData);
        // res.setResult(result);
        return res;
    }

    public static <T> ApiResponse<T> makeResult(Boolean success, int code, String message, T result, ErrorData errorData) {
        ApiResponse<T> res = new ApiResponse<>();
        res.setSuccess(success);
        // res.setCode(code);
        // res.setMessage(message);
        res.setResult(result);
        res.setError(errorData);
        return res;
    }

    public static <T> ApiResponse<T> fail(ErrorCode errorCode) {
        ErrorData errorData = new ErrorData();
        errorData.setCode(errorCode.getFullCode());
        errorData.setMessage(errorCode.getMessage());
        return new ApiResponse<>(false, null, errorData);
    }

    public static <T> ApiResponse<T> fail(ErrorCode errorCode, String message) {
        ErrorData errorData = new ErrorData();
        errorData.setCode(errorCode.getFullCode());
        errorData.setMessage(message);
        return new ApiResponse<>(false, null, errorData);
    }

    public static <T> ApiResponse<T> fail(String code, String message) {
        ErrorData errorData = new ErrorData();
        errorData.setCode(code);
        errorData.setMessage(message);
        return new ApiResponse<>(false, null, errorData);
    }

    /**
     * 构建支持错误文案国际化的接口返回对象
     *
     * @param errorCode
     * @param message
     * @param resourceKey
     * @return
     * @param <T>
     */
    public static <T> ApiResponse<T> failWithI18n(ErrorCode errorCode, String message, String resourceKey) {
        ErrorData errorData = new ErrorData();
        errorData.setCode(errorCode.getFullCode());
        errorData.setMessage(message);
        errorData.setResourceKey(resourceKey);
        return new ApiResponse<>(false, null, errorData);
    }

    public T parseResult() {
        if(this.isSuccess()){
            return this.getResult();
        }else {
            throw new ApiException(this.getError().getCode(), this.getError().getMessage());
        }
    }
    protected ApiResponse() {
        super();
        this.traceId = MDC.get(IxtechConstants.TRACE_ID2); // 初始化traceId

    }

    /**
     * 获取本次调用的trace_id
     *
     * @return
     */
    public String getTraceId() {
        return MDC.get(IxtechConstants.TRACE_ID2);
    }

}
