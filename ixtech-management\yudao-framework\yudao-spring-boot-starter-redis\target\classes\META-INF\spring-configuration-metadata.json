{"groups": [{"name": "yudao.cache", "type": "cn.iocoder.yudao.framework.redis.config.YudaoCacheProperties", "sourceType": "cn.iocoder.yudao.framework.redis.config.YudaoCacheProperties"}], "properties": [{"name": "yudao.cache.redis-scan-batch-size", "type": "java.lang.Integer", "description": "redis scan 一次返回数量", "sourceType": "cn.iocoder.yudao.framework.redis.config.YudaoCacheProperties"}], "hints": []}