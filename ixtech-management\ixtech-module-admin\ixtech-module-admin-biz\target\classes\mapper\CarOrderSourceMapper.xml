<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.CarOrderSourceMapper">
  <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.CarOrderSource">
    <!--@mbg.generated-->
    <!--@Table jipinzuche_car_order_source-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="active" jdbcType="BIT" property="active" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="stage" jdbcType="VARCHAR" property="stage" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="company_full_name" jdbcType="VARCHAR" property="companyFullName" />
    <result column="quote_currency" jdbcType="VARCHAR" property="quoteCurrency" />
    <result column="settlement_currency" jdbcType="VARCHAR" property="settlementCurrency" />
    <result column="settlement_mode" jdbcType="TINYINT" property="settlementMode" />
    <result column="rate" jdbcType="DECIMAL" property="rate" />
    <result column="credential_key" jdbcType="VARCHAR" property="credentialKey" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, active, deleted, create_time, update_time, title, stage, is_delete, company_full_name, 
    quote_currency, settlement_currency, settlement_mode, rate, credential_key
  </sql>
  <sql id="Ignore_Deleted">
    deleted = 0 AND is_delete = 0
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from jipinzuche_car_order_source
    where id = #{id,jdbcType=INTEGER,javaType=long}
    and
    <include refid="Ignore_Deleted" />
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from jipinzuche_car_order_source
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ixtech.management.repo.entity.CarOrderSource" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into jipinzuche_car_order_source (active, deleted, create_time, 
      update_time, title, stage, 
      is_delete, company_full_name, quote_currency, 
      settlement_currency, settlement_mode, rate
      )
    values (#{active,jdbcType=BIT}, #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{title,jdbcType=VARCHAR}, #{stage,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BIT}, #{companyFullName,jdbcType=VARCHAR}, #{quoteCurrency,jdbcType=VARCHAR}, 
      #{settlementCurrency,jdbcType=VARCHAR}, #{settlementMode,jdbcType=TINYINT}, #{rate,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ixtech.management.repo.entity.CarOrderSource" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into jipinzuche_car_order_source
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="active != null">
        active,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="stage != null">
        stage,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="companyFullName != null">
        company_full_name,
      </if>
      <if test="quoteCurrency != null">
        quote_currency,
      </if>
      <if test="settlementCurrency != null">
        settlement_currency,
      </if>
      <if test="settlementMode != null">
        settlement_mode,
      </if>
      <if test="rate != null">
        rate,
      </if>
      <if test="credentialKey != null">
        credential_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="active != null">
        #{active,jdbcType=BIT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        #{stage,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="companyFullName != null">
        #{companyFullName,jdbcType=VARCHAR},
      </if>
      <if test="quoteCurrency != null">
        #{quoteCurrency,jdbcType=VARCHAR},
      </if>
      <if test="settlementCurrency != null">
        #{settlementCurrency,jdbcType=VARCHAR},
      </if>
      <if test="settlementMode != null">
        #{settlementMode,jdbcType=TINYINT},
      </if>
      <if test="rate != null">
        #{rate,jdbcType=DECIMAL},
      </if>
      <if test="credentialKey != null">
        #{credentialKey,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ixtech.management.repo.entity.CarOrderSource">
    <!--@mbg.generated-->
    update jipinzuche_car_order_source
    <set>
      <if test="active != null">
        active = #{active,jdbcType=BIT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        stage = #{stage,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="companyFullName != null">
        company_full_name = #{companyFullName,jdbcType=VARCHAR},
      </if>
      <if test="quoteCurrency != null">
        quote_currency = #{quoteCurrency,jdbcType=VARCHAR},
      </if>
      <if test="settlementCurrency != null">
        settlement_currency = #{settlementCurrency,jdbcType=VARCHAR},
      </if>
      <if test="settlementMode != null">
        settlement_mode = #{settlementMode,jdbcType=TINYINT},
      </if>
      <if test="rate != null">
        rate = #{rate,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ixtech.management.repo.entity.CarOrderSource">
    <!--@mbg.generated-->
    update jipinzuche_car_order_source
    set active = #{active,jdbcType=BIT},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      title = #{title,jdbcType=VARCHAR},
      stage = #{stage,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=BIT},
      company_full_name = #{companyFullName,jdbcType=VARCHAR},
      quote_currency = #{quoteCurrency,jdbcType=VARCHAR},
      settlement_currency = #{settlementCurrency,jdbcType=VARCHAR},
      settlement_mode = #{settlementMode,jdbcType=TINYINT},
      rate = #{rate,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update jipinzuche_car_order_source
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="active = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.title,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="stage = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.stage,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_delete = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BIT}
        </foreach>
      </trim>
      <trim prefix="company_full_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.companyFullName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="quote_currency = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.quoteCurrency,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="settlement_currency = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.settlementCurrency,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="settlement_mode = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.settlementMode,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.rate,jdbcType=DECIMAL}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update jipinzuche_car_order_source
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="active = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.active != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
          </if>
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deleted != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.title != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.title,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="stage = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stage != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.stage,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_delete = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BIT}
          </if>
        </foreach>
      </trim>
      <trim prefix="company_full_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyFullName != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.companyFullName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="quote_currency = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.quoteCurrency != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.quoteCurrency,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="settlement_currency = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settlementCurrency != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.settlementCurrency,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="settlement_mode = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settlementMode != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.settlementMode,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.rate != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.rate,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into jipinzuche_car_order_source
    (active, deleted, create_time, update_time, title, stage, is_delete, company_full_name, 
      quote_currency, settlement_currency, settlement_mode, rate)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.active,jdbcType=BIT}, #{item.deleted,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.title,jdbcType=VARCHAR}, #{item.stage,jdbcType=VARCHAR}, 
        #{item.isDelete,jdbcType=BIT}, #{item.companyFullName,jdbcType=VARCHAR}, #{item.quoteCurrency,jdbcType=VARCHAR}, 
        #{item.settlementCurrency,jdbcType=VARCHAR}, #{item.settlementMode,jdbcType=TINYINT}, 
        #{item.rate,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <select id="searchByCondition" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM jipinzuche_car_order_source
    <where>
      <include refid="Ignore_Deleted"/>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="channelName != null and channelName != ''">
        AND title LIKE CONCAT('%', REPLACE(#{channelName,jdbcType=VARCHAR}, '_', '\\_'), '%')
      </if>
      <if test="companyFullName != null and companyFullName != ''">
        AND company_full_name LIKE CONCAT('%', REPLACE(#{companyFullName,jdbcType=VARCHAR}, '_', '\\_'), '%')
      </if>
    </where>
    ORDER BY id DESC
  </select>

  <select id="selectByStage" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM jipinzuche_car_order_source
    <where>
      <include refid="Ignore_Deleted"/>
      AND stage = #{stage}
    </where>
    ORDER BY id DESC
  </select>

  <select id="findAllByJipinzucheCarOrderSource"
          resultType="com.ixtech.management.repo.entity.CarOrderSource">
    select <include refid="Base_Column_List" />
    from jipinzuche_car_order_source
    where
        active = 1 and
    <include refid="Ignore_Deleted" />
  </select>
  <select id="selectByTitle" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from jipinzuche_car_order_source
    where
    title = #{title} and
    <include refid="Ignore_Deleted" />
  </select>

</mapper>