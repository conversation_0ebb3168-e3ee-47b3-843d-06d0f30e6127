package com.ixtech.global.common.enums;

import lombok.Getter;

/**
 * 里程单位枚举类
 */
@Getter
public enum InsuranceStatusEnum {

    ON(1, "启用"),

    OFF(2, "禁用"),

    RE_OPEN(3, "启用");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 构造方法
     *
     * @param code 码
     * @param name 单位名称
     */
    InsuranceStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static InsuranceStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InsuranceStatusEnum type : InsuranceStatusEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
