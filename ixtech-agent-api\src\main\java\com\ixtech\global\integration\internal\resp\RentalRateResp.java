package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 租赁费率 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RentalRateResp implements Serializable {

 private static final long serialVersionUID = 1L;


 /**
  * 参考ID,搜索唯一的维度：供应商+门店+SIPP+车辆modelId+某一保险套餐
  */
 private String referenceId;

 /**
  * 确认小时数
  */
 private Integer confirmHours;

 /**
  * 能源规则(1-full to full; 2-same to same)
  */
 private Integer energyRule;

 /**
  * 是否即时确认
  */
 private Boolean instantConfirm;

 /**
  * 保险信息列表
  */
 private List<PricedCoverage> pricedCoverages;

 /**
  * 特殊设备列表，车辆详情所需字段
  */
 private List<PricedEquipmentResp> pricedEquips;

 /**
  * 费率距离信息
  */
 private RateDistance rateDistance;

 /**
  * 费率价格信息
  */
 private RatePrice ratePrice;

 /**
  * 费率限定符信息
  */
 private RateQualifierResp rateQualifier;

 /**
  * 车辆费用数组，车辆租金、门店加班费、附加设备、保险、异地还车费等
  */
 private List<VehicleCharge> vehicleCharges;


 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class PricedCoverage {

  /**
   * 覆盖类型
   */
  private String coverageType;

  /**
   * 费用
   */
  private AmountResp priceAmount;

  /**
   * 保险子项明细
   */
  private List<CoverageDetail> coverageDetails;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class RateDistance {
  /**
   * 每单位额外金额
   */
  private AmountResp additionalAmountPerUnit;

  /**
   * 额外金额上限
   */
  private AmountResp additionalAmountUpperLimit;

  /**
   * 距离单位名称
   */
  private String distUnitName;

  /**
   * 免费里程类型
   */
  private String freeMileType;

  /**
   * 最大免费距离
   */
  private BigDecimal maxFreeDistance;

  /**
   * 是否无限制
   */
  private Boolean unlimited;
 }


 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class RatePrice {

  /**
   * 预估总金额
   */
  private AmountResp estimatedTotalAmount;

  /**
   * 支付模式,1-到店支付，2-预付，3-部分预付
   */
  private Integer payMode;

  /**
   * 到达时支付金额
   */
  private RatePayAmountResp ratePayOnArrivedAmount;

  /**
   * 预支付金额
   */
  private RatePayAmountResp ratePrepaidAmount;
 }


 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class VehicleCharge {

  /**
   * 费用
   */
  private AmountResp charge;

  /**
   * 费用代码
   */
  private String code;

  /**
   * 费用用途
   */
  private Integer purpose;

  /**
   * 税费金额
   */
  private TaxAmountResp taxAmount;
 }

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class CoverageDetail {

  /**
   * 保险子项名称
   */
  private String name;

  /**
   * 保险子项类型
   */
  private String type;

  /**
   * 保险起赔额
   */
  private BigDecimal franchiseAmount;

  /**
   * 保险起赔额-最小值
   */
  private BigDecimal minFranchiseAmount;

  /**
   * 保险起赔额-最大值
   */
  private BigDecimal maxFranchiseAmount;

  /**
   * 保险起赔额
   */
  private BigDecimal minCoverageAmount;

  /**
   * 保险起保额
   */
  private BigDecimal maxCoverageAmount;

  /**
   * 货币类型
   */
  private String currency;

  /**
   * 保险子项描述
   */
  private String description;
 }

}
