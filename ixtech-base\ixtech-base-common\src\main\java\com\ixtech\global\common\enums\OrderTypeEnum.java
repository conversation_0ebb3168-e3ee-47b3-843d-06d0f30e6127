package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 业务类型枚举
 * 对应表: platform_order, 字段: order_type
 * (1-租车，2-接送机，3-包车)
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum implements DictInf {

    RENTAL(1, "租车"),
    TRANSFER(2, "接送机"),
    CHARTER(3, "包车"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static OrderTypeEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
