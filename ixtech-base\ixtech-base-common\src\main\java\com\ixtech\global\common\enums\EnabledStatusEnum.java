package com.ixtech.global.common.enums;


import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EnabledStatusEnum implements DictInf {


    ENABLED(1, "启用"), // value 改为 Integer 类型

    DISABLED(2, "禁用"); // value 改为 Integer 类型

    /**
     * 状态值
     */
    private final Integer code; // 字段类型从 String 改为 Integer

    /**
     * 状态名称
     */
    private final String label;


    public static String getLabelByCode(String code) {
        if (code == null) {
            return null;
        }
        for (EnabledStatusEnum enabledStatusEnum : EnabledStatusEnum.values()) {
            if (enabledStatusEnum.getValue().equals(code)) { // 修复：使用 equals 比较字符串
                return enabledStatusEnum.getLabel();
            }
        }
        return null;
    }

    @Override
    public String getValue() {
        return String.valueOf(code);
    }
}
