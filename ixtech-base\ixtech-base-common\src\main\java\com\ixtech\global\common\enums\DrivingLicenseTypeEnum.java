package com.ixtech.global.common.enums;

/**
 * 驾照类型枚举
 */
public enum DrivingLicenseTypeEnum {
    ADLT("ADLT", "Avis车行翻译件"),
    BDLT("BDLT", "Budget车行翻译件"),
    NAATI("NAATI", "NAATI翻译件"),
    NZTA("NZTA", "NATA认证翻译件"),
    MCDL("MCDL", "澳门驾照"),
    DLT("DLT", "车行翻译件"),
    LDT("LDT", "当地驾照"),
    DNLTDL("DNLTDL", "当地临时驾照"),
    OLT("OLT", "当地语言公证件"),
    DST("DST", "德文宣誓翻译件"),
    IDP("IDP", "国际驾照"),
    IDP_1949("IDP(1949)", "国际驾照（1949年版）原件"),
    IDP_1968("IDP(1968)", "国际驾照（1968年版）原件"),
    IDLNZ("IDLNZ", "国际驾照翻译认证件（内含NZTA认证）"),
    KDL("KDL", "韩国驾照"),
    ODL("ODL", "驾驶员本国驾照"),
    ADL("ADL", "美国驾照"),
    EDL("EDL", "欧盟驾照"),
    JAFDLT("JAFDLT", "日本车辆联盟JAF翻译件"),
    DLJT("DLJT", "日本翻译件"),
    JDL("JDL", "日本驾照"),
    RJZ("RJZ", "入境纸"),
    EMB("EMB", "使馆认证件"),
    TDPAASL("TDPAASL", "斯里兰卡临时驾照"),
    TWDL("TWDL", "台湾驾照"),
    TXRTZ("TXRTZ", "通行证+入台证"),
    CDL_OLTES("CDL+OLTES", "西班牙公证件"),
    OLTES("OLTES", "西班牙文公证件"),
    HKDL("HKDL", "香港驾照"),
    OET("OET", "英文公证件"),
    EODL("EODL", "英文驾照"),
    VMDLT("VMDLT", "越南交通部颁发的授权译文"),
    CDL("CDL", "中国大陆驾驶照原件"),
    APOLT("APOLT", "中国外交部附加证明书");

    private final String code;      // 驾照代码（如 ADLT、BDLT）
    private final String description;  // 驾照描述（如 "Avis车行翻译件"）

    DrivingLicenseTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据 code 查找对应的枚举
     */
    public static DrivingLicenseTypeEnum fromCode(String code) {
        for (DrivingLicenseTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的驾照类型: " + code);
    }

    /**
     * 根据 description 查找对应的枚举
     */
    public static DrivingLicenseTypeEnum fromDescription(String description) {
        for (DrivingLicenseTypeEnum type : values()) {
            if (type.description.equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的驾照描述: " + description);
    }
}