package com.ixtech.management.common.validator;

import com.ixtech.management.config.ExcelHeaderCheckException;
import com.ixtech.management.integration.internal.resp.CarOrderImportRespVO;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.*;

import static com.ixtech.management.domain.service.impl.CarOrderServiceImpl.fileOrderSourceId;

public interface ExcelHeaderValidator {
    /**
     * 校验表头
     */
    void validateHeaders(List<String> headers) throws ExcelHeaderCheckException;

    /**
     * 校验单行数据
     */
    void validateRowData(Integer sourceId, Set<String> sourceIdSet, Row row, CarOrderImportRespVO.CarOrderCheckOrImportResult lineInfo) throws ExcelHeaderCheckException;

    /**
     * 批量校验所有行
     */
    default void validateAllRows(Integer sourceId, Long fileVersionId, Sheet sheet
            , Map<String, List<CarOrderImportRespVO.CarOrderCheckOrImportResult>> list)
            throws ExcelHeaderCheckException {
        Set<String> sourceIdSet = new HashSet<>();
        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> checkSuccessData = new ArrayList<>();
        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> checkErrorData = new ArrayList<>();
        List<CarOrderImportRespVO.CarOrderCheckOrImportResult> checkAllData = new ArrayList<>();

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            CarOrderImportRespVO.CarOrderCheckOrImportResult lineInfo =
                    new CarOrderImportRespVO.CarOrderCheckOrImportResult();
            lineInfo.setId(i+1);
            validateRowData(sourceId, sourceIdSet, sheet.getRow(i), lineInfo);
            checkAllData.add(lineInfo);
            if (lineInfo.getIsCheckSucceed()) {
                checkSuccessData.add(lineInfo);
                continue;
            }
            checkErrorData.add(lineInfo);
        }
        list.put("checkSuccessData", checkSuccessData);
        list.put("checkErrorData", checkErrorData);
        list.put("checkAllData", checkAllData);
        fileOrderSourceId.put(fileVersionId, sourceIdSet);
    }
}
