package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 车辆信息 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehicleResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 车辆代码
  */
 private String code;
 //
 // /**
 //  * 车辆modelId
 //  */
 // private Long modelId;

 /**
  * 品牌名称
  */
 private String brandName;

 /**
  * 型号名称
  */
 private String modelName;
}
