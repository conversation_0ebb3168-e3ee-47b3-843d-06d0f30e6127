package com.ixtech.management.repo.mapper;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.MerchantTenantDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 租户 Mapper
 *
 * <AUTHOR>
 */
@DS("merchant")
@Mapper
public interface MerchantTenantMapper extends BaseMapperX<MerchantTenantDO> {

    default PageResult<MerchantTenantDO> selectPage(TenantPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MerchantTenantDO>()
                .likeIfPresent(MerchantTenantDO::getName, reqVO.getName())
                .likeIfPresent(MerchantTenantDO::getContactName, reqVO.getContactName())
                .likeIfPresent(MerchantTenantDO::getContactMobile, reqVO.getContactMobile())
                .eqIfPresent(MerchantTenantDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(MerchantTenantDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MerchantTenantDO::getId));
    }

    default MerchantTenantDO selectByName(String name) {
        return selectOne(MerchantTenantDO::getName, name);
    }

    default MerchantTenantDO selectByWebsite(String website) {
        return selectOne(MerchantTenantDO::getWebsite, website);
    }

    default Long selectCountByPackageId(Long packageId) {
        return selectCount(MerchantTenantDO::getPackageId, packageId);
    }

    default List<MerchantTenantDO> selectListByPackageId(Long packageId) {
        return selectList(MerchantTenantDO::getPackageId, packageId);
    }

}
