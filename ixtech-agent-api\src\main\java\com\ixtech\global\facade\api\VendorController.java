package com.ixtech.global.facade.api;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.util.ErrorRespUtils;
import com.ixtech.global.domain.service.IVendorService;
import com.ixtech.global.integration.internal.req.RuleSearchReq;
import com.ixtech.global.integration.internal.req.VehicleSearchReq;
import com.ixtech.global.integration.internal.req.VendorSearchReq;
import com.ixtech.global.integration.internal.resp.LocationSearchResp;
import com.ixtech.global.integration.internal.resp.RuleSearchResp;
import com.ixtech.global.integration.internal.resp.VehicleSearchResp;
import com.ixtech.global.integration.internal.resp.VendorSearchResp;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * agentapi 供应商相关API
 *
 * <AUTHOR>
 * @date 2025-03-31
 */
@Slf4j
@RestController
@RequestMapping("/agentopenapi/api")
public class VendorController {

    @Resource(name = "vendorServiceV2")
    private IVendorService vendorService;

    /**
     * 供应商静态数据查询
     */
    @PostMapping("/vendor_search")
    public ApiResponse<List<VendorSearchResp>> vendorSearch(@RequestBody VendorSearchReq request) {
        pageJudge(request);
        ApiResponse<List<VendorSearchResp>> resp = vendorService.vendorSearch(request);
        return ErrorRespUtils.checkServiceResponse(resp, "供应商静态数据查询失败");
    }

    /**
     * 供应商门店静态数据查询
     */
    @PostMapping("/location_search")
    public ApiResponse<List<LocationSearchResp>> locationSearch(@RequestBody VendorSearchReq request) {
        pageJudge(request);
        ApiResponse<List<LocationSearchResp>> resp = vendorService.locationSearch(request);
        return ErrorRespUtils.checkServiceResponse(resp, "供应商门店静态数据查询失败");
    }

    /**
     * 供应商的车型数据查询
     * 返回车型、手/自动档、发动机类型、车尺寸、行李数、乘客数、车图等
     */
    @PostMapping("/vehicle_search")
    public ApiResponse<List<VehicleSearchResp>> vehicleSearch(@RequestBody @Valid VehicleSearchReq request) {
        pageJudge(request);
        ApiResponse<List<VehicleSearchResp>> resp = vendorService.vehicleSearch(request);
        return ErrorRespUtils.checkServiceResponse(resp, "供应商车型数据查询失败");
    }

    /**
     * 供应商政策规则
     */
    @PostMapping("/rule_search")
    public ApiResponse<List<RuleSearchResp>> ruleSearch(@RequestBody @Valid RuleSearchReq request) {
        pageJudge(request);
        ApiResponse<List<RuleSearchResp>> resp = vendorService.ruleSearch(request);
        return ErrorRespUtils.checkServiceResponse(resp, "供应商政策规则查询失败");
    }

    /**
     * 分页参数校验
     */
    private void pageJudge(VendorSearchReq request) {
        // if (request.getPageNum() <= NormalConstant.NUM_ZERO || request.getPageSize() > NormalConstant.NUM_ONE_HUNDRED) {
        //     throw new BizException("分页参数异常");
        // }
    }

}
