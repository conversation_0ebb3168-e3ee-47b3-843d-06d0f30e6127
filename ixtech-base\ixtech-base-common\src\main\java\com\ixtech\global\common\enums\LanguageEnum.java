package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.Getter;

@Getter
public enum LanguageEnum implements DictInf {

    CHINESE("zh_1", "中文", "zh", 1),
    ENGLISH("en_1", "英语", "en", 1),
    JAPANESE("ja_1", "日语", "ja", 1),
    KOREAN("ko_1", "韩语", "ko", 1),
    ;

    LanguageEnum(String code, String name, String isoCode, Integer isoCodeType) {
        this.code = code;
        this.name = name;
        this.isoCode = isoCode;
        this.isoCodeType = isoCodeType;
    }

    /**
     * 语言code（isoCode_isoCodeType）
     */
    private final String code;

    /**
     * 语言名
     */
    private final String name;

    /**
     * 语言ISO代码
     */
    private final String isoCode;

    /**
     * ISO代码类型 1:ISO 639-1 2:ISO 639-2 3:ISO 639-3
     */
    private final Integer isoCodeType;

    @Override
    public String getValue() {
        return isoCode;
    }

    @Override
    public String getLabel() {
        return name;
    }
}
