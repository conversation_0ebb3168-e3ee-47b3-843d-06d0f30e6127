<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.global.repo.mapper.TestMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.global.repo.entity.User">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    </resultMap>


    <select id="selectUsersByParams" resultType="com.ixtech.global.repo.entity.User">
        select id, nickname
        from jipinzuche_user
        where id > #{first} limit #{limit}
    </select>

</mapper>