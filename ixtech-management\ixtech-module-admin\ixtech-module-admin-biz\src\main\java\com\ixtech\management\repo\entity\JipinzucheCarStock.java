package com.ixtech.management.repo.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 车辆库存表
 * @TableName jipinzuche_car_stock
 */
@Data
public class JipinzucheCarStock implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * #__car_model 的id
     */
    private Integer modelid;

    /**
     * 门店id
     */
    private Integer storeid;

    /**
     * 车辆数量
     */
    private Integer stock;

    /**
     * 同组名
     */
    private String groupName;

    /**
     * 起赔额
     */
    private Double accident;

    /**
     * 起赔额单位(如：RMB，THB)
     */
    private String accidentUnit;

    /**
     * 添加人
     */
    private Long time;

    /**
     * ip
     */
    private String ip;

    /**
     * 添加人
     */
    private Integer mid;

    /**
     * 1->有效；-1->删除
     */
    private Integer status;

    private static final long serialVersionUID = 1L;
}