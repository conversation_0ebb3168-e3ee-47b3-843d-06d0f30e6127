<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ixtech.global</groupId>
        <artifactId>ixtech-base-starters</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ixtech-base-mysql-starter</artifactId>
    <packaging>jar</packaging>



    <properties>
        <java.version>21</java.version>
        <project.basedir>${basedir}/../..</project.basedir>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ixtech.global</groupId>
            <artifactId>ixtech-base-common</artifactId>
        </dependency>
    </dependencies>


</project>
