package com.ixtech.global.common.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.ixtech.global.common.exception.ServerException;
import com.ixtech.global.common.exception.enums.CommonErrorCode;
import org.apache.poi.ss.usermodel.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.ixtech.global.common.exception.enums.CommonErrorCode.FILE_ERROR;
import static com.ixtech.global.common.exception.enums.CommonErrorCode.FILE_FORMAT_INVALID;

public class ExcelUtils {

    /**生成一个读取数据库数据产生的excel字节文件
     *  最普通的EasyExcel用法
     *  返回excel文件数据流
     */
    public static ByteArrayOutputStream outputExcelToStream(List<?> list, Class<?> clazz,String sheetName, ExcelTypeEnum excelTypeEnum) throws Exception{
        //上传文件的命名规则，暂时没写
//        String extension = ExcelTypeEnum.XLSX.getValue();

        //建立一个字节数组输出流，将excel文件的内容存入其中
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        /**
         *   这里指定用哪个class去写，然后写到第一个sheet，名字为模板，然后文件流会自动关闭
         *   这里可以定义表格的各种样式
         *   out为字节流
         *   clazz为实体类的反射
         */
        EasyExcel.write(out, clazz).excelType(excelTypeEnum).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet(sheetName).doWrite(list);

        return out;
    }



    /**
     * 按行读取 Excel 数据（强制所有单元格按字符串处理，和 readExcel 方法行为一致）
     * @param fileBytes Excel 文件字节数组
     * @return List<List<String>> 每行数据（所有值强制转为字符串）
     */
    public static List<List<String>> readExcelAsRawRows(byte[] fileBytes) throws Exception {
        try (InputStream inputStream = new ByteArrayInputStream(fileBytes);
             Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            List<List<String>> rows = new ArrayList<>();

            // 计算最大列数
            int maxColumns = 0;
            for (Row row : sheet) {
                int lastCellNum = row.getLastCellNum();
                if (lastCellNum > maxColumns) {
                    maxColumns = lastCellNum;
                }
            }

            // 读取每行数据
            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();
                for (int i = 0; i < maxColumns; i++) {
                    Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    cell.setCellType(CellType.STRING); // 强制按字符串读取
                    rowData.add(cell.getStringCellValue()); // 直接获取字符串值
                }
                rows.add(rowData);
            }
            return rows;
        } catch (Exception e) {
            throw ServerException.of(CommonErrorCode.FILE_ERROR);
        }
    }

    private static String getRawCellValue(Cell cell) {
        if (cell == null || cell.getCellType() == CellType.BLANK) {
            return null; // 空单元格返回 null
        }
        return cell.toString(); // 返回文本值
    }

    public static List<List<String>> readExcel(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".xls") || fileName.endsWith(".xlsx"))) {
            throw ServerException.of(CommonErrorCode.FILE_FORMAT_INVALID);
        }

        try (
                InputStream inputStream = file.getInputStream();
                Workbook workbook = WorkbookFactory.create(inputStream);
        ) {
            Sheet sheet = workbook.getSheetAt(0);
            List<List<String>> data = new ArrayList<>();
            int maxColumns = 0;

            // 计算最大列数
            for (Row row : sheet) {
                int lastCellNum = row.getLastCellNum();
                if (lastCellNum > maxColumns) {
                    maxColumns = lastCellNum;
                }
            }

            // 读取每行数据
            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();
                for (int i = 0; i < maxColumns; i++) {
                    Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    cell.setCellType(CellType.STRING); // 强制文本格式
                    String value = getRawCellValue(cell);
                    rowData.add(value);
                }
                data.add(rowData);
            }
            return data;
        } catch (Exception e) {
            throw new IOException("文件读取失败: " + e.getMessage(), e);
        }
    }

    public static byte[] insertColumn(byte[] originalBytes,
                                List<String> columnData) throws Exception {

        try (InputStream is = new ByteArrayInputStream(originalBytes);
             Workbook workbook = WorkbookFactory.create(is);
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            Sheet sheet = workbook.getSheetAt(0);

            // 第一步：删除第一行说明注释数据
            if (sheet.getRow(0) != null) {
                sheet.removeRow(sheet.getRow(0));
                // 移动剩余行向上填补
                sheet.shiftRows(1, sheet.getLastRowNum(), -1);
            }

            // 第二步：插入新列（所有列右移）
            int lastColumnNum = sheet.getRow(0) != null ? sheet.getRow(0).getLastCellNum() : 0;
            sheet.shiftColumns(0, lastColumnNum, 1);

            // 记录需要删除的行索引（从后往前删除避免索引错位）
            List<Integer> rowsToDelete = new ArrayList<>();
            int lastRowNum = sheet.getLastRowNum();

            // 第三步：填充数据（现在行索引已经减1，因为删除了首行）
            for (int i = 0; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                // 检查columnData是否有足够的数据
                if (i < columnData.size()) {
                    String cellValue = columnData.get(i);
                    if (cellValue == null) {
                        // 记录需要删除的行
                        rowsToDelete.add(i);
                    } else {
                        // 创建新单元格并设置值
                        Cell cell = row.createCell(0);
                        cell.setCellValue(cellValue);
                    }
                } else {
                    // 如果columnData不足，插入空值
                    row.createCell(0);
                }
            }

            // 第四步：从后往前删除包含null值的行
            Collections.sort(rowsToDelete, Collections.reverseOrder());
            for (int rowIndex : rowsToDelete) {
                Row row = sheet.getRow(rowIndex);
                if (row != null) {
                    sheet.removeRow(row);
                    // 移动剩余行向上填补
                    if (rowIndex < sheet.getLastRowNum()) {
                        sheet.shiftRows(rowIndex + 1, sheet.getLastRowNum(), -1);
                    }
                }
            }
            // 可选：确保行号连续
            for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                if (sheet.getRow(i) == null) {
                    sheet.createRow(i);
                }
            }

            workbook.write(out);
            return out.toByteArray();
        }
    }

}