package com.ixtech.management.domain.service.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.domain.service.RentalOrderService;
import com.ixtech.management.integration.internal.client.OrdersrvFeignClient;
import com.ixtech.management.integration.internal.client.VendormanagementsrvClientV2;
import com.ixtech.management.integration.internal.req.MerchantRentalOrderDetailReq;
import com.ixtech.management.integration.internal.req.OrderPageQueryReq;
import com.ixtech.management.integration.internal.req.OrderTransferReq;
import com.ixtech.management.integration.internal.req.common.SelectOption;
import com.ixtech.management.integration.internal.req.common.SelectOptionReq;
import com.ixtech.management.integration.internal.resp.MerchantRentalOrderResp;
import com.ixtech.management.integration.internal.resp.OrderQueryResp;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RentalOrderServiceImpl implements RentalOrderService {

    @Resource
    private OrdersrvFeignClient ordersrvFeignClient;

    @Resource
    private VendormanagementsrvClientV2 vendormanagementsrvClientV2;

    @Override
    public ApiResponse<PageResult<OrderQueryResp>> listByPage(OrderPageQueryReq req) {
        return ordersrvFeignClient.listByPage(req);
    }

    @Override
    public ApiResponse<MerchantRentalOrderResp> getOrderDetail(MerchantRentalOrderDetailReq req) {
        return ordersrvFeignClient.getOrderDetail(req);
    }

    @Override
    public ApiResponse<List<SelectOption>> storeList(SelectOptionReq req) {
        return vendormanagementsrvClientV2.getStoreList(req);
    }

    @Override
    public ApiResponse<List<SelectOption>> vehicleGroupList(SelectOptionReq req) {
        return vendormanagementsrvClientV2.getVehicleGroupList(req);
    }

    @Override
    public ApiResponse<List<SelectOption>> getChannelList(SelectOptionReq req) {
        return vendormanagementsrvClientV2.getChannelList(req);
    }

    @Override
    public ApiResponse<Boolean> orderTransfer(OrderTransferReq req) {
        return ordersrvFeignClient.orderTransfer(req);
    }


}
