package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 地点信息对象，用于表示取车或还车地点。
 */
@Data
public class LocationReq {

    /**
     * 地点代码上下文（如 "IATA"），可选
     */
    @JsonProperty("location_code_context")
    private String locationCodeContext;

    /**
     * 地点代码（如 "SFOC28"），必填
     */
    @NotBlank(message = "地点代码不能为空")
    @JsonProperty("location_code")
    private String locationCode;
}
