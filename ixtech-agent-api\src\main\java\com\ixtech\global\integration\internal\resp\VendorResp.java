package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 供应商信息 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VendorResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 供应商代码
  */
 private String code;

 /**
  * 供应商名称
  */
 private String name;
}
