package com.ixtech.global.common.listener.event;

import org.springframework.context.ApplicationEvent;

/**
 * 应用服务启动校验事件类
 *
 * <AUTHOR>
 * @date 2025-3-12
 */
public class ApplicationStartCheckEvent extends ApplicationEvent {
   private final String applicationName;
   private final String port;

   public ApplicationStartCheckEvent(Object source, String applicationName, String port) {
    super(source);
    this.applicationName = applicationName;
    this.port = port;
   }

   public String getApplicationName() {
    return applicationName;
   }

    public String getPort() {
        return port;
    }

}
