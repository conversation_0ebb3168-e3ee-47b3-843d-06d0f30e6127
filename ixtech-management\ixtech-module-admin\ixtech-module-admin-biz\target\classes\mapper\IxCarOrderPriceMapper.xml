<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.IxCarOrderPriceMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.IxCarOrderPrice">
            <id property="id" column="id" />
            <result property="active" column="active" />
            <result property="deleted" column="deleted" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="carOrderId" column="car_order_id" />
            <result property="prepaidAmount" column="prepaid_amount" />
            <result property="cashOnDeliveryAmount" column="cash_on_delivery_amount" />
            <result property="oneWayFee" column="one_way_fee" />
            <result property="specialPickupTimeFee" column="special_pickup_time_fee" />
            <result property="specialReturnTimeFee" column="special_return_time_fee" />
            <result property="buyInsurance" column="buy_insurance" />
            <result property="settlementAmount" column="settlement_amount" />
            <result property="localCurrency" column="local_currency" />
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,car_order_id,
        prepaid_amount,cash_on_delivery_amount,one_way_fee,special_pickup_time_fee,special_return_time_fee,
        buy_insurance,settlement_amount,local_currency
    </sql>

    <sql id="Base_delete">
        active = 1 and deleted = 0
    </sql>

    <select id="selectByCarOrderId" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from ix_car_order_price
        where car_order_id = #{carOrderId}
        and
            <include refid="Base_delete" />
    </select>

    <insert id="insertOne">
        INSERT INTO ix_car_order_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carOrderId!=null">
                car_order_id,
            </if>
            <if test="prepaidAmount!=null">
                prepaid_amount,
            </if>
            <if test="cashOnDeliveryAmount!=null">
                cash_on_delivery_amount,
            </if>
            <if test="oneWayFee!=null">
                one_way_fee,
            </if>
            <if test="specialPickupTimeFee!=null">
                special_pickup_time_fee,
            </if>
            <if test="specialReturnTimeFee!=null">
                special_return_time_fee,
            </if>
            <if test="buyInsurance!=null">
                buy_insurance,
            </if>
            <if test="settlementAmount!=null">
                settlement_amount,
            </if>
            <if test="localCurrency!=null">
                local_currency,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carOrderId!=null">
                #{carOrderId},
            </if>
            <if test="prepaidAmount!=null">
                #{prepaidAmount},
            </if>
            <if test="cashOnDeliveryAmount!=null">
                #{cashOnDeliveryAmount},
            </if>
            <if test="oneWayFee!=null">
                #{oneWayFee},
            </if>
            <if test="specialPickupTimeFee!=null">
                #{specialPickupTimeFee},
            </if>
            <if test="specialReturnTimeFee!=null">
                #{specialReturnTimeFee},
            </if>
            <if test="buyInsurance!=null">
                #{buyInsurance},
            </if>
            <if test="settlementAmount!=null">
                #{settlementAmount},
            </if>
            <if test="localCurrency!=null">
                #{localCurrency},
            </if>
        </trim>
    </insert>


</mapper>
