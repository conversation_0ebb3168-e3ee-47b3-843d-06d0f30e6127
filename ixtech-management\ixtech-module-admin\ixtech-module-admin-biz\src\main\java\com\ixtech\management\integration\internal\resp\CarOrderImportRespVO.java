package com.ixtech.management.integration.internal.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 车辆订单导入 Response VO")
@Data
public class CarOrderImportRespVO {

    private Boolean isAllSucceed;

    private List<CarOrderCheckOrImportResult> resultList;

    @Data
    public static class CarOrderCheckOrImportResult {
        private Integer id;

        private String sourceOrdercode;

        private Boolean isCheckSucceed;

        private String checkFailReason = "";

        private Boolean isImportSucceed;

        private String importFailReason = "";

        private Boolean isNoted;

        private String note;
    }
}
