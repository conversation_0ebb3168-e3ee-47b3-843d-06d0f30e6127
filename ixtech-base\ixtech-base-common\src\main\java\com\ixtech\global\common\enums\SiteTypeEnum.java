package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 店铺类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SiteTypeEnum implements DictInf {

    /**
     * 机场店
     */
    AIRPORT(1, "机场店","IATA"),

    /**
     * 市中心店
     */
    CITY_CENTER(2, "市中心店",""),

    /**
     * 火车站店
     */
    TRAIN_STATION(3, "火车站店","");

    /**
     * 类型值
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String label;

    /**
     * 类型名称
     */
    private final String shortForm;

    @Override
    public String getValue() {
        return String.valueOf(code);
    }

    public static String getLabelByValue(String value) {
        for (SiteTypeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e.label;
            }
        }
        return "";
    }
}
