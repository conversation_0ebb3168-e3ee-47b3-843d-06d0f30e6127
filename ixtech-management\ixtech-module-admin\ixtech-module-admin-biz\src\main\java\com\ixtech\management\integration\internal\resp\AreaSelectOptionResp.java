package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 区域响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 17:16
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AreaSelectOptionResp {

    /**
     * 父区域id
     */
    private Long parentId;

    /**
     * 区域id
     */
    private Long id;

    /**
     * 区域名称
     */
    private String name;

    /**
     * 区域类型 1：国家 2：省 3：市 4：区
     */
    private Integer category;

    /**
     * 下级区域
     */
    private List<AreaSelectOptionResp> childList;

}
