package com.ixtech.global;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import io.micrometer.tracing.Tracer;
import io.micrometer.tracing.propagation.Propagator;

import java.util.Objects;

/**
 * feign请求时对请求头增加traceId
 *
 * <AUTHOR> hu
 * @date 2025/5/20 16:43
 */
public class FeignTracingInterceptor implements RequestInterceptor {

    private Tracer tracer;
    private Propagator propagator;

    public FeignTracingInterceptor(Tracer tracer, Propagator propagator) {
        this.tracer = tracer;
        this.propagator = propagator;
    }

    @Override
    public void apply(RequestTemplate template) {
        propagator.inject(Objects.requireNonNull(tracer.currentTraceContext().context()),
                template,
                (requestTemplate, key, value) -> {
                    assert requestTemplate != null;
                    requestTemplate.header(key, value);
                });
    }

}
