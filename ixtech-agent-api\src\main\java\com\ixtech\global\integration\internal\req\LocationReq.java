package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 门店地点req
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LocationReq implements Serializable {

 private static final long serialVersionUID = 7985504938627085943L;

 /**
  * 地点代码上下文，如 IATA、ARC
  */
 private String locationCodeContext;

 /**
  * 地点代码
  */
 private String locationCode;
}
