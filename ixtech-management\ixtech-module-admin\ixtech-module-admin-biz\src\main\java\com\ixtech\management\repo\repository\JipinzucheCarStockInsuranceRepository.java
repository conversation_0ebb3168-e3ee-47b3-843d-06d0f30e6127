package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.JipinzucheCarStockInsurance;
import com.ixtech.management.repo.mapper.JipinzucheCarStockInsuranceMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class JipinzucheCarStockInsuranceRepository {

    @Resource
    private JipinzucheCarStockInsuranceMapper jipinzucheCarStockInsuranceMapper;

    public JipinzucheCarStockInsurance selectById(Long id){
        return jipinzucheCarStockInsuranceMapper.selectById(id);
    }

    public List<JipinzucheCarStockInsurance> selectByStockId(Integer id) {
        return jipinzucheCarStockInsuranceMapper.selectByStockId(id);
    }
}
