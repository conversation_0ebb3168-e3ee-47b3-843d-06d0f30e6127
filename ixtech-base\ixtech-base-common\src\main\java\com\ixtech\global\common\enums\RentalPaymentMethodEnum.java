package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 租车费用支付方式枚举
 * 对应表: rental_order_charge, 字段: payment_type
 * (1-线下，2-线上)
 */
@Getter
@AllArgsConstructor
public enum RentalPaymentMethodEnum implements DictInf {

    OFFLINE(1, "线下"),
    ONLINE(2, "线上"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static RentalPaymentMethodEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
