<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyunmaven" />
      <option name="name" value="aliyun" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-milestones" />
      <option name="name" value="Spring Milestones" />
      <option name="url" value="https://repo.spring.io/milestone" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-snapshots" />
      <option name="name" value="Spring Snapshots" />
      <option name="url" value="https://repo.spring.io/snapshot" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="huaweicloud" />
      <option name="name" value="huawei" />
      <option name="url" value="https://mirrors.huaweicloud.com/repository/maven/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="rdc-snapshots" />
      <option name="name" value="rdc-snapshots" />
      <option name="url" value="https://packages.aliyun.com/677b967610def31b486d77d9/maven/2513107-snapshot-nrql5x" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="osgeo" />
      <option name="name" value="OSGeo Release Repository" />
      <option name="url" value="https://repo.osgeo.org/repository/release/" />
    </remote-repository>
  </component>
</project>