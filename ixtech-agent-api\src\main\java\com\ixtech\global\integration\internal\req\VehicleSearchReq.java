package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商搜索 req
 *
 * @author: Phili
 * @date： 2025/4/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehicleSearchReq extends VendorSearchReq {

    private static final long serialVersionUID = 1L;

    /**
     * 门店code
     */
    @NotBlank(message = "门店code不能为空")
    private String locationCode;

}
