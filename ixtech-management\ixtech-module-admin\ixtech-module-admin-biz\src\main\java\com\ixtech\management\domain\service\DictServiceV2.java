package com.ixtech.management.domain.service;


import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.integration.internal.req.DictTypeListReq;
import com.ixtech.management.integration.internal.resp.DictTypeListResp;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 门店 Service
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface DictServiceV2 {


    ApiResponse<DictTypeListResp> getDictByTypeList(@RequestBody DictTypeListReq req);

}
