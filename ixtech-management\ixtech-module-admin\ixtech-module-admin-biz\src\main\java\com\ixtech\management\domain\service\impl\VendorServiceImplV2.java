package com.ixtech.management.domain.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.common.enums.BaseEnum;
import com.ixtech.management.common.enums.SettlementCurrencyEnum;
import com.ixtech.management.common.enums.SettlementModeEnum;
import com.ixtech.management.common.exception.BizException;
import com.ixtech.management.common.utils.ContentLengthValidateUtils;
import com.ixtech.management.domain.service.VendorService;
import com.ixtech.management.integration.internal.client.OrdersrvFeignClient;
import com.ixtech.management.integration.internal.client.ProductsrvFeignClient;
import com.ixtech.management.integration.internal.client.VendormanagementsrvClientV2;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.VendorAddResp;
import com.ixtech.management.integration.internal.resp.VendorListInfoResp;
import com.ixtech.management.repo.entity.Vendor;
import com.ixtech.management.repo.model.CountModel;
import com.ixtech.management.repo.model.VendorAreaCountModel;
import com.ixtech.management.repo.repository.StoreRepository;
import com.ixtech.management.repo.repository.VendorRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 供应商service
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:14
 */
@Slf4j
@Service("vendorServiceV2")
public class VendorServiceImplV2 implements VendorService {

    @Resource
    private StoreRepository storeRepository;
    @Resource
    private VendorRepository vendorRepository;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ProductsrvFeignClient productsrvFeignClient;
    @Resource
    private OrdersrvFeignClient ordersrvFeignClient;
    @Resource
    private VendormanagementsrvClientV2 vendormanagementsrvClientV2;

    @Override
    public PageResponse<VendorListInfoResp> list(VendorListQueryReq vendorListQueryReq) {

        int pageNum = vendorListQueryReq.getPageNum(), pageSize = vendorListQueryReq.getPageSize();
        Page<Object> page = PageHelper.startPage(pageNum, pageSize, vendorListQueryReq.isNeedTotalCount());

        // 按条件查询供应商列表
        List<Vendor> vendors = vendorRepository.searchByCondition(vendorListQueryReq.getId(), vendorListQueryReq.getName(),
                vendorListQueryReq.getCompanyFullName());
        List<VendorListInfoResp> list = CollectionUtils.emptyIfNull(vendors).stream()
                .map(this::convert2VendorListInfoResp).toList();

        // 供应商ID列表
        Set<Long> vendorIds = list.stream().map(VendorListInfoResp::getId).collect(Collectors.toSet());

        // 获取供应商覆盖国家、城市数量
        Map<Long, VendorAreaCountModel> areaCountMap = storeRepository.getStoreCoveredAreaCountByVendorIds(vendorIds);
        // 获取供应商门店数量
        Map<Long, CountModel> storeCountMap = storeRepository.getStoreCountByVendorIds(vendorIds);
        // 获取供应商车辆数量
        Map<Long, CountModel> carCountMap = storeRepository.getStoreCarCountByVendorIds(vendorIds);

        // 填充数据
        enrichVendorListInfo(list, areaCountMap, storeCountMap, carCountMap);

        return PageResponse.success(list, page.getTotal(), pageNum);
    }

    /**
     * 将vendor实体对象转换为VendorListInfoResp对象
     *
     * @param vendor
     * @return
     */
    private VendorListInfoResp convert2VendorListInfoResp(Vendor vendor) {
        if (vendor == null) {
            return null;
        }
        VendorListInfoResp vendorListInfo = new VendorListInfoResp();
        BeanUtils.copyProperties(vendor, vendorListInfo);
        // 结算模式
        vendorListInfo.setSettlementModeStr(showSettlementModeStr(vendorListInfo.getSettlementMode()));
        return vendorListInfo;
    }

    /**
     * 将结算模式code转化为名称
     *
     * @param settlementMode
     * @return
     */
    private String showSettlementModeStr(Byte settlementMode) {
        if (settlementMode == null) {
            return null;
        }
        SettlementModeEnum enumSettlementMode = BaseEnum.fromCode(SettlementModeEnum.class, settlementMode);
        return Optional.ofNullable(enumSettlementMode)
                .map(SettlementModeEnum::getName).orElse(null);
    }

    /**
     * 供应商列表数据填充
     *
     * @param vendorList    供应商列表
     * @param areaCountMap  供应商覆盖国家、城市数量
     * @param storeCountMap 供应商门店数量
     * @param carCountMap   供应商车辆数量
     */
    private void enrichVendorListInfo(List<VendorListInfoResp> vendorList,
                                      Map<Long, VendorAreaCountModel> areaCountMap,
                                      Map<Long, CountModel> storeCountMap,
                                      Map<Long, CountModel> carCountMap) {

        if (CollectionUtils.isEmpty(vendorList)) {
            return;
        }

        vendorList.forEach(vendor -> {
            Long vendorId = vendor.getId();

            // 填充区域覆盖数量
            Optional.ofNullable(areaCountMap.get(vendorId)).ifPresentOrElse(
                    areaCount -> {
                        vendor.setCoveredCountryCount(areaCount.getCoveredCountryCount());
                        vendor.setCoveredCityCount(areaCount.getCoveredCityCount());
                    },
                    () -> {
                        vendor.setCoveredCountryCount(0);
                        vendor.setCoveredCityCount(0);
                    }
            );

            // 填充门店数量
            CountModel.setCountFields(
                    storeCountMap.get(vendorId),
                    vendor::setStoreCount,
                    vendor::setActiveStoreCount
            );

            // 填充车辆数量
            CountModel.setCountFields(
                    carCountMap.get(vendorId),
                    vendor::setCarCount,
                    vendor::setActiveCarCount
            );

        });

    }

    @Override
    public VendorAddResp add(VendorAddReq vendorAddReq) {

        // 报价币种、结算币种和结算模式校验
        validateCurrencyAndSettlement(vendorAddReq.getQuoteCurrency(), vendorAddReq.getSettlementCurrency(), vendorAddReq.getSettlementMode());

        // 供应商简称和公司全称校验
        validateContentLength(vendorAddReq.getName(), vendorAddReq.getCompanyFullName(), vendorAddReq.getCode());

        // 负责人校验
        usernameValidate(vendorAddReq.getPrincipal());

        // 手机号校验
        phoneValidate(vendorAddReq.getContactNumber());

        // 电话区号校验
        phoneCodeValidate(vendorAddReq.getContactNumberCode());

        // 插入供应商
        Long id = null;
        try {
            Vendor vendor = new Vendor();
            BeanUtils.copyProperties(vendorAddReq, vendor);
            vendorRepository.insertSelective(vendor);
            id = vendor.getId();
        } catch (Exception e) {
            log.error("新增供应商出错，供应商参数：{}", JSONObject.toJSONString(vendorAddReq), e);
            handleInsertException(e);
        }

        VendorAddResp vendorAddResp = new VendorAddResp();
        vendorAddResp.setId(id);
        return vendorAddResp;
    }

    /**
     * 报价币种、结算币种和结算模式校验
     *
     * @param quoteCurrency      报价币种
     * @param settlementCurrency 结算币种
     * @param settlementMode     结算模式
     */
    private void validateCurrencyAndSettlement(String quoteCurrency, String settlementCurrency, Byte settlementMode) {
        if (quoteCurrency != null && !SettlementCurrencyEnum.USD.getCode().equals(quoteCurrency)) {
            throw new BizException("报价货币当前只支持USD");
        }
        if (settlementCurrency != null && !SettlementCurrencyEnum.USD.getCode().equals(settlementCurrency)) {
            throw new BizException("结算货币当前只支持USD");
        }
        if (settlementMode != null && !SettlementModeEnum.COMMISSION.getCode().equals(settlementMode)) {
            throw new BizException("结算模式当前只支持抽佣模式");
        }
    }

    /**
     * 供应商简称和公司全称校验
     *
     * @param name            供应商简称
     * @param companyFullName 公司全称
     * @param code            供应商代码
     */
    private void validateContentLength(String name, String companyFullName, String code) {
        String validateFailMessage = name != null ? ContentLengthValidateUtils.validate(name, 4, 20) : null;
        if (validateFailMessage != null) {
            throw new BizException("供应商简称校验失败，" + validateFailMessage);
        }
        validateFailMessage = companyFullName != null ? ContentLengthValidateUtils.validate(companyFullName, 4, 128) : null;
        if (validateFailMessage != null) {
            throw new BizException("公司全称校验失败，" + validateFailMessage);
        }
        validateFailMessage = code != null ? ContentLengthValidateUtils.validateSupplierCode(code) : null;
        if (validateFailMessage != null) {
            throw new BizException("供应商编码校验失败，" + validateFailMessage);
        }
    }

    /**
     * 人名校验
     *
     * @param principal
     */
    private void usernameValidate(String principal) {
        String validateFailMessage = principal != null ? ContentLengthValidateUtils.validateUsername(principal) : null;
        if (validateFailMessage != null) {
            throw new BizException("负责人校验失败，" + validateFailMessage);
        }
    }

    /**
     * 手机号校验
     *
     * @param phone
     */
    private void phoneValidate(String phone) {
        String validateFailMessage = phone != null ? ContentLengthValidateUtils.validatePhoneNumber(phone) : null;
        if (validateFailMessage != null) {
            throw new BizException("联系电话校验失败，" + validateFailMessage);
        }
    }

    /**
     * 电话区号校验
     *
     * @param contactNumberCode
     */
    private void phoneCodeValidate(String contactNumberCode) {
        String validateFailMessage = contactNumberCode != null ? ContentLengthValidateUtils.validatePhoneNumberCode(contactNumberCode) : null;
        if (validateFailMessage != null) {
            throw new BizException("电话区号校验失败，" + validateFailMessage);
        }
    }

    /**
     * 处理新增供应商异常
     *
     * @param e
     * @throws BizException
     */
    private void handleInsertException(Exception e) throws BizException {
        String errorMsg = "业务异常";
        // 提取并验证是否为数据库完整性约束违反异常
        Throwable cause = e.getCause();
        if (!(cause instanceof SQLIntegrityConstraintViolationException sqlEx)) {
            throw new BizException(errorMsg);
        }
        // 解析并处理错误信息
        String errMsg = sqlEx.getMessage();
        if (errMsg != null && errMsg.contains("Duplicate entry")) {
            errorMsg = errMsg.contains("company_full_name") ? "已存在相同的公司全称，请修改"
                    : errMsg.contains("name") ? "已存在相同的供应商简称，请修改"
                    : errMsg.contains("code") ? "已存在相同的供应商编码，请修改"
                    : "已存在相同的供应商数据，请修改";
        }
        throw new BizException(errorMsg);
    }

    @Override
    public void update(VendorUpdateReq vendorUpdateReq) {

        // 报价币种、结算币种和结算模式校验
        validateCurrencyAndSettlement(vendorUpdateReq.getQuoteCurrency(), vendorUpdateReq.getSettlementCurrency(), vendorUpdateReq.getSettlementMode());

        // 供应商简称和公司全称校验
        validateContentLength(vendorUpdateReq.getName(), vendorUpdateReq.getCompanyFullName(), vendorUpdateReq.getCode());

        // 负责人校验
        usernameValidate(vendorUpdateReq.getPrincipal());

        // 手机号校验
        phoneValidate(vendorUpdateReq.getContactNumber());

        // 电话区号校验
        phoneCodeValidate(vendorUpdateReq.getContactNumberCode());

        Long id = vendorUpdateReq.getId();
        Vendor oldVendor = vendorRepository.selectById(id);
        if (oldVendor == null) {
            throw new BizException("供应商不存在！");
        }

        try {
            Vendor vendor = new Vendor();
            BeanUtils.copyProperties(vendorUpdateReq, vendor);
            vendorRepository.updateByPrimaryKeySelective(vendor);
        } catch (Exception e) {
            log.error("更新供应商出错，供应商参数：{}", JSONObject.toJSONString(vendorUpdateReq), e);
            handleUpdateException(e);
        }

        // 清除缓存
        clearCache(id);

    }

    /**
     * 处理更新供应商异常
     *
     * @param e
     * @throws BizException
     */
    private void handleUpdateException(Exception e) throws BizException {
        handleInsertException(e);
    }

    @Override
    public void setActive(VendorSetActiveReq vendorSetActiveReq) {

        Long id = vendorSetActiveReq.getId();
        Vendor oldVendor = vendorRepository.selectById(id);
        if (oldVendor == null) {
            throw new BizException("供应商不存在！");
        }

        Vendor vendor = new Vendor();
        vendor.setId(vendorSetActiveReq.getId());
        vendor.setActive(vendorSetActiveReq.getActive());
        vendorRepository.updateByPrimaryKeySelective(vendor);

        // 清除缓存
        clearCache(id);

    }

    /**
     * 删除供应商信息缓存
     *
     * @param id 供应商id
     */
    private void clearCache(Long id) {
        VendorClearCacheReq req = new VendorClearCacheReq();
        req.setVendorId(id);
        try {
            // 删除商品服务中的供应商缓存
            productsrvFeignClient.clearVendorCache(req);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        try {
            // 删除订单服务中的供应商缓存
            ordersrvFeignClient.clearVendorCache(req);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public List<SelectOptionResponse<Long>> dropdownList(VendorDropdownReq vendorDropdownReq) {
        List<Vendor> list = vendorRepository.searchByCondition(null, vendorDropdownReq.getName(), null);
        return CollectionUtils.emptyIfNull(list).stream()
                .map(v -> new SelectOptionResponse<>(v.getId(), v.getName())).toList();
    }

    @Override
    public ApiResponse<com.ixtech.merchant.resp.IxVendorResp> getVendorById(MerchantVendorQueryReq req) {
        return vendormanagementsrvClientV2.getVendorByVendorId(req);
    }

}
