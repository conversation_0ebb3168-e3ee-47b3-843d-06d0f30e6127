package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 接送机附加服务类型
 *
 * <AUTHOR> hu
 * @date 2025/7/24 16:57
 */
@Getter
public enum TransferAmenityServiceTypeEnum {

    PICK_UP_CARD(1, "举牌接机"),
    DRIVER_LANGUAGE(2, "司机语言"),
    CHILD_SEAT(3, "儿童座椅"),
    ;

    TransferAmenityServiceTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 附加服务类型code
     */
    private final Integer code;

    /**
     * 附加服务类型描述
     */
    private final String value;

}
