<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.IxCarOrderCustomerMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.IxCarOrderCustomer">
            <id property="id" column="id" />
            <result property="active" column="active" />
            <result property="deleted" column="deleted" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="carOrderId" column="car_order_id" />
            <result property="driverAge" column="driver_age" />
            <result property="citizenCountryCode" column="citizen_country_code" />
            <result property="emailAddress" column="email_address" />
            <result property="givenName" column="given_name" />
            <result property="surname" column="surname" />
            <result property="mobileAreaCityCode" column="mobile_area_city_code" />
            <result property="mobilePhoneNumber" column="mobile_phone_number" />
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,car_order_id,
        driver_age,citizen_country_code,email_address,given_name,surname,
        mobile_area_city_code,mobile_phone_number
    </sql>
    <insert id="insert" parameterType="com.ixtech.management.repo.entity.IxCarOrderCustomer">
        INSERT INTO ix_car_order_customer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carOrderId != null">car_order_id,</if>
            <if test="driverAge != null">driver_age,</if>
            <if test="citizenCountryCode != null">citizen_country_code,</if>
            <if test="emailAddress != null">email_address,</if>
            <if test="givenName != null">given_name,</if>
            <if test="surname != null">surname,</if>
            <if test="mobileAreaCityCode != null">mobile_area_city_code,</if>
            <if test="mobilePhoneNumber != null">mobile_phone_number,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="carOrderId != null">#{carOrderId},</if>
            <if test="driverAge != null">#{driverAge},</if>
            <if test="citizenCountryCode != null">#{citizenCountryCode},</if>
            <if test="emailAddress != null">#{emailAddress},</if>
            <if test="givenName != null">#{givenName},</if>
            <if test="surname != null">#{surname},</if>
            <if test="mobileAreaCityCode != null">#{mobileAreaCityCode},</if>
            <if test="mobilePhoneNumber != null">#{mobilePhoneNumber},</if>
        </trim>
    </insert>
</mapper>
