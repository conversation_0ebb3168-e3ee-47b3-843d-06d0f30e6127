package com.ixtech.management.facade.api;


import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.domain.service.VendorService;
import com.ixtech.management.integration.internal.req.MerchantRentalOrderDetailReq;
import com.ixtech.management.integration.internal.req.MerchantVendorQueryReq;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 供应商接口
 */
@RestController
@RequestMapping("/v2/managementsrv/api/vendor")
public class IxVendorControllerV2 {

    @Resource(name = "vendorServiceV2")
    private VendorService vendorServiceV2;

    @PostMapping("/get")
    public ApiResponse<com.ixtech.merchant.resp.IxVendorResp> getVendorById(@RequestBody @Valid MerchantVendorQueryReq req) {
        return vendorServiceV2.getVendorById(req);
    }


}
