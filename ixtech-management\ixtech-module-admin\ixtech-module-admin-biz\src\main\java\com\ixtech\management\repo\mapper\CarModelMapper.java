package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.CarModelPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 19:09
 */
@DS("ix")
@Mapper
public interface CarModelMapper {

    int insert(CarModelPO record);

    int insertSelective(CarModelPO record);

    CarModelPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CarModelPO record);

    int updateByPrimaryKey(CarModelPO record);

    int updateBatch(List<CarModelPO> list);

    int updateBatchSelective(List<CarModelPO> list);

    int batchInsert(@Param("list") List<CarModelPO> list);

    /**
     * 查询指定门店的车型
     *
     * @param storeId 门店id
     * @return
     */
    List<CarModelPO> getCarModelsByStoreId(@Param(value = "storeId") Long storeId);

}