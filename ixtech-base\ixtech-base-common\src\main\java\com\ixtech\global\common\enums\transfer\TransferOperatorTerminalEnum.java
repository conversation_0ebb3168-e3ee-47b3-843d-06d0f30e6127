package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 操作端枚举
 *
 * <AUTHOR> hu
 * @date 2025/7/28 21:05
 */
@Getter
public enum TransferOperatorTerminalEnum {

    MERCHANT(1, "商户端"),
    MANAGEMENT(2, "平台端"),
    ;

    TransferOperatorTerminalEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 单位编码
     */
    private final Integer code;

    /**
     * 单位描述
     */
    private final String value;

}
