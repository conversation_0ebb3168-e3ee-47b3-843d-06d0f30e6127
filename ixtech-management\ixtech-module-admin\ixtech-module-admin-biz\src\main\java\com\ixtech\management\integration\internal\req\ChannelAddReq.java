package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.ixtech.management.common.annotation.InEnum;
import com.ixtech.management.common.enums.SettlementCurrencyEnum;
import com.ixtech.management.common.enums.SettlementModeEnum;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 渠道新增请求Req
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:32
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChannelAddReq {

    /**
     * 渠道简称
     */
    @NotBlank(message = "渠道简称不能为空")
    private String channelName;

    /**
     * 公司全称
     */
    @NotBlank(message = "公司全称不能为空")
    private String companyFullName;

    /**
     * 报价货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    @NotBlank(message = "报价货币不能为空")
    @InEnum(enumClass = SettlementCurrencyEnum.class, message = "不支持的报价货币")
    private String quoteCurrency;

    /**
     * 结算货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    @NotBlank(message = "结算货币不能为空")
    @InEnum(enumClass = SettlementCurrencyEnum.class, message = "不支持的结算货币")
    private String settlementCurrency;

    /**
     * 结算模式 1：底价模式 2：抽佣模式
     */
    @NotNull(message = "结算模式不能为空")
    @InEnum(enumClass = SettlementModeEnum.class, message = "不支持的结算模式")
    private Byte settlementMode;

    /**
     * 比例x%
     */
    @Digits(integer = 2, fraction = 1, message = "比例设置错误，请设置（0，100）范围内的数值，最多1位小数")
    @DecimalMin(value = "0", inclusive = false, message = "比例设置错误，请设置（0，100）范围内的数值，最多1位小数")
    @DecimalMax(value = "100", inclusive = false, message = "比例设置错误，请设置（0，100）范围内的数值，最多1位小数")
    @NotNull(message = "比例不能为空")
    private BigDecimal rate;

}
