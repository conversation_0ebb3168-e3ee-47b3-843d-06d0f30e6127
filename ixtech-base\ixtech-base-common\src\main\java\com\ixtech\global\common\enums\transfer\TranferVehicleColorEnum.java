package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum TranferVehicleColorEnum {

    WHITE("WHITE", "白色"),
    BLACK("<PERSON><PERSON><PERSON><PERSON>", "黑色"),
    SILVER("SILVER", "银色"),
    GREY("GREY", "灰色"),
    RED("RED", "红色"),
    GREEN("GREEN", "绿色"),
    YELLOW("YELLOW", "黄色"),
    BLUE("BLUE", "蓝色");

    private String code;
    private String value;

    TranferVehicleColorEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    // 多渠道映射配置（渠道代码 → (渠道颜色代码 → IX枚举)）
    private static final Map<String, Map<String, String>> CHANNEL_MAPPINGS = new HashMap<>();
    // IX枚举到渠道代码的反向映射（渠道代码 → (IX枚举 → 渠道颜色代码)）
    private static final Map<String, Map<String, String>> REVERSE_MAPPINGS = new HashMap<>();

    public static String getValueByCode(String code) {
        for (TranferVehicleColorEnum value : values()) {
            if (value.code.equals(code)) {
                return value.value;
            }
        }
        return "未知";
    }
    static {
        // klook颜色与IX颜色的映射
        Map<String, String> klookColorMapping = new HashMap<>();
        klookColorMapping.put("WHITE", TranferVehicleColorEnum.WHITE.code);
        klookColorMapping.put("BLACK", TranferVehicleColorEnum.BLACK.code);
        klookColorMapping.put("SILVER", TranferVehicleColorEnum.SILVER.code);
        klookColorMapping.put("GREY", TranferVehicleColorEnum.GREY.code);
        klookColorMapping.put("RED", TranferVehicleColorEnum.RED.code);
        klookColorMapping.put("GREEN", TranferVehicleColorEnum.GREEN.code);
        klookColorMapping.put("YELLOW", TranferVehicleColorEnum.YELLOW.code);
        klookColorMapping.put("BLUE", TranferVehicleColorEnum.BLUE.code);
        CHANNEL_MAPPINGS.put(TransferSourceCodeEnum.KLOOK.getValue(), klookColorMapping);

        // klook反向映射
        Map<String, String> reverseMap = new HashMap<>();
        klookColorMapping.forEach((channelColor, ixColor) -> reverseMap.put(ixColor, channelColor));
        REVERSE_MAPPINGS.put(TransferSourceCodeEnum.KLOOK.getValue(), reverseMap);
    }
}
