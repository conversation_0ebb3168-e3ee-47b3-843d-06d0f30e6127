package com.ixtech.global.common.biz.rental.utils;

import com.ixtech.global.common.biz.rental.bo.ReferenceIdBO;
import com.ixtech.global.common.biz.rental.constant.NormalConstant;
import com.ixtech.global.common.utils.ObfuscationUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ReferenceIdUtil {
    public static ReferenceIdBO parseReferenceIdV2(String referenceId) {
        // 步骤 1: 对输入的 referenceId 进行解混淆
        String str = ObfuscationUtils.deobfuscate(referenceId, NormalConstant.HASH_SALT);
        // 步骤 2: 使用 NormalConstant.REGEX_ONE 分隔符将解混淆后的字符串分割成多个部分
        String[] parts = str.split(NormalConstant.REGEX_ONE);
        // 步骤 3: 创建一个新的 ReferenceIdBO 对象
        ReferenceIdBO ref = new ReferenceIdBO();
        // 步骤 4: 将分割后的字符串数组中的各个部分按顺序赋值给 ReferenceIdBO 对象的相应属性
        // 确保数组长度足够，避免IndexOutOfBoundsException
        if (parts.length >= 6) {
            // 供应商code
            ref.setVendorCode(parts[0]);
            // 门店code
            ref.setStoreCode(parts[1]);
            // sipp四字码 (String)
            ref.setSippCode(parts[2]);
            // 车型id (Long)
            ref.setModelId(Long.valueOf(parts[3]));
            // 保险code
            ref.setInsuranceCode(parts[4]);
            // 是否立即确认
            ref.setInstantConfirm(Boolean.parseBoolean(parts[5]));
        } else {
            log.warn("Invalid referenceId format: {}", referenceId);
            return null;
        }
        // 步骤 5: 返回填充好的 ReferenceIdBO 对象
        return ref;
    }

    public static void main(String[] args) {
        System.out.printf("ref:" + parseReferenceIdV2("9DbifWYVg4XZEHqTSC/7d0b/2fltP04v77wlVKdHtYlQGBp4FITIYQ=="));
    }

}
