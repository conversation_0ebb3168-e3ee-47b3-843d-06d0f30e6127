package com.ixtech.global.common.enums.transfer;

public enum TransferVehicleOwnerEnum {

    OWNED(1, "自有"),
    COOPERATIVE_FLEET(2, "合作车队");

    private final Integer code;
    private final String description;

    TransferVehicleOwnerEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取枚举值
    public static TransferVehicleOwnerEnum getByCode(Integer code) {
        for (TransferVehicleOwnerEnum ownership : values()) {
            if (ownership.code == code) {
                return ownership;
            }
        }
        return null;
    }
}
