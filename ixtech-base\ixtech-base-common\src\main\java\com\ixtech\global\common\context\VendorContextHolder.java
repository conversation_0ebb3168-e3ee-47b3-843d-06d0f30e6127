package com.ixtech.global.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.ixtech.global.common.dto.VendorInfo;

/**
 * 多租户上下文 Holder
 */
public class VendorContextHolder {

    public static final String VENDOR_ID_HEADER = "Vendor-Id";
    public static final String USER_ID_HEADER = "User-Id";

    /**
     * 当前供应商编号
     */
    private static final ThreadLocal<Long> VENDOR_ID = new TransmittableThreadLocal<>();

    /**
     * 当前用户编号
     */
    private static final ThreadLocal<Long> USER_ID = new TransmittableThreadLocal<>();

    /**
     * 当前供应商信息
     */
    private static final ThreadLocal<VendorInfo> VENDOR_INFO = new TransmittableThreadLocal<>();

    /**
     * 获得供应商编号
     *
     * @return 供应商编号
     */
    public static Long getVendorId() {
        return VENDOR_ID.get();
    }

    /**
     * 获得供应商编号。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 供应商编号
     */
    public static Long getRequiredVendorId() {
        Long vendorId = getVendorId();
        if (vendorId == null) {
            throw new NullPointerException("TenantContextHolder 不存在供应商编号！");
        }
        return vendorId;
    }

    /**
     * 设置供应商编号
     *
     * @param vendorId 供应商编号
     */
    public static void setVendorId(Long vendorId) {
        VENDOR_ID.set(vendorId);
    }

    /**
     * 获得用户编号
     *
     * @return 用户编号
     */
    public static Long getUserId() {
        return USER_ID.get();
    }

    /**
     * 获得用户编号。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 用户编号
     */
    public static Long getRequiredUserId() {
        Long userId = getUserId();
        if (userId == null) {
            throw new NullPointerException("TenantContextHolder 不存在用户编号！");
        }
        return userId;
    }

    /**
     * 设置用户编号
     *
     * @param userId 用户编号
     */
    public static void setUserId(Long userId) {
        USER_ID.set(userId);
    }

    /**
     * 获得供应商信息
     *
     * @return 供应商信息
     */
    public static VendorInfo getVendorInfo() {
        return VENDOR_INFO.get();
    }

    /**
     * 获得供应商信息。如果不存在，则抛出 NullPointerException 异常
     *
     * @return 供应商信息
     */
    public static VendorInfo getRequiredVendorInfo() {
        VendorInfo vendorInfo = getVendorInfo();
        if (vendorInfo == null) {
            throw new NullPointerException("VendorInfoContextHolder 不存在供应商信息！");
        }
        return vendorInfo;
    }

    /**
     * 设置供应商信息
     *
     * @param vendorInfo 供应商信息
     */
    public static void setVendorInfo(VendorInfo vendorInfo) {
        VENDOR_INFO.set(vendorInfo);
    }

    /**
     * 清除上下文
     */
    public static void clear() {
        VENDOR_ID.remove();
        USER_ID.remove();
        VENDOR_INFO.remove();
    }
}
