package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.CarBrand;
import com.ixtech.management.repo.mapper.CarBrandMapper;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:41
 */
@Repository
public class CarBrandRepository {

    @Resource
    private CarBrandMapper carBrandMapper;

    /**
     * 批量查询品牌信息
     *
     * @param brandIds 品牌id列表
     * @return
     */
    public Map<Long, CarBrand> selectByIds(Collection<Long> brandIds) {
        if (CollectionUtils.isEmpty(brandIds)) {
            return new HashMap<>();
        }
        List<CarBrand> brands = carBrandMapper.selectByPrimaryKeyList(brandIds);
        return CollectionUtils.emptyIfNull(brands).stream()
                .collect(Collectors.toMap(CarBrand::getId, Function.identity()));
    }

}
