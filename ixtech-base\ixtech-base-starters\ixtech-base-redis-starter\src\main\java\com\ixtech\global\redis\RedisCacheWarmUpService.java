package com.ixtech.global.redis;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RCountDownLatch;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Service
@Slf4j
@RequiredArgsConstructor
public class RedisCacheWarmUpService {

    private final RedissonClient redissonClient;

    private static final String LOCK_KEY_PREFIX = "warmup:lock:";
    private static final String COUNTDOWN_LATCH_KEY_PREFIX = "warmup:latch:";

    // 默认的等待和租约时间
    // followerWaitTime 应该比 leaderLeaseTime 稍长
    private static final long DEFAULT_FOLLOWER_WAIT_TIME_SECONDS = 10L;
    // 使用 RLock 时，如果业务执行时间不确定，可以将 leaseTime 设为-1，以启用看门狗机制
    private static final long DEFAULT_LEADER_LEASE_TIME_SECONDS = -1L; // -1 启用看门狗

    /**
     * 执行一个可能会引发缓存击穿的操作（使用默认超时和看门狗机制）。
     * 领导者获取锁后，如果 leaseTime 为 -1，看门狗会自动续期。
     * 追随者等待时间默认为10秒。
     *
     * @param uniqueKey    用于标识同一个预热任务的唯一key
     * @param dataSupplier 一个提供者函数，封装了从数据库查询并回写缓存的逻辑
     */
    public void execute(String uniqueKey, Supplier<?> dataSupplier) {
        // 在这个重载方法中，我们将waitTime设为0，表示“尝试一次，不行就换路”，
        // leaseTime设为-1以启用看门狗。
        execute(uniqueKey,
                DEFAULT_FOLLOWER_WAIT_TIME_SECONDS, // 追随者等待时间
                0L,                                 // 领导者尝试获取锁的等待时间，0表示立即返回
                DEFAULT_LEADER_LEASE_TIME_SECONDS,  // 领导者租约时间，-1表示启用看门狗
                TimeUnit.SECONDS,
                dataSupplier);
    }

    /**
     * 执行一个可能会引发缓存击穿的操作（使用 RLock 优化）。
     * 此方法通过 RLock 和 RCountDownLatch，确保提供的 `dataSupplier` 只被执行一次。
     *
     * @param uniqueKey        用于标识同一个预热任务的唯一key（例如，可以是缓存的key）
     * @param followerWaitTime 追随者线程等待领导者完成任务的最大时间
     * @param leaderWaitTime   领导者线程尝试获取锁的最大等待时间。设为0表示不等待，立即尝试。
     * @param leaderLeaseTime  锁的租约时间。如果设为-1，Redisson的看门狗机制将被激活，自动续期。
     * @param unit             时间单位
     * @param dataSupplier     一个提供者函数，封装了从数据库查询并回写缓存的逻辑。只有领导者线程会执行它。
     */
    public void execute(String uniqueKey, long followerWaitTime, long leaderWaitTime, long leaderLeaseTime, TimeUnit unit, Supplier<?> dataSupplier) {
        String lockKey = LOCK_KEY_PREFIX + uniqueKey;
        String latchKey = COUNTDOWN_LATCH_KEY_PREFIX + uniqueKey;

        RLock lock = redissonClient.getLock(lockKey);
        boolean isLeader = false;

        try {
            // 1. 领导者选举：使用 RLock.tryLock 替代 RBucket.trySet
            isLeader = lock.tryLock(leaderWaitTime, leaderLeaseTime, unit);

            if (isLeader) {
                // 2a. 我是领导者：负责执行任务并通知其他人
                log.info("线程 [{}] 成为key [{}] 的领导者。", Thread.currentThread().getName(), uniqueKey);
                RCountDownLatch latch = redissonClient.getCountDownLatch(latchKey);
                latch.trySetCount(1); // 初始化计数器为1

                try {
                    // 执行核心业务：查询数据库，回写缓存
                    dataSupplier.get();
                    log.info("key [{}] 的领导者已完成任务。", uniqueKey);
                } catch (Exception e) {
                    log.error("key [{}] 的领导者执行任务失败。", uniqueKey, e);
                    // 即使失败，也要通知其他线程，避免它们一直等待直到超时
                    throw e; // 重新抛出异常，让上层业务感知
                } finally {
                    // 任务完成（无论成功或失败），发出信号
                    latch.countDown();
                }
            } else {
                // 2b. 我是追随者：等待领导者完成任务
                log.info("线程 [{}] 成为key [{}] 的追随者，开始等待信号。", Thread.currentThread().getName(), uniqueKey);
                RCountDownLatch latch = redissonClient.getCountDownLatch(latchKey);
                // 等待信号，或者直到超时
                boolean success = latch.await(followerWaitTime, unit);
                if (success) {
                    log.info("key [{}] 的追随者已接收到信号。", uniqueKey);
                } else {
                    log.warn("key [{}] 的追随者等待领导者超时。", uniqueKey);
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("线程在获取锁或等待时被中断, key: {}", uniqueKey, e);
        } finally {
            // 3. 释放锁：只有成功获取锁的领导者线程才能且必须释放锁
            if (isLeader && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
