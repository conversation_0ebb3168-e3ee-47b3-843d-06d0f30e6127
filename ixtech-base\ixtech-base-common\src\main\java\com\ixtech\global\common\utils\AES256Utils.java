package com.ixtech.global.common.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES加密解密工具类
 *
 * @author: JP
 * @date： 2025/3/20 10:14
 */
public class AES256Utils {

    /*
     * 加密算法
     */
    private static final String ALGORITHM = "AES";
    /*
     * 加密解密算法/加密模式/填充方式
     */
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";

    static {
        java.security.Security.setProperty("crypto.policy", "unlimited");
    }



    /**
     * 加密字符串
     *
     * @param plainText 需要加密的字符串
     * @return 加密后的Base64编码字符串
     */
    public static String encrypt(String secretKey, String iv, String plainText) {
        try {
            // 创建密钥和IV
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));

            // 初始化Cipher
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);

            // 加密
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 返回Base64编码的加密结果
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }

    /**
     * 解密字符串
     *
     * @param encryptedText 加密后的Base64编码字符串
     * @return 解密后的原始字符串
     */
    public static String decrypt(String secretKey, String iv, String encryptedText) {
        try {
            // 创建密钥和IV
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));

            // 初始化Cipher
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

            // 解码Base64并解密
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

            // 返回解密后的字符串
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }
}
