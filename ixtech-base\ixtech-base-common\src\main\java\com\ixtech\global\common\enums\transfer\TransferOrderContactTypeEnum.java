package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 联系方式类型枚举
 *
 * <AUTHOR> hu
 * @date 2025/7/22 10:15
 */
@Getter
public enum TransferOrderContactTypeEnum {

    EMAIL(1, "Email"),
    WHATSAPP(2, "WhatsApp"),
    WECHAT(3, "WeChat"),
    LINE(4, "LINE"),
    KAKAO_TALK(5, "Kakao Talk"),
    SKYPE(6, "Skype"),
    ;

    TransferOrderContactTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 联系方式类型code
     */
    private final Integer code;

    /**
     * 联系方式类型描述
     */
    private final String value;


    /**
     * 根据code获取对应的value
     * @param code 枚举编码
     * @return 对应的value，若未找到则返回null
     */
    public static String getValueByCode(Integer code) {
        // 遍历所有枚举值，匹配code
        for (TransferOrderContactTypeEnum typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum.value;
            }
        }
        // 未找到匹配的code时返回null
        return null;
    }
}
