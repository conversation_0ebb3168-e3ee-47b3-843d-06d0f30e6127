package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 平台订单状态枚举
 * 对应表: platform_order, 字段: status
 * (-1-已取消，0-待确认，1-已确认，2-已完成)
 */
@Getter
@AllArgsConstructor
public enum PlatformOrderStatusEnum implements DictInf {

    CANCELLED(-1, "已取消"),
    PENDING_CONFIRMATION(0, "待确认"),
    CONFIRMED(1, "已确认"),
    COMPLETED(2, "已完成"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static PlatformOrderStatusEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
