package com.ixtech.global.common.enums.inf;

/**
 * 通用字典接口
 */
public interface DictInf {

    /**
     * value
     *
     * @return
     */
    String getValue();

    /**
     * label
     *
     * @return
     */
    String getLabel();

    /**
     * 通用的fromCode方法，子枚举通过此方法查找枚举值
     */
    static <T extends DictInf> T fromCode(T[] enumConstants, Object code) {
        if (code == null) {
            throw new IllegalArgumentException("枚举值不能为空");
        }
        if (enumConstants == null) {
            throw new IllegalArgumentException("枚举不能为空");
        }
        String codeStr = code.toString();
        for (T enumConstant : enumConstants) {
            if (enumConstant.getValue().equals(codeStr)) {
                return enumConstant;
            }
        }
        throw new IllegalArgumentException("无效的枚举值: " + code);
    }
}
