package com.ixtech.global.common.enums.transfer;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.Getter;

/**
 * 接送机渠道类型
 *
 * <AUTHOR> hu
 * @date 2025/7/20 18:36
 */
@Getter
public enum TransferSourceCodeEnum implements DictInf {

    KLOOK("klook", "客路"),
    BOOKING("booking", "BOOKING"),
    HEYCARS("heycars", "悦行"),
    CTRIP("ctrip", "携程"),
    COMMON("common", "自有渠道"),
    ;

    TransferSourceCodeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 渠道code
     */
    private final String value;

    /**
     * 渠道名称
     */
    private final String label;

    public static TransferSourceCodeEnum findByValue(String value) {
        for (TransferSourceCodeEnum transferSourceCodeEnum : TransferSourceCodeEnum.values()) {
            if (transferSourceCodeEnum.getValue().equals(value)) {
                return transferSourceCodeEnum;
            }
        }
        return null;
    }
}
