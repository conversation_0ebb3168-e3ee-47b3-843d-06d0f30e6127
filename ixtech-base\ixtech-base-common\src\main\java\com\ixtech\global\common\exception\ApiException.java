package com.ixtech.global.common.exception;

/**
 * @description: Api异常
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/6/5
 */

public class ApiException extends RuntimeException {
	/**
	 *
	 */
	private static final long serialVersionUID = -693163817014879162L;

	private String errorCode;
	private String errorMsg;

	public String getErrorCode() {
		return errorCode;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	/**
	 * @param errMsg
	 */
	private void setErrorMsg(String errMsg) {
		this.errorMsg = errMsg;
	}

	public ApiException(String errorCode, String errorMsg) {
		this.errorCode = errorCode;
		this.errorMsg = errorMsg;
	}
}
