package com.ixtech.global;

import feign.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @description: feign请求日志级别
 * @author: JP
 * @date： 2025/3/18
 */
@Configuration
public class DefaultFeignConfig {

 public DefaultFeignConfig() {
 }

 @Bean
 public Logger.Level multipartLoggerLevel() {
  return Logger.Level.FULL;
 }
}
