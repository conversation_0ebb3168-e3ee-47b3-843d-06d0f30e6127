package com.ixtech.management.domain.service.impl;

import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.CarOrderPayTypeService;
import com.ixtech.management.integration.internal.resp.ModelSelectOptionResponse;
import com.ixtech.management.repo.entity.JipinzucheCarList;
import com.ixtech.management.repo.entity.JipinzucheCarModel;
import com.ixtech.management.repo.entity.JipinzucheCarOrderPaytype;
import com.ixtech.management.repo.entity.JipinzucheCarStock;
import com.ixtech.management.repo.repository.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 供应商service
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:14
 */
@Slf4j
@Service
public class CarOrderPayTypeServiceImpl implements CarOrderPayTypeService {

    @Resource
    private JipinzucheCarOrderPaytypeRepository jipinzucheCarOrderPaytypeRepository;

    @Override
    public List<SelectOptionResponse<Long>> dropdownList() {
        List<JipinzucheCarOrderPaytype> list = jipinzucheCarOrderPaytypeRepository.selectAll();
        return CollectionUtils.emptyIfNull(list).stream()
                .map(v -> new SelectOptionResponse<>((long)v.getId(), v.getTitle())).toList();
    }

}
