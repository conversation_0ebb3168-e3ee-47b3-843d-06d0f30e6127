package com.ixtech.management.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 下拉列表选项通用返回Response
 *
 * <AUTHOR> hu
 * @date 2025/4/8 11:33
 */
@Data
@NoArgsConstructor
public class SelectOptionResponse<T> {

    /**
     * 显示文本
     */
    private String label;

    /**
     * 实际传递的值
     */
    private T value;

    /**
     * 是否禁用该选项 true：禁用 false：可用
     */
    private Boolean disabled = false;

    /**
     * 分组标识（用于分组展示选项）
     */
    private String group;

    /**
     * 扩展字段（图标/样式等）
     */
    private Map<String, Object> extra;

    public SelectOptionResponse(T value, String label) {
        this.value = value;
        this.label = label;
    }

    public SelectOptionResponse(T value, String label, String group) {
        this.value = value;
        this.label = label;
        this.group = group;
    }

}
