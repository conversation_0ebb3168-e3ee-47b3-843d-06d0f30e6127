package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.IxCarOrderPrice;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【ix_car_order_price(订单-金额信息表)】的数据库操作Mapper
* @createDate 2025-04-29 18:03:25
* @Entity com.ixtech.management.repo.entity.IxCarOrderPrice
*/
@DS("ix")
@Mapper
public interface IxCarOrderPriceMapper {

    IxCarOrderPrice selectByCarOrderId(Long carOrderId);

    Long insertOne(IxCarOrderPrice ixCarOrderPrice);
}




