<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.StoreMapper">
    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.Store">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="active" jdbcType="BIT" property="active"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="countryid" jdbcType="INTEGER" property="countryid"/>
        <result column="provinceid" jdbcType="INTEGER" property="provinceid"/>
        <result column="cityid" jdbcType="INTEGER" property="cityid"/>
        <result column="countyid" jdbcType="INTEGER" property="countyid"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="lat" jdbcType="REAL" property="lat"/>
        <result column="lon" jdbcType="REAL" property="lon"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="litpic" jdbcType="VARCHAR" property="litpic"/>
        <result column="map_img_small" jdbcType="VARCHAR" property="mapImgSmall"/>
        <result column="map_img_large" jdbcType="VARCHAR" property="mapImgLarge"/>
        <result column="intro" jdbcType="VARCHAR" property="intro"/>
        <result column="principal" jdbcType="VARCHAR" property="principal"/>
        <result column="principal_mobile" jdbcType="VARCHAR" property="principalMobile"/>
        <result column="mobile_area_code" jdbcType="VARCHAR" property="mobileAreaCode"/>
        <result column="currency_unit" jdbcType="VARCHAR" property="currencyUnit"/>
        <result column="day_mileage" jdbcType="REAL" property="dayMileage"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="opentime" jdbcType="VARCHAR" property="opentime"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="iata" jdbcType="VARCHAR" property="iata"/>
        <result column="postal_code" jdbcType="VARCHAR" property="postalCode"/>
        <result column="how_to_get" jdbcType="VARCHAR" property="howToGet"/>
        <result column="how_to_go" jdbcType="VARCHAR" property="howToGo"/>
        <result column="exchange_rate" jdbcType="REAL" property="exchangeRate"/>
        <result column="min_rent_days" jdbcType="INTEGER" property="minRentDays"/>
        <result column="ordercode_pre" jdbcType="VARCHAR" property="ordercodePre"/>
        <result column="time" jdbcType="INTEGER" property="time"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="mid" jdbcType="INTEGER" property="mid"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="every_price" jdbcType="REAL" property="everyPrice"/>
        <result column="monetary_unit" jdbcType="VARCHAR" property="monetaryUnit"/>
        <result column="kilometers" jdbcType="VARCHAR" property="kilometers"/>
        <result column="enname" jdbcType="VARCHAR" property="enname"/>
        <result column="enaddress" jdbcType="VARCHAR" property="enaddress"/>
        <result column="weekdayevening" jdbcType="VARCHAR" property="weekdayevening"/>
        <result column="weekdaymoring" jdbcType="VARCHAR" property="weekdaymoring"/>
        <result column="feescale" jdbcType="REAL" property="feescale"/>
        <result column="weekendevening" jdbcType="VARCHAR" property="weekendevening"/>
        <result column="weekendmoring" jdbcType="VARCHAR" property="weekendmoring"/>
        <result column="standardcharge" jdbcType="VARCHAR" property="standardcharge"/>
        <result column="displaystatus" jdbcType="INTEGER" property="displaystatus"/>
        <result column="childseat" jdbcType="REAL" property="childseat"/>
        <result column="time_delay" jdbcType="REAL" property="timeDelay"/>
        <result column="card_type" jdbcType="VARCHAR" property="cardType"/>
        <result column="is_vcc" jdbcType="INTEGER" property="isVcc"/>
        <result column="quota_interval" jdbcType="VARCHAR" property="quotaInterval"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="order_terms" jdbcType="LONGVARCHAR" property="orderTerms"/>
        <result column="order_precautions" jdbcType="LONGVARCHAR" property="orderPrecautions"/>
        <result column="confirm_now" jdbcType="VARCHAR" property="confirmNow"/>
        <result column="min_book_hour" jdbcType="INTEGER" property="minBookHour"/>
        <result column="localcurrency_unit" jdbcType="VARCHAR" property="localcurrencyUnit"/>
        <result column="arrival_way" jdbcType="INTEGER" property="arrivalWay"/>
        <result column="vendor_id" jdbcType="BIGINT" property="vendorId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, active, deleted, create_time, update_time, countryid, provinceid, cityid, countyid,
        address, lat, lon, `name`, litpic, map_img_small, map_img_large, intro, principal,
        principal_mobile, mobile_area_code, currency_unit, day_mileage, code, opentime, `type`,
        iata, postal_code, how_to_get, how_to_go, exchange_rate, min_rent_days, ordercode_pre,
        `time`, ip, mid, `status`, every_price, monetary_unit, kilometers, enname, enaddress,
        weekdayevening, weekdaymoring, feescale, weekendevening, weekendmoring, standardcharge,
        displaystatus, childseat, time_delay, card_type, is_vcc, quota_interval, url, order_terms,
        order_precautions, confirm_now, min_book_hour, localcurrency_unit, arrival_way, vendor_id
    </sql>
    <sql id="Ignore_Deleted">
        status != -1
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from jipinzuche_store
        where id = #{id,jdbcType=INTEGER,javaType=long} AND
        <include refid="Ignore_Deleted"/>
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ixtech.management.repo.entity.Store"
            useGeneratedKeys="true">
        insert into jipinzuche_store (active, deleted, create_time,
        update_time, countryid, provinceid,
        cityid, countyid, address,
        lat, lon, `name`, litpic,
        map_img_small, map_img_large, intro,
        principal, principal_mobile, mobile_area_code,
        currency_unit, day_mileage, code,
        opentime, `type`, iata,
        postal_code, how_to_get, how_to_go,
        exchange_rate, min_rent_days, ordercode_pre,
        `time`, ip, mid, `status`,
        every_price, monetary_unit, kilometers,
        enname, enaddress, weekdayevening,
        weekdaymoring, feescale, weekendevening,
        weekendmoring, standardcharge, displaystatus,
        childseat, time_delay, card_type,
        is_vcc, quota_interval, url,
        order_terms, order_precautions, confirm_now,
        min_book_hour, localcurrency_unit, arrival_way,
        vendor_id)
        values (#{active,jdbcType=BIT}, #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{countryid,jdbcType=INTEGER}, #{provinceid,jdbcType=INTEGER},
        #{cityid,jdbcType=INTEGER}, #{countyid,jdbcType=INTEGER}, #{address,jdbcType=VARCHAR},
        #{lat,jdbcType=REAL}, #{lon,jdbcType=REAL}, #{name,jdbcType=VARCHAR}, #{litpic,jdbcType=VARCHAR},
        #{mapImgSmall,jdbcType=VARCHAR}, #{mapImgLarge,jdbcType=VARCHAR}, #{intro,jdbcType=VARCHAR},
        #{principal,jdbcType=VARCHAR}, #{principalMobile,jdbcType=VARCHAR}, #{mobileAreaCode,jdbcType=VARCHAR},
        #{currencyUnit,jdbcType=VARCHAR}, #{dayMileage,jdbcType=REAL}, #{code,jdbcType=VARCHAR},
        #{opentime,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{iata,jdbcType=VARCHAR},
        #{postalCode,jdbcType=VARCHAR}, #{howToGet,jdbcType=VARCHAR}, #{howToGo,jdbcType=VARCHAR},
        #{exchangeRate,jdbcType=REAL}, #{minRentDays,jdbcType=INTEGER}, #{ordercodePre,jdbcType=VARCHAR},
        #{time,jdbcType=INTEGER}, #{ip,jdbcType=VARCHAR}, #{mid,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
        #{everyPrice,jdbcType=REAL}, #{monetaryUnit,jdbcType=VARCHAR}, #{kilometers,jdbcType=VARCHAR},
        #{enname,jdbcType=VARCHAR}, #{enaddress,jdbcType=VARCHAR}, #{weekdayevening,jdbcType=VARCHAR},
        #{weekdaymoring,jdbcType=VARCHAR}, #{feescale,jdbcType=REAL}, #{weekendevening,jdbcType=VARCHAR},
        #{weekendmoring,jdbcType=VARCHAR}, #{standardcharge,jdbcType=VARCHAR}, #{displaystatus,jdbcType=INTEGER},
        #{childseat,jdbcType=REAL}, #{timeDelay,jdbcType=REAL}, #{cardType,jdbcType=VARCHAR},
        #{isVcc,jdbcType=INTEGER}, #{quotaInterval,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
        #{orderTerms,jdbcType=LONGVARCHAR}, #{orderPrecautions,jdbcType=LONGVARCHAR}, #{confirmNow,jdbcType=VARCHAR},
        #{minBookHour,jdbcType=INTEGER}, #{localcurrencyUnit,jdbcType=VARCHAR}, #{arrivalWay,jdbcType=INTEGER},
        #{vendorId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ixtech.management.repo.entity.Store"
            useGeneratedKeys="true">
        insert into jipinzuche_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="active != null">
                active,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="countryid != null">
                countryid,
            </if>
            <if test="provinceid != null">
                provinceid,
            </if>
            <if test="cityid != null">
                cityid,
            </if>
            <if test="countyid != null">
                countyid,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="lat != null">
                lat,
            </if>
            <if test="lon != null">
                lon,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="litpic != null">
                litpic,
            </if>
            <if test="mapImgSmall != null">
                map_img_small,
            </if>
            <if test="mapImgLarge != null">
                map_img_large,
            </if>
            <if test="intro != null">
                intro,
            </if>
            <if test="principal != null">
                principal,
            </if>
            <if test="principalMobile != null">
                principal_mobile,
            </if>
            <if test="mobileAreaCode != null">
                mobile_area_code,
            </if>
            <if test="currencyUnit != null">
                currency_unit,
            </if>
            <if test="dayMileage != null">
                day_mileage,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="opentime != null">
                opentime,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="iata != null">
                iata,
            </if>
            <if test="postalCode != null">
                postal_code,
            </if>
            <if test="howToGet != null">
                how_to_get,
            </if>
            <if test="howToGo != null">
                how_to_go,
            </if>
            <if test="exchangeRate != null">
                exchange_rate,
            </if>
            <if test="minRentDays != null">
                min_rent_days,
            </if>
            <if test="ordercodePre != null">
                ordercode_pre,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="mid != null">
                mid,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="everyPrice != null">
                every_price,
            </if>
            <if test="monetaryUnit != null">
                monetary_unit,
            </if>
            <if test="kilometers != null">
                kilometers,
            </if>
            <if test="enname != null">
                enname,
            </if>
            <if test="enaddress != null">
                enaddress,
            </if>
            <if test="weekdayevening != null">
                weekdayevening,
            </if>
            <if test="weekdaymoring != null">
                weekdaymoring,
            </if>
            <if test="feescale != null">
                feescale,
            </if>
            <if test="weekendevening != null">
                weekendevening,
            </if>
            <if test="weekendmoring != null">
                weekendmoring,
            </if>
            <if test="standardcharge != null">
                standardcharge,
            </if>
            <if test="displaystatus != null">
                displaystatus,
            </if>
            <if test="childseat != null">
                childseat,
            </if>
            <if test="timeDelay != null">
                time_delay,
            </if>
            <if test="cardType != null">
                card_type,
            </if>
            <if test="isVcc != null">
                is_vcc,
            </if>
            <if test="quotaInterval != null">
                quota_interval,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="orderTerms != null">
                order_terms,
            </if>
            <if test="orderPrecautions != null">
                order_precautions,
            </if>
            <if test="confirmNow != null">
                confirm_now,
            </if>
            <if test="minBookHour != null">
                min_book_hour,
            </if>
            <if test="localcurrencyUnit != null">
                localcurrency_unit,
            </if>
            <if test="arrivalWay != null">
                arrival_way,
            </if>
            <if test="vendorId != null">
                vendor_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="active != null">
                #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="countryid != null">
                #{countryid,jdbcType=INTEGER},
            </if>
            <if test="provinceid != null">
                #{provinceid,jdbcType=INTEGER},
            </if>
            <if test="cityid != null">
                #{cityid,jdbcType=INTEGER},
            </if>
            <if test="countyid != null">
                #{countyid,jdbcType=INTEGER},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                #{lat,jdbcType=REAL},
            </if>
            <if test="lon != null">
                #{lon,jdbcType=REAL},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="litpic != null">
                #{litpic,jdbcType=VARCHAR},
            </if>
            <if test="mapImgSmall != null">
                #{mapImgSmall,jdbcType=VARCHAR},
            </if>
            <if test="mapImgLarge != null">
                #{mapImgLarge,jdbcType=VARCHAR},
            </if>
            <if test="intro != null">
                #{intro,jdbcType=VARCHAR},
            </if>
            <if test="principal != null">
                #{principal,jdbcType=VARCHAR},
            </if>
            <if test="principalMobile != null">
                #{principalMobile,jdbcType=VARCHAR},
            </if>
            <if test="mobileAreaCode != null">
                #{mobileAreaCode,jdbcType=VARCHAR},
            </if>
            <if test="currencyUnit != null">
                #{currencyUnit,jdbcType=VARCHAR},
            </if>
            <if test="dayMileage != null">
                #{dayMileage,jdbcType=REAL},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="opentime != null">
                #{opentime,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="iata != null">
                #{iata,jdbcType=VARCHAR},
            </if>
            <if test="postalCode != null">
                #{postalCode,jdbcType=VARCHAR},
            </if>
            <if test="howToGet != null">
                #{howToGet,jdbcType=VARCHAR},
            </if>
            <if test="howToGo != null">
                #{howToGo,jdbcType=VARCHAR},
            </if>
            <if test="exchangeRate != null">
                #{exchangeRate,jdbcType=REAL},
            </if>
            <if test="minRentDays != null">
                #{minRentDays,jdbcType=INTEGER},
            </if>
            <if test="ordercodePre != null">
                #{ordercodePre,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                #{time,jdbcType=INTEGER},
            </if>
            <if test="ip != null">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="mid != null">
                #{mid,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="everyPrice != null">
                #{everyPrice,jdbcType=REAL},
            </if>
            <if test="monetaryUnit != null">
                #{monetaryUnit,jdbcType=VARCHAR},
            </if>
            <if test="kilometers != null">
                #{kilometers,jdbcType=VARCHAR},
            </if>
            <if test="enname != null">
                #{enname,jdbcType=VARCHAR},
            </if>
            <if test="enaddress != null">
                #{enaddress,jdbcType=VARCHAR},
            </if>
            <if test="weekdayevening != null">
                #{weekdayevening,jdbcType=VARCHAR},
            </if>
            <if test="weekdaymoring != null">
                #{weekdaymoring,jdbcType=VARCHAR},
            </if>
            <if test="feescale != null">
                #{feescale,jdbcType=REAL},
            </if>
            <if test="weekendevening != null">
                #{weekendevening,jdbcType=VARCHAR},
            </if>
            <if test="weekendmoring != null">
                #{weekendmoring,jdbcType=VARCHAR},
            </if>
            <if test="standardcharge != null">
                #{standardcharge,jdbcType=VARCHAR},
            </if>
            <if test="displaystatus != null">
                #{displaystatus,jdbcType=INTEGER},
            </if>
            <if test="childseat != null">
                #{childseat,jdbcType=REAL},
            </if>
            <if test="timeDelay != null">
                #{timeDelay,jdbcType=REAL},
            </if>
            <if test="cardType != null">
                #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="isVcc != null">
                #{isVcc,jdbcType=INTEGER},
            </if>
            <if test="quotaInterval != null">
                #{quotaInterval,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="orderTerms != null">
                #{orderTerms,jdbcType=LONGVARCHAR},
            </if>
            <if test="orderPrecautions != null">
                #{orderPrecautions,jdbcType=LONGVARCHAR},
            </if>
            <if test="confirmNow != null">
                #{confirmNow,jdbcType=VARCHAR},
            </if>
            <if test="minBookHour != null">
                #{minBookHour,jdbcType=INTEGER},
            </if>
            <if test="localcurrencyUnit != null">
                #{localcurrencyUnit,jdbcType=VARCHAR},
            </if>
            <if test="arrivalWay != null">
                #{arrivalWay,jdbcType=INTEGER},
            </if>
            <if test="vendorId != null">
                #{vendorId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ixtech.management.repo.entity.Store">
        update jipinzuche_store
        <set>
            <if test="active != null">
                active = #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="countryid != null">
                countryid = #{countryid,jdbcType=INTEGER},
            </if>
            <if test="provinceid != null">
                provinceid = #{provinceid,jdbcType=INTEGER},
            </if>
            <if test="cityid != null">
                cityid = #{cityid,jdbcType=INTEGER},
            </if>
            <if test="countyid != null">
                countyid = #{countyid,jdbcType=INTEGER},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=REAL},
            </if>
            <if test="lon != null">
                lon = #{lon,jdbcType=REAL},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="litpic != null">
                litpic = #{litpic,jdbcType=VARCHAR},
            </if>
            <if test="mapImgSmall != null">
                map_img_small = #{mapImgSmall,jdbcType=VARCHAR},
            </if>
            <if test="mapImgLarge != null">
                map_img_large = #{mapImgLarge,jdbcType=VARCHAR},
            </if>
            <if test="intro != null">
                intro = #{intro,jdbcType=VARCHAR},
            </if>
            <if test="principal != null">
                principal = #{principal,jdbcType=VARCHAR},
            </if>
            <if test="principalMobile != null">
                principal_mobile = #{principalMobile,jdbcType=VARCHAR},
            </if>
            <if test="mobileAreaCode != null">
                mobile_area_code = #{mobileAreaCode,jdbcType=VARCHAR},
            </if>
            <if test="currencyUnit != null">
                currency_unit = #{currencyUnit,jdbcType=VARCHAR},
            </if>
            <if test="dayMileage != null">
                day_mileage = #{dayMileage,jdbcType=REAL},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="opentime != null">
                opentime = #{opentime,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="iata != null">
                iata = #{iata,jdbcType=VARCHAR},
            </if>
            <if test="postalCode != null">
                postal_code = #{postalCode,jdbcType=VARCHAR},
            </if>
            <if test="howToGet != null">
                how_to_get = #{howToGet,jdbcType=VARCHAR},
            </if>
            <if test="howToGo != null">
                how_to_go = #{howToGo,jdbcType=VARCHAR},
            </if>
            <if test="exchangeRate != null">
                exchange_rate = #{exchangeRate,jdbcType=REAL},
            </if>
            <if test="minRentDays != null">
                min_rent_days = #{minRentDays,jdbcType=INTEGER},
            </if>
            <if test="ordercodePre != null">
                ordercode_pre = #{ordercodePre,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=INTEGER},
            </if>
            <if test="ip != null">
                ip = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="mid != null">
                mid = #{mid,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="everyPrice != null">
                every_price = #{everyPrice,jdbcType=REAL},
            </if>
            <if test="monetaryUnit != null">
                monetary_unit = #{monetaryUnit,jdbcType=VARCHAR},
            </if>
            <if test="kilometers != null">
                kilometers = #{kilometers,jdbcType=VARCHAR},
            </if>
            <if test="enname != null">
                enname = #{enname,jdbcType=VARCHAR},
            </if>
            <if test="enaddress != null">
                enaddress = #{enaddress,jdbcType=VARCHAR},
            </if>
            <if test="weekdayevening != null">
                weekdayevening = #{weekdayevening,jdbcType=VARCHAR},
            </if>
            <if test="weekdaymoring != null">
                weekdaymoring = #{weekdaymoring,jdbcType=VARCHAR},
            </if>
            <if test="feescale != null">
                feescale = #{feescale,jdbcType=REAL},
            </if>
            <if test="weekendevening != null">
                weekendevening = #{weekendevening,jdbcType=VARCHAR},
            </if>
            <if test="weekendmoring != null">
                weekendmoring = #{weekendmoring,jdbcType=VARCHAR},
            </if>
            <if test="standardcharge != null">
                standardcharge = #{standardcharge,jdbcType=VARCHAR},
            </if>
            <if test="displaystatus != null">
                displaystatus = #{displaystatus,jdbcType=INTEGER},
            </if>
            <if test="childseat != null">
                childseat = #{childseat,jdbcType=REAL},
            </if>
            <if test="timeDelay != null">
                time_delay = #{timeDelay,jdbcType=REAL},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="isVcc != null">
                is_vcc = #{isVcc,jdbcType=INTEGER},
            </if>
            <if test="quotaInterval != null">
                quota_interval = #{quotaInterval,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="orderTerms != null">
                order_terms = #{orderTerms,jdbcType=LONGVARCHAR},
            </if>
            <if test="orderPrecautions != null">
                order_precautions = #{orderPrecautions,jdbcType=LONGVARCHAR},
            </if>
            <if test="confirmNow != null">
                confirm_now = #{confirmNow,jdbcType=VARCHAR},
            </if>
            <if test="minBookHour != null">
                min_book_hour = #{minBookHour,jdbcType=INTEGER},
            </if>
            <if test="localcurrencyUnit != null">
                localcurrency_unit = #{localcurrencyUnit,jdbcType=VARCHAR},
            </if>
            <if test="arrivalWay != null">
                arrival_way = #{arrivalWay,jdbcType=INTEGER},
            </if>
            <if test="vendorId != null">
                vendor_id = #{vendorId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ixtech.management.repo.entity.Store">
        update jipinzuche_store
        set active = #{active,jdbcType=BIT},
        deleted = #{deleted,jdbcType=BIT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        countryid = #{countryid,jdbcType=INTEGER},
        provinceid = #{provinceid,jdbcType=INTEGER},
        cityid = #{cityid,jdbcType=INTEGER},
        countyid = #{countyid,jdbcType=INTEGER},
        address = #{address,jdbcType=VARCHAR},
        lat = #{lat,jdbcType=REAL},
        lon = #{lon,jdbcType=REAL},
        `name` = #{name,jdbcType=VARCHAR},
        litpic = #{litpic,jdbcType=VARCHAR},
        map_img_small = #{mapImgSmall,jdbcType=VARCHAR},
        map_img_large = #{mapImgLarge,jdbcType=VARCHAR},
        intro = #{intro,jdbcType=VARCHAR},
        principal = #{principal,jdbcType=VARCHAR},
        principal_mobile = #{principalMobile,jdbcType=VARCHAR},
        mobile_area_code = #{mobileAreaCode,jdbcType=VARCHAR},
        currency_unit = #{currencyUnit,jdbcType=VARCHAR},
        day_mileage = #{dayMileage,jdbcType=REAL},
        code = #{code,jdbcType=VARCHAR},
        opentime = #{opentime,jdbcType=VARCHAR},
        `type` = #{type,jdbcType=INTEGER},
        iata = #{iata,jdbcType=VARCHAR},
        postal_code = #{postalCode,jdbcType=VARCHAR},
        how_to_get = #{howToGet,jdbcType=VARCHAR},
        how_to_go = #{howToGo,jdbcType=VARCHAR},
        exchange_rate = #{exchangeRate,jdbcType=REAL},
        min_rent_days = #{minRentDays,jdbcType=INTEGER},
        ordercode_pre = #{ordercodePre,jdbcType=VARCHAR},
        `time` = #{time,jdbcType=INTEGER},
        ip = #{ip,jdbcType=VARCHAR},
        mid = #{mid,jdbcType=INTEGER},
        `status` = #{status,jdbcType=INTEGER},
        every_price = #{everyPrice,jdbcType=REAL},
        monetary_unit = #{monetaryUnit,jdbcType=VARCHAR},
        kilometers = #{kilometers,jdbcType=VARCHAR},
        enname = #{enname,jdbcType=VARCHAR},
        enaddress = #{enaddress,jdbcType=VARCHAR},
        weekdayevening = #{weekdayevening,jdbcType=VARCHAR},
        weekdaymoring = #{weekdaymoring,jdbcType=VARCHAR},
        feescale = #{feescale,jdbcType=REAL},
        weekendevening = #{weekendevening,jdbcType=VARCHAR},
        weekendmoring = #{weekendmoring,jdbcType=VARCHAR},
        standardcharge = #{standardcharge,jdbcType=VARCHAR},
        displaystatus = #{displaystatus,jdbcType=INTEGER},
        childseat = #{childseat,jdbcType=REAL},
        time_delay = #{timeDelay,jdbcType=REAL},
        card_type = #{cardType,jdbcType=VARCHAR},
        is_vcc = #{isVcc,jdbcType=INTEGER},
        quota_interval = #{quotaInterval,jdbcType=VARCHAR},
        url = #{url,jdbcType=VARCHAR},
        order_terms = #{orderTerms,jdbcType=LONGVARCHAR},
        order_precautions = #{orderPrecautions,jdbcType=LONGVARCHAR},
        confirm_now = #{confirmNow,jdbcType=VARCHAR},
        min_book_hour = #{minBookHour,jdbcType=INTEGER},
        localcurrency_unit = #{localcurrencyUnit,jdbcType=VARCHAR},
        arrival_way = #{arrivalWay,jdbcType=INTEGER},
        vendor_id = #{vendorId,jdbcType=BIGINT}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        update jipinzuche_store
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="countryid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.countryid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="provinceid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.provinceid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cityid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.cityid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="countyid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.countyid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.address,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lat = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.lat,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="lon = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.lon,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="litpic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.litpic,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="map_img_small = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.mapImgSmall,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="map_img_large = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.mapImgLarge,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="intro = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.intro,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="principal = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.principal,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="principal_mobile = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.principalMobile,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mobile_area_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.mobileAreaCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="currency_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.currencyUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="day_mileage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.dayMileage,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.code,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="opentime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.opentime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`type` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.type,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="iata = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.iata,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="postal_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.postalCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="how_to_get = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.howToGet,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="how_to_go = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.howToGo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="exchange_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.exchangeRate,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="min_rent_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.minRentDays,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ordercode_pre = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.ordercodePre,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`time` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.time,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.ip,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.mid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="every_price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.everyPrice,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="monetary_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.monetaryUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kilometers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.kilometers,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enname = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.enname,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enaddress = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.enaddress,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weekdayevening = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.weekdayevening,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weekdaymoring = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.weekdaymoring,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="feescale = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.feescale,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="weekendevening = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.weekendevening,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weekendmoring = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.weekendmoring,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standardcharge = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.standardcharge,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="displaystatus = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.displaystatus,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="childseat = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.childseat,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="time_delay = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.timeDelay,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="card_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.cardType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_vcc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.isVcc,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="quota_interval = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.quotaInterval,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.url,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_terms = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.orderTerms,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_precautions = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.orderPrecautions,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="confirm_now = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.confirmNow,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_book_hour = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.minBookHour,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="localcurrency_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.localcurrencyUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="arrival_way = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.arrivalWay,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="vendor_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.vendorId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        update jipinzuche_store
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.active != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deleted != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="countryid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.countryid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.countryid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="provinceid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.provinceid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.provinceid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cityid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cityid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.cityid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="countyid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.countyid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.countyid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.address != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.address,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lat = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lat != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.lat,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="lon = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lon != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.lon,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="litpic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.litpic != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.litpic,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="map_img_small = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mapImgSmall != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.mapImgSmall,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="map_img_large = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mapImgLarge != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.mapImgLarge,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="intro = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.intro != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.intro,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="principal = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.principal != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.principal,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="principal_mobile = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.principalMobile != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.principalMobile,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mobile_area_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mobileAreaCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.mobileAreaCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="currency_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.currencyUnit != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.currencyUnit,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="day_mileage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dayMileage != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.dayMileage,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.code != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.code,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="opentime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.opentime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.opentime,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`type` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.type != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.type,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="iata = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.iata != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.iata,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="postal_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.postalCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.postalCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="how_to_get = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.howToGet != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.howToGet,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="how_to_go = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.howToGo != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.howToGo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="exchange_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.exchangeRate != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.exchangeRate,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="min_rent_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.minRentDays != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.minRentDays,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ordercode_pre = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ordercodePre != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.ordercodePre,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`time` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.time != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.time,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ip != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.ip,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.mid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="every_price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.everyPrice != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.everyPrice,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="monetary_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.monetaryUnit != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.monetaryUnit,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="kilometers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.kilometers != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.kilometers,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="enname = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.enname != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.enname,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="enaddress = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.enaddress != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.enaddress,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="weekdayevening = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.weekdayevening != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.weekdayevening,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="weekdaymoring = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.weekdaymoring != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.weekdaymoring,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="feescale = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.feescale != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.feescale,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="weekendevening = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.weekendevening != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.weekendevening,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="weekendmoring = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.weekendmoring != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.weekendmoring,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="standardcharge = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.standardcharge != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.standardcharge,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="displaystatus = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.displaystatus != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.displaystatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="childseat = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.childseat != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.childseat,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="time_delay = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.timeDelay != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.timeDelay,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="card_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cardType != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.cardType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_vcc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isVcc != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.isVcc,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="quota_interval = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.quotaInterval != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.quotaInterval,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.url != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.url,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_terms = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderTerms != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orderTerms,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_precautions = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderPrecautions != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.orderPrecautions,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="confirm_now = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.confirmNow != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.confirmNow,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="min_book_hour = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.minBookHour != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.minBookHour,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="localcurrency_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.localcurrencyUnit != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.localcurrencyUnit,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="arrival_way = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.arrivalWay != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.arrivalWay,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="vendor_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.vendorId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.vendorId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_store
        (active, deleted, create_time, update_time, countryid, provinceid, cityid, countyid,
        address, lat, lon, `name`, litpic, map_img_small, map_img_large, intro, principal,
        principal_mobile, mobile_area_code, currency_unit, day_mileage, code, opentime,
        `type`, iata, postal_code, how_to_get, how_to_go, exchange_rate, min_rent_days,
        ordercode_pre, `time`, ip, mid, `status`, every_price, monetary_unit, kilometers,
        enname, enaddress, weekdayevening, weekdaymoring, feescale, weekendevening, weekendmoring,
        standardcharge, displaystatus, childseat, time_delay, card_type, is_vcc, quota_interval,
        url, order_terms, order_precautions, confirm_now, min_book_hour, localcurrency_unit,
        arrival_way, vendor_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.active,jdbcType=BIT}, #{item.deleted,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.countryid,jdbcType=INTEGER},
            #{item.provinceid,jdbcType=INTEGER},
            #{item.cityid,jdbcType=INTEGER}, #{item.countyid,jdbcType=INTEGER}, #{item.address,jdbcType=VARCHAR},
            #{item.lat,jdbcType=REAL}, #{item.lon,jdbcType=REAL}, #{item.name,jdbcType=VARCHAR},
            #{item.litpic,jdbcType=VARCHAR}, #{item.mapImgSmall,jdbcType=VARCHAR}, #{item.mapImgLarge,jdbcType=VARCHAR},
            #{item.intro,jdbcType=VARCHAR}, #{item.principal,jdbcType=VARCHAR},
            #{item.principalMobile,jdbcType=VARCHAR},
            #{item.mobileAreaCode,jdbcType=VARCHAR}, #{item.currencyUnit,jdbcType=VARCHAR},
            #{item.dayMileage,jdbcType=REAL}, #{item.code,jdbcType=VARCHAR}, #{item.opentime,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER}, #{item.iata,jdbcType=VARCHAR}, #{item.postalCode,jdbcType=VARCHAR},
            #{item.howToGet,jdbcType=VARCHAR}, #{item.howToGo,jdbcType=VARCHAR}, #{item.exchangeRate,jdbcType=REAL},
            #{item.minRentDays,jdbcType=INTEGER}, #{item.ordercodePre,jdbcType=VARCHAR}, #{item.time,jdbcType=INTEGER},
            #{item.ip,jdbcType=VARCHAR}, #{item.mid,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER},
            #{item.everyPrice,jdbcType=REAL}, #{item.monetaryUnit,jdbcType=VARCHAR},
            #{item.kilometers,jdbcType=VARCHAR},
            #{item.enname,jdbcType=VARCHAR}, #{item.enaddress,jdbcType=VARCHAR},
            #{item.weekdayevening,jdbcType=VARCHAR},
            #{item.weekdaymoring,jdbcType=VARCHAR}, #{item.feescale,jdbcType=REAL},
            #{item.weekendevening,jdbcType=VARCHAR},
            #{item.weekendmoring,jdbcType=VARCHAR}, #{item.standardcharge,jdbcType=VARCHAR},
            #{item.displaystatus,jdbcType=INTEGER}, #{item.childseat,jdbcType=REAL}, #{item.timeDelay,jdbcType=REAL},
            #{item.cardType,jdbcType=VARCHAR}, #{item.isVcc,jdbcType=INTEGER}, #{item.quotaInterval,jdbcType=VARCHAR},
            #{item.url,jdbcType=VARCHAR}, #{item.orderTerms,jdbcType=LONGVARCHAR},
            #{item.orderPrecautions,jdbcType=LONGVARCHAR},
            #{item.confirmNow,jdbcType=VARCHAR}, #{item.minBookHour,jdbcType=INTEGER},
            #{item.localcurrencyUnit,jdbcType=VARCHAR},
            #{item.arrivalWay,jdbcType=INTEGER}, #{item.vendorId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <select id="getStoreCoveredAreaCountByVendorIds" resultType="com.ixtech.management.repo.model.VendorAreaCountModel">
        SELECT
        vendor_id AS vendorId,
        COUNT(DISTINCT countryid) AS coveredCountryCount,
        -- 解决cityid对应的area为'市辖区'导致统计不准的问题
        COUNT(DISTINCT CONCAT(provinceid, '-', cityid)) AS coveredCityCount
        FROM jipinzuche_store
        <where>
            vendor_id IN
            <foreach item="item" collection="vendorIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND
            <include refid="Ignore_Deleted"/>
        </where>
        GROUP BY
        vendor_id
    </select>

    <select id="getStoreCountByVendorIds" resultType="com.ixtech.management.repo.model.CountModel">
        SELECT
        vendor_id AS targetId,
        COUNT(id) AS totalCount,
        SUM( CASE WHEN displaystatus = 1 THEN 1 ELSE 0 END ) AS activeCount
        FROM jipinzuche_store
        <where>
            vendor_id IN
            <foreach item="item" collection="vendorIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND
            <include refid="Ignore_Deleted"/>
        </where>
        GROUP BY
        vendor_id
    </select>

    <select id="getStoreCarCountByVendorIds" resultType="com.ixtech.management.repo.model.CountModel">
        SELECT
        s.vendor_id AS targetId,
        COUNT(cl.id) AS totalCount,
        COUNT(cl.id) - COUNT(DISTINCT clr.carid) AS activeCount
        FROM jipinzuche_store s
        INNER JOIN jipinzuche_car_stock cs ON cs.storeid = s.id
        INNER JOIN jipinzuche_car_list cl ON cl.stockid = cs.id
        LEFT JOIN jipinzuche_car_list_repair clr ON clr.carid = cl.id AND clr.is_end = 0 AND clr.is_delete = 0
        <where>
            s.vendor_id IN
            <foreach item="item" collection="vendorIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND s.
            <include refid="Ignore_Deleted"/>
            AND cs.
            <include refid="Ignore_Deleted"/>
            AND cl.
            <include refid="Ignore_Deleted"/>
        </where>
        GROUP BY
        s.vendor_id
    </select>

    <select id="getStoreCarCountByStoreIds" resultType="com.ixtech.management.repo.model.CountModel">
        SELECT
        cs.storeid AS targetId,
        COUNT(cl.id) AS totalCount,
        COUNT(cl.id) - COUNT(DISTINCT clr.carid) AS activeCount
        FROM jipinzuche_car_stock cs
        INNER JOIN jipinzuche_car_list cl ON cl.stockid = cs.id
        LEFT JOIN jipinzuche_car_list_repair clr ON clr.carid = cl.id AND clr.is_end = 0 AND clr.is_delete = 0
        <where>
            cs.storeid IN
            <foreach item="item" collection="storeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND cs.
            <include refid="Ignore_Deleted"/>
            AND cl.
            <include refid="Ignore_Deleted"/>
        </where>
        GROUP BY
        cs.storeid
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        id, countryid, provinceid, cityid, countyid, type, `name`, displaystatus, vendor_id,
        create_time, update_time, active
        FROM jipinzuche_store
        <where>
            <include refid="Ignore_Deleted"/>
            <if test="name != null and name !=''">
                AND `name` LIKE CONCAT('%', REPLACE(#{name,jdbcType=VARCHAR}, '_', '\\_'), '%')
            </if>
            <if test="countryId != null">
                AND countryid = #{countryId}
            </if>
            <if test="provinceId != null">
                AND provinceid = #{provinceId}
            </if>
            <if test="cityId != null">
                AND cityid = #{cityId}
            </if>
            <if test="vendorId != null">
                AND vendor_id = #{vendorId}
            </if>
            <if test="types != null and types.size() > 0">
                AND type IN
                <foreach item="item" collection="types" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="displayStatus != null">
                AND displaystatus = #{displayStatus}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>