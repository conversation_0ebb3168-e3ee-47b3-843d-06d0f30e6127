package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 车型
 *
 * <AUTHOR>
 * @since 2025-03-25 14:38:41
 */
@Data
public class JipinzucheCarModelDTO {

    @JsonProperty("id")
    private Long id; // 修改：从 Integer 改为 Long

    /**
     * car_brand的id
     */
    @JsonProperty("brand_id")
    private Long brandid; // 修改：从 Integer 改为 Long

    /**
     * 车辆图片
     */
    @JsonProperty("litpic")
    private String litpic;

    /**
     * 车型名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 车辆四字码
     */
    @JsonProperty("car_code")
    private String carcode;

    /**
     * 座位数
     */
    @JsonProperty("seat")
    private Integer seat;

    /**
     * 车门数
     */
    @JsonProperty("door")
    private Integer door;

    /**
     * 行李数
     */
    @JsonProperty("luggage")
    private Integer luggage;

    /**
     * 1->自动挡；2->手动挡
     */
    @JsonProperty("transmission")
    private Integer transmission;

    /**
     * 1->有效；-1->删除
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 车辆成本价
     */
    @JsonProperty("day_price")
    private Integer dayprice;
}
