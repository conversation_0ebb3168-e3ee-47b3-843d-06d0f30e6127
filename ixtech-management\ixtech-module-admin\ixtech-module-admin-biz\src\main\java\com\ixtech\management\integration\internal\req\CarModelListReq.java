package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.ixtech.management.common.dto.PageRequest;
import lombok.Data;

/**
 * 渠道列表请求Req
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:33
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CarModelListReq {

    private Long storeid;

}
