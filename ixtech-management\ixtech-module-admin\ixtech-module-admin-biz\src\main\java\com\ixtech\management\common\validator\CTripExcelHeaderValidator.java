package com.ixtech.management.common.validator;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.exception.BizException;
import com.ixtech.management.config.ExcelHeaderCheckException;
import com.ixtech.management.domain.service.CarOrderService;
import com.ixtech.management.integration.internal.client.OrdersrvFeignClient;
import com.ixtech.management.integration.internal.client.ProductsrvFeignClient;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.*;
import com.ixtech.management.repo.entity.*;
import com.ixtech.management.repo.repository.VendorRepository;
import jakarta.annotation.Resource;
import org.apache.commons.validator.routines.EmailValidator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@Component
@ValidatorFor("携程")
public class CTripExcelHeaderValidator implements ExcelHeaderValidator {

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(PATTERN);


    @Resource
    private OrdersrvFeignClient ordersrvFeignClient;

    @Resource
    private ProductsrvFeignClient productsrvFeignClient;

    @Resource
    private CarOrderService carOrderService;

    private static final List<String> REQUIRED_HEADERS =
            List.of("order Id", "order date", "customer name",
                    "customer contact", "email", "pickup date",
                    "return date", "vehicle", "prepay amount（after markup）",
                    "pay on arrival（after markup）", "total amount（after markup）",
                    "currency code（after markup）", "Package Name", "SIPP Code",
                    "Pick-up Branch Name", "Drop-off Branch Name", "Country of Origin", "Settlement Amount");

    private static Map<String, Integer> columnMustMap = new HashMap<>();
    private static Map<String, Integer> columnMap = new HashMap<>();


    static {
        columnMap.put("order Id", 0);  // 1-1=0
        columnMap.put("order date", 1);  // 2-1=1
        columnMap.put("confirm number", 4);  // 5-1=4
        columnMap.put("customer name", 6);  // 7-1=6
        columnMap.put("customer contact", 7);  // 8-1=7
        columnMap.put("email", 8);  // 9-1=8
        columnMap.put("flight number", 9);  // 10-1=9
        columnMap.put("pickup date", 14);  // 15-1=14
        columnMap.put("return date", 15);  // 16-1=15
        columnMap.put("vehicle", 19);  // 20-1=19
        columnMap.put("prepay amount（after markup）", 21);  // 22-1=21
        columnMap.put("pay on arrival（after markup）", 22);  // 23-1=22
        columnMap.put("total amount（after markup）", 23);  // 24-1=23
        columnMap.put("currency code（after markup）", 24);  // 25-1=24
        columnMap.put("order status", 25);
        columnMap.put("remark", 26);  // 27-1=26
        columnMap.put("One way fee", 31);  // 32-1=31
        columnMap.put("currency&", 32);  // 33-1=32
        columnMap.put("Payment method&", 33);  // 34-1=33
        columnMap.put("Special opening hours fee for pickup", 34);  // 35-1=34
        columnMap.put("currency&&", 35);  // 36-1=35
        columnMap.put("Payment method&&", 36);  // 37-1=36
        columnMap.put("Special opening hours fee for dropoff", 37);  // 38-1=37
        columnMap.put("option name(out of package)", 40);  // 41-1=40
        columnMap.put("fee", 41);  // 42-1=41
        columnMap.put("currency", 42);  // 43-1=42
        columnMap.put("Payment method&&&", 43);  // 44-1=43
        columnMap.put("Package Name", 46);  // 47-1=46
        columnMap.put("SIPP Code", 50);  // 51-1=50
        columnMap.put("Pick-up Branch Name", 55);  // 56-1=55
        columnMap.put("Drop-off Branch Name", 58);  // 59-1=58
        columnMap.put("Country of Origin", 59);  // 60-1=59
        columnMap.put("Settlement Amount", 60);  // 61-1=60
        columnMap.put("Platform of Order", 63);  // 64-1=63
        columnMap.put("Purchase Platform Insurance", 69);  // 70-1=69
    }

    static {
        columnMustMap.put("order Id", 0);  // 1-1=0
        columnMustMap.put("order date", 1);  // 2-1=1
//        columnMustMap.put("confirm number", 4);  // 5-1=4
        columnMustMap.put("customer name", 6);  // 7-1=6
        columnMustMap.put("customer contact", 7);  // 8-1=7
        columnMustMap.put("email", 8);  // 9-1=8
//        columnMap.put("flight number", 9);  // 10-1=9
        columnMustMap.put("pickup date", 14);  // 15-1=14
        columnMustMap.put("return date", 15);  // 16-1=15
        columnMustMap.put("vehicle", 19);  // 20-1=19
        columnMustMap.put("prepay amount（after markup）", 21);  // 22-1=21
        columnMustMap.put("pay on arrival（after markup）", 22);  // 23-1=22
        columnMustMap.put("total amount（after markup）", 23);  // 24-1=23
        columnMustMap.put("currency code（after markup）", 24);  // 25-1=24
        columnMustMap.put("order status", 25);  // 25-1=24
//        columnMap.put("remark", 26);  // 27-1=26
//        columnMap.put("One way fee", 31);  // 32-1=31
//        columnMap.put("currency", 32);  // 33-1=32
//        columnMap.put("Payment method", 33);  // 34-1=33
//        columnMap.put("Special opening hours fee for pickup", 34);  // 35-1=34
//        columnMap.put("currency", 35);  // 36-1=35
//        columnMap.put("Payment method", 36);  // 37-1=36
//        columnMap.put("Special opening hours fee for dropoff", 37);  // 38-1=37
//        columnMap.put("option name(out of package)", 40);  // 41-1=40
//        columnMap.put("fee", 41);  // 42-1=41
//        columnMap.put("currency", 42);  // 43-1=42
//        columnMap.put("Payment method", 43);  // 44-1=43
        columnMustMap.put("Package Name", 46);  // 47-1=46
        columnMustMap.put("SIPP Code", 50);  // 51-1=50
        columnMustMap.put("Pick-up Branch Name", 55);  // 56-1=55
        columnMustMap.put("Drop-off Branch Name", 58);  // 59-1=58
        columnMustMap.put("Country of Origin", 59);  // 60-1=59
        columnMustMap.put("Settlement Amount", 60);  // 61-1=60
//        columnMustMap.put("Platform of Order", 63);  // 64-1=63
//        columnMap.put("Purchase Platform Insurance", 69);
    }

    public static Map<String, String> carBrands = new HashMap<String, String>() {{
        put("雷诺", "Renault");
        put("斯巴鲁", "SUBARU");
        put("霍顿", "Holden");
        put("Jeep", "Jeep");
        put("铃木", "Suzuki");
        put("Kia", "Kia");
        put("起亚", "Kia");
        put("西雅特", "Seat");
        put("雷克萨斯", "LeXuS");
        put("现代", "Hyundai");
        put("雪铁龙", "Citroen");
        put("奥迪", "Audi");
        put("捷豹", "Jaguar");
        put("斯柯达", "Skoda");
        put("沃尔沃", "Volvo");
        put("福特", "Ford");
        put("三菱", "Mitsubishi");
        put("Opel", "Opel");
        put("欧宝", "Opel");
        put("宝马", "BMW");
        put("Volkswagen", "Volkswagen");
        put("大众", "Volkswagen");
        put("日产", "Nissan");
        put("本田", "Honda");
        put("奔驰", "Benz");
        put("丰田", "Toyota");
    }};
    @Autowired
    private VendorRepository vendorRepository;


    @Override
    public void validateHeaders(List<String> headers) throws ExcelHeaderCheckException {
        columnMap.forEach((key, value) -> {
            if (key.contains("&")) {
                key = key.replaceAll("&", "");
            }
            if (!headers.contains(key) || headers.size() != 83 || !headers.get(value).equals(key)) {
                throw new BizException("选择的渠道与导入的 excel 字段格式不对应，请确认选择的渠道和上传的 excel 文件");
            }
        });

    }

    @Override
    public void validateRowData(
            Integer source, Set<String> sourceIdSet, Row row, CarOrderImportRespVO.CarOrderCheckOrImportResult lineInfo) throws ExcelHeaderCheckException {

        StringBuilder checkFailReason = new StringBuilder();
        columnMap.forEach((info, rowNum) -> {
            if (null == row) {
                throw new BizException("存在空行数据，请删除空行后重试");
            }
            Cell orderIdCell = row.getCell(rowNum);
            if (orderIdCell == null || orderIdCell.getCellType() == CellType.BLANK
                    || (orderIdCell.getCellType() == CellType.STRING && orderIdCell.getStringCellValue().trim().isEmpty())) {
                if (columnMustMap.containsKey(info)) {
                    // 处理空值情况
                    checkFailReason.append(info).append("不能为空；");
                }
            }
        });

        if (checkFailReason.isEmpty()) {

            // 校验order date
            if (!isValidFormat(row.getCell(columnMap.get("order date")).getStringCellValue())) {
                checkFailReason.append("order date值异常；");
            }

            Cell contactCell = row.getCell(columnMap.get("customer contact"));
            if (contactCell != null) {
                String contactValue = "";
                // 根据单元格类型获取值
                switch (contactCell.getCellType()) {
                    case STRING:
                        contactValue = contactCell.getStringCellValue();
                        break;
                    case NUMERIC:
                        // 如果是数字类型，转换为字符串
                        contactValue = String.valueOf((long)contactCell.getNumericCellValue());
                        break;
                    case FORMULA:
                        // 如果是公式，尝试获取字符串值
                        try {
                            contactValue = contactCell.getStringCellValue();
                        } catch (IllegalStateException e) {
                            // 如果公式结果是数字，转换为字符串
                            contactValue = String.valueOf((long)contactCell.getNumericCellValue());
                        }
                        break;
                    default:
                        // 其他类型按空字符串处理
                        contactValue = "";
                }
                if (!contactValue.contains("-")) {
                    checkFailReason.append("customer contact值异常；");
                }
            } else {
                checkFailReason.append("customer contact值异常；");
            }

            // 校验email
            if (EmailValidator.getInstance().isValid(row.getCell(columnMap.get("email")).getStringCellValue())) {
                checkFailReason.append("email值异常；");
            }
            // 校验pickup date
            if (!isValidFormat(row.getCell(columnMap.get("pickup date")).getStringCellValue())) {
                checkFailReason.append("pickup date值异常；");
            }
            // 校验return date
            if (!isValidFormat(row.getCell(columnMap.get("return date")).getStringCellValue())) {
                checkFailReason.append("return date值异常；");
            }


            // 校验prepay amount（after markup）
            if (!(row.getCell(columnMap.get("prepay amount（after markup）")).getStringCellValue()).contains(".")) {
                checkFailReason.append("prepay amount（after markup）值异常；");
            }
            // 校验pay on arrival（after markup）
            if (!(row.getCell(columnMap.get("pay on arrival（after markup）")).getStringCellValue()).contains(".")) {
                checkFailReason.append("pay on arrival（after markup）值异常；");
            }
            // 校验total amount（after markup）
            if (!(row.getCell(columnMap.get("total amount（after markup）")).getStringCellValue()).contains(".")) {
                checkFailReason.append("total amount（after markup）值异常；");
            }
            // 校验currency code（after markup）
            if (null == row.getCell(columnMap.get("currency code（after markup）")) ||
                    null == row.getCell(columnMap.get("currency code（after markup）")).getStringCellValue() ||
                    row.getCell(columnMap.get("currency code（after markup）")).getStringCellValue().isEmpty()) {
                checkFailReason.append("currency code（after markup）值异常；");
            }
            // 校验order status
            if (null == row.getCell(columnMap.get("order status")) || null == row.getCell(columnMap.get("order status")).getStringCellValue() ||
                    row.getCell(columnMap.get("order status")).getStringCellValue().isEmpty()
                    || !row.getCell(columnMap.get("order status")).getStringCellValue().equals("已确认")) {
                checkFailReason.append("order status值异常；");
            }
            // 校验sipp code
            if ((row.getCell(columnMap.get("SIPP Code")).getStringCellValue()).length() != 4) {
                checkFailReason.append("SIPP Code值异常；");
            }
            // 校验Pick-up Branch Name
            StoreQueryReq storeQueryReq = new StoreQueryReq();
            storeQueryReq.setName(row.getCell(columnMap.get("Pick-up Branch Name")).getStringCellValue());
            ApiResponse<StoreResp> pickUpStore = ordersrvFeignClient.storeSearch(storeQueryReq);
            if (!pickUpStore.isSuccess() || ObjectUtils.isEmpty(pickUpStore.getResult())) {
                checkFailReason.append("Pick-up Branch Name值异常；");
            } else {
                Vendor vendor = vendorRepository.selectById(pickUpStore.getResult().getVendorId());
                if (null == vendor || !vendor.getActive()) {
                    checkFailReason.append("Pick-up Branch Name值异常，供应商已下线");
                }
            }
            // 校验Drop-off Branch Name
            storeQueryReq.setName(row.getCell(columnMap.get("Drop-off Branch Name")).getStringCellValue());
            ApiResponse<StoreResp> returnStore = ordersrvFeignClient.storeSearch(storeQueryReq);
            if (!returnStore.isSuccess() || ObjectUtils.isEmpty(returnStore.getResult())) {
                checkFailReason.append("Drop-off Branch Name值异常；");
            } else {
                Vendor vendor = vendorRepository.selectById(returnStore.getResult().getVendorId());
                if (null == vendor || !vendor.getActive()) {
                    checkFailReason.append("Drop-off Branch Name值异常，供应商已下线");
                }
            }
            // 校验Settlement Amount
//        JipinzucheStorePO jipinzucheStorePO1 = jipinzucheStoreMapper.selectByName(row.getCell(columnMap.get("Drop-off Branch Name")).getStringCellValue());
            if (!row.getCell(columnMap.get("Settlement Amount")).getStringCellValue().contains("USD")) {
                checkFailReason.append("Settlement Amount值异常；");
            }

            // 校验vehicle
            if (!(row.getCell(columnMap.get("vehicle")).getStringCellValue()).contains("-")
                    || !(row.getCell(columnMap.get("vehicle")).getStringCellValue()).contains("动")) {
                checkFailReason.append("vehicle值异常；");
            } else {
                String vehicle = row.getCell(columnMap.get("vehicle")).getStringCellValue();
                String modelName = "";
                String[] arr1 = vehicle.split("自动");
                if (arr1.length > 1) {
                    modelName = arr1[0].split("-")[1].trim();
                } else {
                    String[] arr2 = vehicle.split("手动");
                    modelName = arr2[0].split("-")[1].trim();
                }
                if (modelName.split(" ").length > 1) {
                    if (carBrands.containsKey(modelName.split(" ")[0])) {
                        modelName = carBrands.get(modelName.split(" ")[0]) + " " + modelName.split(" ")[1];
                    }
                }

                ApiResponse<CarModelPO> carModelPOApiResponse = productsrvFeignClient.velModelNameQuery(modelName);
                if (!carModelPOApiResponse.isSuccess() || carModelPOApiResponse.getError() != null) {
                    checkFailReason.append("vehicle不存在；");
                } else {
                    StockRangeReq storeQueryReq1 = new StockRangeReq();
                    if (pickUpStore.isSuccess() && !ObjectUtils.isEmpty(pickUpStore.getResult())) {
                        storeQueryReq1.setStoreId(pickUpStore.getResult().getId());
                        storeQueryReq1.setModelId(carModelPOApiResponse.getResult().getId());
                        ApiResponse<JipinzucheCarStockResp> jipinzucheCarStockApiResponse = ordersrvFeignClient.vehCarStock(storeQueryReq1);
                        if (null == jipinzucheCarStockApiResponse || null == jipinzucheCarStockApiResponse.getResult() || !jipinzucheCarStockApiResponse.isSuccess() || jipinzucheCarStockApiResponse.getError() != null) {
                            checkFailReason.append("车型stock不存在；");
                        } else {
                            CarStockInsuranceReq carStockInsuranceReq = new CarStockInsuranceReq();
                            carStockInsuranceReq.setStockId(Long.valueOf(jipinzucheCarStockApiResponse.getResult().getId()));
                            carStockInsuranceReq.setTitle(row.getCell(columnMap.get("Package Name")).getStringCellValue());
                            ApiResponse<CarStockInsuranceManagementModel> carStockInsuranceManagementModelApiResponse = productsrvFeignClient.velCarStockInsuranceTitleQuery(carStockInsuranceReq);
                            if (!carStockInsuranceManagementModelApiResponse.isSuccess()
                                    || carStockInsuranceManagementModelApiResponse.getError() != null
                                    || carStockInsuranceManagementModelApiResponse.getResult() == null) {
                                checkFailReason.append("车型保险package name不存在；");
                            }
                        }
                    }

                }
            }


            String sourceId = row.getCell(columnMap.get("order Id")).getStringCellValue();
            OrderQueryReq orderQueryReq = new OrderQueryReq();

            orderQueryReq.setSource(source);
            orderQueryReq.setSourceOrderId(sourceId);
            isOrderExist(sourceIdSet, checkFailReason, sourceId, orderQueryReq, ordersrvFeignClient);
            lineInfo.setSourceOrdercode(sourceId);
        }
        // 设置check结果状态
        lineInfo.setIsCheckSucceed(checkFailReason.isEmpty());
        lineInfo.setCheckFailReason(checkFailReason.toString());
    }

    public void isOrderExist(Set<String> sourceIdSet, StringBuilder checkFailReason, String sourceId, OrderQueryReq orderQueryReq, OrdersrvFeignClient ordersrvFeignClient) {
        JipinzucheCarOrderPO carOrder = carOrderService.selectBySourceAndSourceCode(orderQueryReq);
        boolean isExistingInDb = false;
        if (null != carOrder && null != carOrder.getId()) {
            isExistingInDb = true;
        }
        boolean isDuplicateInFile = sourceIdSet.contains(sourceId);

        if (isExistingInDb || isDuplicateInFile) {
            if (isExistingInDb) {
                checkFailReason.append("订单号已存在库中；");
            }
            if (isDuplicateInFile) {
                checkFailReason.append("文件存在重复订单号；");
            }
        } else {
            sourceIdSet.add(sourceId); // 无重复则加入集合
        }
    }


    // 时间格式校验
    public static boolean isValidFormat(String dateStr) {
        try {
            LocalDateTime parse = LocalDateTime.parse(dateStr, FORMATTER);
            LocalDateTime now = LocalDateTime.now();

            // 检查时间是否大于当前时间
            return parse.isAfter(now);
        } catch (DateTimeParseException e) {
            return false;
        }
    }


}
