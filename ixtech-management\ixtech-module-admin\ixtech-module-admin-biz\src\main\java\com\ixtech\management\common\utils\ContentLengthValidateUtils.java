package com.ixtech.management.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符长度内容校验器
 *
 * <AUTHOR> hu
 * @date 2025/4/5 00:17
 */
public class ContentLengthValidateUtils {

    /**
     * 内容只能为 字母、数字、空格、下划线、汉字的正则表达式
     */
    private static final Pattern NORMAL_CHAR_PATTERN = Pattern.compile("^[（）()a-zA-Z0-9 _\\u4e00-\\u9fa5]+$");

    /**
     * 内容允许的字符：汉字、字母、·、.、-、空格、'
     */
    private static final Pattern ALLOWED_USER_NAME_CHARS_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z·\\-. ']+$");

    /**
     * 供应商编码内容只允许字母、数字、空格
     */
    private static final Pattern CHANNEL_CODE_REGEX = Pattern.compile("^[a-zA-Z0-9 ]+$");

    /**
     * 供应商编码内容只允许字母、数字、空格
     */
    private static final Pattern PHONE_CODE_REGEX = Pattern.compile("^[0-9-]+$");

    private static final int CHANNEL_CODE_MAX_LENGTH = 20;

    /**
     * - 字符长度：minLength-maxLength 个字符（1 汉字=2 字符）
     * - 小于 minLength 个，提示：请输入至少 minLength/2 个汉字或者 minLength 个英文字母、数字、下划线；
     * - 大于 maxLength 个，提示：支持最大输入 maxLength/2 个汉字或者 maxLength 个英文字母、数字、下划线；
     * - 输入内容包含特殊符号（非字母、数字、汉字）提示：不能输入特殊字符；
     *
     * @param input     输入字符串
     * @param minLength
     * @param maxLength
     * @return （null表示通过，否则返回错误信息）
     */
    public static String validate(String input, int minLength, int maxLength) {

        // 检查是否为null或空字符串
        if (input == null || input.isEmpty()) {
            return "请输入内容";
        }

        // 校验特殊字符
        if (!isValidCharacters(input)) {
            return "不能输入特殊字符";
        }

        // 计算字符长度
        int calculatedLength = calculateLength(input);

        // 校验长度范围
        if (calculatedLength < minLength) {
            return "请输入至少 " + minLength / 2 + " 个汉字或者 " + minLength + " 个英文字母、数字、空格，中英文括号及下划线";
        } else if (calculatedLength > maxLength) {
            return "支持最大输入 " + maxLength / 2 + " 个汉字或者 " + maxLength + " 个英文字母、数字、空格，中英文括号及下划线";
        }

        // 校验通过
        return null;
    }

    /**
     * 校验是否只包含允许的字符（汉字、字母、数字、下划线）
     *
     * @param input
     * @return
     */
    private static boolean isValidCharacters(String input) {
        Matcher matcher = NORMAL_CHAR_PATTERN.matcher(input);
        return matcher.find();
    }

    /**
     * 计算字符长度（汉字算2个，其他算1个）
     *
     * @param input
     * @return
     */
    private static int calculateLength(String input) {
        int length = 0;
        for (char c : input.toCharArray()) {
            if (isChineseCharacter(c)) {
                length += 2;
            } else {
                length += 1;
            }
        }
        return length;
    }

    /**
     * 判断是否为汉字（基本Unicode范围）
     *
     * @param c
     * @return
     */
    private static boolean isChineseCharacter(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;
    }

    /**
     * 校验姓名字段
     * 支持汉字、字母、间隔号“·”、下脚点“.”、连字符“-”、空格、单引号“'”。字符长度4-64个字符（1 汉字=2 字符）
     * 仅支持汉字、字母、间隔号(·)、下脚点(.)、连字符(-)、空格、单引号(')
     *
     * @param input 输入姓名
     * @return 校验结果（null表示通过，否则返回错误信息）
     */
    public static String validateUsername(String input) {

        // 非必填字段，空值直接通过
        if (StringUtils.isEmpty(input)) {
            return null;
        }

        // 1. 校验非法字符
        if (!ALLOWED_USER_NAME_CHARS_PATTERN.matcher(input).matches()) {
            return "仅支持汉字、字母、间隔号(·)、下脚点(.)、连字符(-)、空格、单引号(')";
        }

        // 2. 计算有效长度（汉字算2个字符，其他算1个）
        int effectiveLength = calculateLength(input);

        // 3. 长度校验
        if (effectiveLength < 4) {
            return "至少输入2个汉字或4个英文字母、间隔号(·)、下脚点(.)、连字符(-)、空格、单引号(')";
        } else if (effectiveLength > 64) {
            return "最大输入32个汉字或64个英文字母、间隔号(·)、下脚点(.)、连字符(-)、空格、单引号(')";
        }

        // 校验通过
        return null;
    }

    /**
     * 校验联系电话字段
     *
     * @param phoneNumber 输入电话
     * @return 校验结果（null表示通过，否则返回错误信息）
     */
    public static String validatePhoneNumber(String phoneNumber) {
        // 非必填字段，空值直接通过
        if (StringUtils.isEmpty(phoneNumber)) {
            return null;
        }

        // 1. 校验是否为纯数字
        if (!StringUtils.isNumeric(phoneNumber)) {
            return "仅支持输入数字";
        }

        // 2. 校验长度
        if (phoneNumber.length() > 16) {
            return "最大输入16位数字";
        }

        // 校验通过
        return null;
    }

    /**
     * 校验联系电话字段
     *
     * @param phoneNumberCode 输入电话区号
     * @return 校验结果（null表示通过，否则返回错误信息）
     */
    public static String validatePhoneNumberCode(String phoneNumberCode) {
        // 非必填字段，空值直接通过
        if (StringUtils.isEmpty(phoneNumberCode)) {
            return null;
        }

        // 检查字符类型
        if (!PHONE_CODE_REGEX.matcher(phoneNumberCode).matches()) {
            return "只支持输入数字、“-”";
        }

        // 检查长度
        if (phoneNumberCode.length() > 7) {
            return "支持最大输入7个数字、“-”";
        }

        // 校验通过
        return null;
    }

    /**
     * 验证供应商编码是否合法
     *
     * @param supplierCode 待验证的供应商编码
     * @return 校验结果（null表示通过，否则返回错误信息）
     */
    public static String validateSupplierCode(String supplierCode) {

        // 检查是否为null或空字符串
        if (StringUtils.isEmpty(supplierCode)) {
            return "请输入内容";
        }

        // 检查长度
        if (supplierCode.length() > CHANNEL_CODE_MAX_LENGTH) {
            return "支持最大输入" + CHANNEL_CODE_MAX_LENGTH + "个英文字母和数字";
        }

        // 检查字符类型
        if (!CHANNEL_CODE_REGEX.matcher(supplierCode).matches()) {
            return "只支持英文字母、数字、空格";
        }

        return null;
    }

}
