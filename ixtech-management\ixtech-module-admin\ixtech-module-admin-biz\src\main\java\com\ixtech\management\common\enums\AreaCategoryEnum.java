package com.ixtech.management.common.enums;

import lombok.Getter;

/**
 * 地区类别
 *
 * <AUTHOR> hu
 * @date 2025/4/9 11:18
 */
@Getter
public enum AreaCategoryEnum implements BaseEnum<Integer> {

    COUNTRY(1, "国家"),
    PROVINCE(2, "省份"),
    CITY(3, "城市"),
    DISTRICT(4, "区县"),
    // STREET(5, "街道"),
    ;

    private final Integer code;
    private final String name;

    AreaCategoryEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return asString();
    }

}
