package com.ixtech.management.common.utils;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

@MappedTypes({LocalDateTime.class})
@MappedJdbcTypes({JdbcType.INTEGER})
public class IntToLocalDateTimeTypeHandler extends BaseTypeHandler<LocalDateTime> {
    private static final ZoneId ZONE_ID = ZoneId.systemDefault();

    public IntToLocalDateTimeTypeHandler() {
    }

    public void setNonNullParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType) throws SQLException {
        long timestamp = parameter.atZone(ZONE_ID).toEpochSecond();
        ps.setInt(i, (int)timestamp);
    }

    public LocalDateTime getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int timestamp = rs.getInt(columnName);
        return timestamp == 0 ? null : LocalDateTime.ofInstant(Instant.ofEpochSecond((long)timestamp), ZONE_ID);
    }

    public LocalDateTime getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int timestamp = rs.getInt(columnIndex);
        return timestamp == 0 ? null : LocalDateTime.ofInstant(Instant.ofEpochSecond((long)timestamp), ZONE_ID);
    }

    public LocalDateTime getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int timestamp = cs.getInt(columnIndex);
        return timestamp == 0 ? null : LocalDateTime.ofInstant(Instant.ofEpochSecond((long)timestamp), ZONE_ID);
    }
}