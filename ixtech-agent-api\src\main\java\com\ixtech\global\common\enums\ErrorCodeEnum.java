package com.ixtech.global.common.enums;

import com.ixtech.global.common.exception.inf.ErrorCode;

/**
 * agent服务错误码枚举
 */
public enum ErrorCodeEnum implements ErrorCode {
    // 参数错误 (ERROR_TYPE_PARAM)

    // 业务错误 (ERROR_TYPE_BUSINESS)
    ;

    private final String errorType;
    private final String specificCode;
    private final String message;

    ErrorCodeEnum(String errorType, String specificCode, String message) {
        this.errorType = errorType;
        this.specificCode = specificCode;
        this.message = message;
    }

    @Override
    public String getErrorType() {
        return errorType;
    }

    @Override
    public String getSpecificCode() {
        return specificCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getFullCode() {
        return getServiceCode() + errorType + specificCode;
    }

    @Override
    public ErrorCode fromFullCode(String fullCode) {
        if (fullCode == null || fullCode.length() != 9 || !fullCode.startsWith(ErrorCode.SERVICE_CODE)) {
            return null;
        }
        for (ErrorCodeEnum errorCode : ErrorCodeEnum.values()) {
            if (errorCode.getFullCode().equals(fullCode)) {
                return errorCode;
            }
        }
        return null;
    }
}
