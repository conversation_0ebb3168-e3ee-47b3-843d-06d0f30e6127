package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 商品、设备、服务
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GoodsAndEquipmentAndServiceResp {

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 项目描述
     */
    private String projectDes;

    /**
     * 关联商品
     */
    private String relatedProduct;
    private String unitPrice;
    private String rateCode;
    private String quantity; // 数量

    private BigDecimal amount; // 金额
    private BigDecimal taxRate; // 税率
    private BigDecimal taxAmount; // 税额
    private BigDecimal discount; // 折扣值
    private BigDecimal refundAmount; // 退还额
//    private BigDecimal receivableAmount; // 应收金额
//    private BigDecimal receivedAmount; // 实收金额
    private String paymentMethod;
}
