package com.ixtech.global;

import com.ixtech.global.cache.I18nTranslationCacheManager;
import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.constant.I18nConstants;
import com.ixtech.global.feign.client.TranslationFeignClient;
import com.ixtech.global.feign.req.TranslationReq;
import com.ixtech.global.feign.resp.TranslationResp;
import com.ixtech.global.interceptor.LanguageContextHolder;
import com.ixtech.global.util.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 翻译服务
 *
 * <AUTHOR> hu
 * @date 2025/6/8 15:55
 */
@Slf4j
public class I18nTranslationExecutor {

    private I18nTranslationCacheManager i18nTranslationCacheManager;
    private TranslationFeignClient translationFeignClient;

    public I18nTranslationExecutor(I18nTranslationCacheManager i18nTranslationCacheManager, TranslationFeignClient translationFeignClient) {
        this.i18nTranslationCacheManager = i18nTranslationCacheManager;
        this.translationFeignClient = translationFeignClient;
    }

    /**
     * 获取翻译资源key在指定语言下的翻译内容
     *
     * @param resourceKey 翻译资源key
     * @return
     */

    public String getTranslation(String resourceKey) {
        // 获取语言code
        String languageCode = Optional.ofNullable(LanguageContextHolder.getPrimaryLanguage())
                .map(RequestUtils.LanguagePriority::languageCode).orElse(null);
        if (StringUtils.isBlank(languageCode)) {
            return null;
        }
        return getTranslation(resourceKey, languageCode, true);
    }

    /**
     * 获取翻译资源key在指定语言下的翻译内容
     *
     * @param resourceKeys 翻译资源key
     * @return
     */

    public Map<String, String> getTranslations(Collection<String> resourceKeys) {
        // 获取语言code
        String languageCode = Optional.ofNullable(LanguageContextHolder.getPrimaryLanguage())
                .map(RequestUtils.LanguagePriority::languageCode).orElse(null);
        if (StringUtils.isBlank(languageCode)) {
            return null;
        }
        return getTranslations(resourceKeys, languageCode, true);
    }

    /**
     * 获取翻译资源key在指定语言下的翻译内容
     *
     * @param resourceKey  翻译资源key
     * @param languageCode 语言code
     * @return
     */
    public String getTranslation(String resourceKey, String languageCode) {
        return getTranslation(resourceKey, languageCode, true);
    }

    /**
     * 获取翻译资源key在指定语言下的翻译内容
     *
     * @param resourceKey   翻译资源key
     * @param languageCode  语言code
     * @param useLocalCache 是否使用本地缓存 true：使用 false：不使用
     * @return
     */
    public String getTranslation(String resourceKey, String languageCode, boolean useLocalCache) {
        Map<String, String> translations = getTranslations(Collections.singletonList(resourceKey), languageCode, useLocalCache);
        return translations.get(resourceKey);
    }

    /**
     * 批量获取翻译资源key在指定语言下的翻译内容
     *
     * @param resourceKeys 翻译资源key集合
     * @param languageCode 语言code
     * @return
     */
    public Map<String, String> getTranslations(Collection<String> resourceKeys, String languageCode) {
        return getTranslations(resourceKeys, languageCode, true);
    }

    /**
     * 批量获取翻译资源key在指定语言下的翻译内容
     *
     * @param resourceKeys  翻译资源key集合
     * @param languageCode  语言code
     * @param useLocalCache 是否使用本地缓存 true：使用 false：不使用
     * @return
     */
    public Map<String, String> getTranslations(Collection<String> resourceKeys, String languageCode, boolean useLocalCache) {
        return getTranslations(resourceKeys, languageCode, useLocalCache, false, null);
    }

    /**
     * 异步批量获取翻译资源key在指定语言下的翻译内容
     *
     * @param resourceKeys  翻译资源key集合
     * @param languageCode  语言code
     * @param useLocalCache 是否使用本地缓存 true：使用 false：不使用
     * @param executor      线程池执行器
     * @return
     */
    public Map<String, String> getTranslationsAsync(Collection<String> resourceKeys, String languageCode, boolean useLocalCache, Executor executor) {
        return getTranslations(resourceKeys, languageCode, useLocalCache, true, executor);
    }

    /**
     * 批量获取翻译资源key在指定语言下的翻译内容
     *
     * @param resourceKeys  翻译资源key集合
     * @param languageCode  语言code
     * @param useLocalCache 是否使用本地缓存 true：使用 false：不使用
     * @param async         是否异步 true：异步 false：同步
     * @param executor      线程池执行器
     * @return
     */
    private Map<String, String> getTranslations(Collection<String> resourceKeys, String languageCode, boolean useLocalCache, boolean async, Executor executor) {

        if (CollectionUtils.isEmpty(resourceKeys)) {
            return new HashMap<>();
        }

        // 对需翻译资源key进行数据去重
        List<String> distinctKeys = resourceKeys.stream().distinct().toList();
        // 拆分大数据集，分批处理
        List<List<String>> batches = ListUtils.partition(distinctKeys, I18nConstants.BATCH_KEY_SIZE);

        // 批量获取翻译结果
        if (!async) {
            // 同步处理每个批次
            Map<String, String> result = new HashMap<>();
            for (Collection<String> subList : batches) {
                // 查询翻译
                Map<String, String> translationMap = doGetTranslations(subList, languageCode, useLocalCache);
                if (MapUtils.isNotEmpty(translationMap)) {
                    result.putAll(translationMap);
                }
            }
            return result;
        } else {
            // 并行处理每个批次
            List<CompletableFuture<Map<String, String>>> futures = batches.stream()
                    .map(batch ->
                            CompletableFuture.supplyAsync(() -> doGetTranslations(batch, languageCode, useLocalCache), executor)
                    ).toList();
            // 合并所有批次的结果
            return futures.stream().map(CompletableFuture::join)
                    .flatMap(map -> map.entrySet().stream())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            // 冲突时保留先出现的值
                            (existing, replacement) -> existing
                    ));
        }

    }

    /**
     * 从本地缓存和翻译服务中获取资源key对应的翻译文案
     *
     * @param resourceKeys  翻译资源key集合
     * @param languageCode  语言code
     * @param useLocalCache 是否使用本地缓存 true：使用 false：不使用
     * @return
     */
    private Map<String, String> doGetTranslations(Collection<String> resourceKeys, String languageCode, boolean useLocalCache) {

        if (CollectionUtils.isEmpty(resourceKeys)) {
            return null;
        }

        // 翻译结果集
        Map<String, String> result = new HashMap<>();
        // 未从缓存中获取到结果的key
        Set<String> unCachedResourceKeys = new HashSet<>(resourceKeys);

        // 从本地缓存中获取翻译结果
        if (useLocalCache) {
            Map<String, String> cachedTranslationMap = i18nTranslationCacheManager.get(resourceKeys, languageCode);
            if (MapUtils.isNotEmpty(cachedTranslationMap)) {
                result.putAll(cachedTranslationMap);
            }
            unCachedResourceKeys.removeAll(result.keySet());
        }

        // 调用翻译服务
        if (!unCachedResourceKeys.isEmpty()) {
            Map<String, String> noCachedTranslationMap = doGetTranslations(unCachedResourceKeys, languageCode);
            if (MapUtils.isNotEmpty(noCachedTranslationMap)) {
                result.putAll(noCachedTranslationMap);
                // 存入本地缓存
                if (useLocalCache) {
                    i18nTranslationCacheManager.put(noCachedTranslationMap, languageCode);
                }
            }
        }

        return result;
    }

    /**
     * 从翻译服务获取翻译资源key对应的翻译文案
     *
     * @param resourceKeys 翻译资源key集合
     * @param languageCode 语言code
     * @return
     */
    public Map<String, String> doGetTranslations(Collection<String> resourceKeys, String languageCode) {

        Map<String, String> translationMap = new HashMap<>();

        try {
            TranslationReq req = new TranslationReq();
            req.setResourceKeys(resourceKeys);
            req.setLanguageCode(languageCode);
            ApiResponse<TranslationResp> apiResponse = translationFeignClient.langTrans(req);
            if (apiResponse != null && apiResponse.isSuccess()) {
                return Optional.ofNullable(apiResponse.getResult()).map(TranslationResp::getTranslationResult)
                        .orElse(translationMap);
            }
        } catch (Exception e) {
            log.error("翻译服务调用失败", e);
        }

        return translationMap;
    }

}
