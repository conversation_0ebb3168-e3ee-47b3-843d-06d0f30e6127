package com.ixtech.global.domain.service.impl;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.utils.CollUtils;
import com.ixtech.global.domain.service.IVehicleService;
import com.ixtech.global.integration.internal.client.ProductsrvFeignClientV2;
import com.ixtech.global.integration.internal.req.ReferenceQueryReq;
import com.ixtech.global.integration.internal.req.VehicleAvailListQueryReq;
import com.ixtech.global.integration.internal.req.VehicleDetailQueryReq;
import com.ixtech.global.integration.internal.resp.ReferenceQueryResp;
import com.ixtech.global.integration.internal.resp.VehicleAvailQueryResp;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service("vehicleServiceV2")
public class IVehicleServiceV2Impl implements IVehicleService {

    //全局计数器-失败
//    private static final Counter QUERY_VEHICLE_FAILED_COUNTER = Counter
//            .builder("query_vehicle_failed_count")  // 指标名
//            .description("Total number of failed queryVehicleAvailList")  // 描述
//            .tag("result", "query_vehicle_avail_list_failed")          // 自定义标签
//            .register(Metrics.globalRegistry);  // 注册到全局Registry

    //全局计数器-无结果
//    private static final Counter QUERY_VEHICLE_NO_RESULT_COUNTER = Counter
//            .builder("query_vehicle_no_result_count")  // 指标名
//            .description("Total number of no result queryVehicleAvailList")  // 描述
//            .tag("result", "query_vehicle_avail_list_no_result")          // 自定义标签
//            .register(Metrics.globalRegistry);  // 注册到全局Registry


    @Resource
    private ProductsrvFeignClientV2 productsrvFeignClientV2;

    @Override
    public ApiResponse<VehicleAvailQueryResp> queryVehicleAvailList(VehicleAvailListQueryReq request) {
        ApiResponse<VehicleAvailQueryResp> response = productsrvFeignClientV2.vehicleList(request);
        return response;
    }

    @Override
    public ApiResponse<VehicleAvailQueryResp> queryVehicleAvailDetail(VehicleDetailQueryReq request) {
        return productsrvFeignClientV2.vehicleDetail(request);
    }

    @Override
    public ApiResponse<ReferenceQueryResp> queryVehicleByReferenceId(ReferenceQueryReq request) {
        return productsrvFeignClientV2.refQuery(request);
    }
}
