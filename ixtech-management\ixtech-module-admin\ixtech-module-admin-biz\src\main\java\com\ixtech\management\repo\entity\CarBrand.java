package com.ixtech.management.repo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆品牌实体
 *
 * <AUTHOR> hu
 * @date 2025/4/12 17:43
 */
@Data
@NoArgsConstructor
public class CarBrand extends BaseEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 所属国家
     */
    private String country;

    /**
     * 品牌logo
     */
    private String logo;

    /**
     * 添加时间
     */
    private Integer time;

    /**
     * 添加ip
     */
    private String ip;

    /**
     * 添加人
     */
    private Integer mid;

    /**
     * 1->有效；-1->删除
     */
    private Integer status;

}