package com.ixtech.management.common.validator;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.config.ExcelHeaderCheckException;
import com.ixtech.management.domain.enums.InsuranceEnum;
import com.ixtech.management.integration.internal.client.OrdersrvFeignClient;
import com.ixtech.management.integration.internal.client.ProductsrvFeignClient;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.*;
import com.ixtech.management.repo.entity.CarModelPO;
import com.ixtech.management.repo.entity.JipinzucheCarStock;
import jakarta.annotation.Resource;
import org.apache.commons.validator.routines.EmailValidator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@Component
@ValidatorFor("租租车")
public class ZuZuCheExcelHeaderValidator implements ExcelHeaderValidator {

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(PATTERN);


    @Resource
    private OrdersrvFeignClient ordersrvFeignClient;

    @Resource
    private ProductsrvFeignClient productsrvFeignClient;

    private static final List<String> REQUIRED_HEADERS =
            List.of("Reservation No.", "Book Date", "Driver Name",
                    "customer contact", "email", "Pick Up Date",
                    "Drop Off Date", "Car Type", "Pre Paid Amount",
                    "pay on arrival（after markup）", "Total Amount",
                    "currency code（after markup）", "Package Name", "SIPP Code",
                    "Pick-up Branch Name", "Drop-off Branch Name", "Country of Origin", "Settlement Amount");

    private static Map<String, Integer> columnMustMap = new HashMap<>();
    private static Map<String, Integer> columnMap = new HashMap<>();


    static {
        columnMap.put("Reservation No.", 0);  // 1-1=0
        columnMap.put("Book Date", 1);  // 2-1=1
        columnMap.put("confirm number", 4);  // 5-1=4
        columnMap.put("Driver Name", 6);  // 7-1=6
        columnMap.put("customer contact", 7);  // 8-1=7
        columnMap.put("email", 8);  // 9-1=8
        columnMap.put("flight number", 9);  // 10-1=9
        columnMap.put("Pick Up Date", 14);  // 15-1=14
        columnMap.put("Drop Off Date", 15);  // 16-1=15
        columnMap.put("Car Type", 19);  // 20-1=19
        columnMap.put("Pre Paid Amount", 21);  // 22-1=21
        columnMap.put("pay on arrival（after markup）", 22);  // 23-1=22
        columnMap.put("Total Amount", 23);  // 24-1=23
        columnMap.put("currency code（after markup）", 24);  // 25-1=24
        columnMap.put("Order Status", 25);
        columnMap.put("remark", 26);  // 27-1=26
        columnMap.put("One way fee", 31);  // 32-1=31
        columnMap.put("currency", 32);  // 33-1=32
        columnMap.put("Payment method", 33);  // 34-1=33
        columnMap.put("Special opening hours fee for pickup", 34);  // 35-1=34
        columnMap.put("currency", 35);  // 36-1=35
        columnMap.put("Payment method", 36);  // 37-1=36
        columnMap.put("Special opening hours fee for dropoff", 37);  // 38-1=37
        columnMap.put("Optional service", 40);  // 41-1=40
        columnMap.put("Optional service price", 41);  // 42-1=41
        columnMap.put("currency", 42);  // 43-1=42
        columnMap.put("Payment method", 43);  // 44-1=43
        columnMap.put("Package Name", 46);  // 47-1=46
        columnMap.put("SIPP Code", 50);  // 51-1=50
        columnMap.put("Pick-up Branch Name", 55);  // 56-1=55
        columnMap.put("Drop-off Branch Name", 58);  // 59-1=58
        columnMap.put("Country of Origin", 59);  // 60-1=59
        columnMap.put("Settlement Amount", 60);  // 61-1=60
        columnMap.put("Platform of Order", 63);  // 64-1=63
        columnMap.put("Purchase Platform Insurance", 69);  // 70-1=69
    }

    static {
        columnMustMap.put("Reservation No.", 0);  // 1-1=0
        columnMustMap.put("Book Date", 1);  // 2-1=1
//        columnMustMap.put("confirm number", 4);  // 5-1=4
        columnMustMap.put("Driver Name", 6);  // 7-1=6
        columnMustMap.put("customer contact", 7);  // 8-1=7
        columnMustMap.put("email", 8);  // 9-1=8
//        columnMap.put("flight number", 9);  // 10-1=9
        columnMustMap.put("Pick Up Date", 14);  // 15-1=14
        columnMustMap.put("Drop Off Date", 15);  // 16-1=15
        columnMustMap.put("Car Type", 19);  // 20-1=19
        columnMustMap.put("Pre Paid Amount", 21);  // 22-1=21
        columnMustMap.put("pay on arrival（after markup）", 22);  // 23-1=22
        columnMustMap.put("Total Amount", 23);  // 24-1=23
        columnMustMap.put("currency code（after markup）", 24);  // 25-1=24
        columnMustMap.put("Order Status", 25);  // 25-1=24
//        columnMap.put("remark", 26);  // 27-1=26
//        columnMap.put("One way fee", 31);  // 32-1=31
//        columnMap.put("currency", 32);  // 33-1=32
//        columnMap.put("Payment method", 33);  // 34-1=33
//        columnMap.put("Special opening hours fee for pickup", 34);  // 35-1=34
//        columnMap.put("currency", 35);  // 36-1=35
//        columnMap.put("Payment method", 36);  // 37-1=36
//        columnMap.put("Special opening hours fee for dropoff", 37);  // 38-1=37
//        columnMap.put("Optional service", 40);  // 41-1=40
//        columnMap.put("Optional service price", 41);  // 42-1=41
//        columnMap.put("currency", 42);  // 43-1=42
//        columnMap.put("Payment method", 43);  // 44-1=43
        columnMustMap.put("Package Name", 46);  // 47-1=46
        columnMustMap.put("SIPP Code", 50);  // 51-1=50
        columnMustMap.put("Pick-up Branch Name", 55);  // 56-1=55
        columnMustMap.put("Drop-off Branch Name", 58);  // 59-1=58
        columnMustMap.put("Country of Origin", 59);  // 60-1=59
        columnMustMap.put("Settlement Amount", 60);  // 61-1=60
//        columnMustMap.put("Platform of Order", 63);  // 64-1=63
//        columnMap.put("Purchase Platform Insurance", 69);
    }

    public static Map<String, String> carBrands = new HashMap<String, String>() {{
        put("雷诺", "Renault");
        put("斯巴鲁", "SUBARU");
        put("霍顿", "Holden");
        put("吉普", "Jeep");
        put("铃木", "Suzuki");
        put("Kia", "Kia");
        put("起亚", "Kia");
        put("西雅特", "Seat");
        put("雷克萨斯", "LeXuS");
        put("现代", "Hyundai");
        put("雪铁龙", "Citroen");
        put("奥迪", "Audi");
        put("捷豹", "Jaguar");
        put("斯柯达", "Skoda");
        put("沃尔沃", "Volvo");
        put("福特", "Ford");
        put("三菱", "Mitsubishi");
        put("Opel", "Opel");
        put("欧宝", "Opel");
        put("宝马", "BMW");
        put("Volkswagen", "Volkswagen");
        put("大众", "Volkswagen");
        put("日产", "Nissan");
        put("本田", "Honda");
        put("奔驰", "Benz");
        put("丰田", "Toyota");
    }};


    @Override
    public void validateHeaders(List<String> headers) throws ExcelHeaderCheckException {
        columnMustMap.forEach((key, value) -> {
            if (!headers.contains(key) || headers.size() != 83 || !headers.get(value).equals(key)) {
                throw new ExcelHeaderCheckException("选择的渠道与导入的 excel 字段格式不对应，请确认选择的渠道和上传的 excel 文件");
            }
        });

    }

    @Override
    public void validateRowData(Integer source, Set<String> sourceIdSet, Row row, CarOrderImportRespVO.CarOrderCheckOrImportResult lineInfo) throws ExcelHeaderCheckException {

        StringBuilder checkFailReason = new StringBuilder();
        columnMap.forEach((info, rowNum) -> {
            Cell orderIdCell = row.getCell(rowNum);
            if (orderIdCell == null || orderIdCell.getCellType() == CellType.BLANK
                    || (orderIdCell.getCellType() == CellType.STRING && orderIdCell.getStringCellValue().trim().isEmpty())) {
                if (columnMustMap.containsKey(info)) {
                    // 处理空值情况
                    checkFailReason.append(info).append("不能为空；");
                }
            }
        });

        if (checkFailReason.length() == 0) {

            // 校验Book Date
            if (!isValidFormat(row.getCell(columnMap.get("Book Date")).getStringCellValue())) {
                checkFailReason.append("Book Date值异常；");
            }
            ;
            // 校验email
            if (EmailValidator.getInstance().isValid(row.getCell(columnMap.get("email")).getStringCellValue())) {
                checkFailReason.append("email值异常；");
            }
            // 校验Pick Up Date
            if (!isValidFormat(row.getCell(columnMap.get("Pick Up Date")).getStringCellValue())) {
                checkFailReason.append("Pick Up Date值异常；");
            }
            // 校验Drop Off Date
            if (!isValidFormat(row.getCell(columnMap.get("Drop Off Date")).getStringCellValue())) {
                checkFailReason.append("Drop Off Date值异常；");
            }


            // 校验Pre Paid Amount
            if (!(row.getCell(columnMap.get("Pre Paid Amount")).getStringCellValue()).contains(".")) {
                checkFailReason.append("Pre Paid Amount值异常；");
            }
            // 校验pay on arrival（after markup）
            if (!(row.getCell(columnMap.get("pay on arrival（after markup）")).getStringCellValue()).contains(".")) {
                checkFailReason.append("pay on arrival（after markup）值异常；");
            }
            // 校验Total Amount
            if (!(row.getCell(columnMap.get("Total Amount")).getStringCellValue()).contains(".")) {
                checkFailReason.append("Total Amount值异常；");
            }
            // 校验currency code（after markup）
            if (null == row.getCell(columnMap.get("currency code（after markup）")) ||
                    null == row.getCell(columnMap.get("currency code（after markup）")).getStringCellValue() ||
                    row.getCell(columnMap.get("currency code（after markup）")).getStringCellValue().isEmpty()) {
                checkFailReason.append("currency code（after markup）值异常；");
            }
            // 校验Order Status
            if (null == row.getCell(columnMap.get("Order Status")) || null == row.getCell(columnMap.get("Order Status")).getStringCellValue() ||
                    row.getCell(columnMap.get("Order Status")).getStringCellValue().isEmpty()
                    || !row.getCell(columnMap.get("Order Status")).getStringCellValue().equals("已确认")) {
                checkFailReason.append("Order Status值异常；");
            }
            // 校验Package Name
//            if (!InsuranceEnum.containsName(row.getCell(columnMap.get("Package Name")).getStringCellValue())) {
//                checkFailReason.append("Package Name值异常；");
//            }
            // 校验sipp code
            if ((row.getCell(columnMap.get("SIPP Code")).getStringCellValue()).length() != 4) {
                checkFailReason.append("SIPP Code值异常；");
            }
            // 校验Pick-up Branch Name
            StoreQueryReq storeQueryReq = new StoreQueryReq();
            storeQueryReq.setName(row.getCell(columnMap.get("Pick-up Branch Name")).getStringCellValue());
            ApiResponse<StoreResp> pickUpStore = ordersrvFeignClient.storeSearch(storeQueryReq);
            if (!pickUpStore.isSuccess() || ObjectUtils.isEmpty(pickUpStore.getResult())) {
                checkFailReason.append("Pick-up Branch Name值异常；");
            }
            // 校验Drop-off Branch Name
            storeQueryReq.setName(row.getCell(columnMap.get("Drop-off Branch Name")).getStringCellValue());
            ApiResponse<StoreResp> returnStore = ordersrvFeignClient.storeSearch(storeQueryReq);
            if (!returnStore.isSuccess() || ObjectUtils.isEmpty(returnStore.getResult())) {
                checkFailReason.append("Drop-off Branch Name值异常；");
            }
            // 校验Settlement Amount
//        JipinzucheStorePO jipinzucheStorePO1 = jipinzucheStoreMapper.selectByName(row.getCell(columnMap.get("Drop-off Branch Name")).getStringCellValue());
            if (!row.getCell(columnMap.get("Settlement Amount")).getStringCellValue().contains("USD")) {
                checkFailReason.append("Settlement Amount值异常；");
            }

            // 校验Car Type
            if (!(row.getCell(columnMap.get("Car Type")).getStringCellValue()).contains("-")
                    || !(row.getCell(columnMap.get("Car Type")).getStringCellValue()).contains("动")) {
                checkFailReason.append("Car Type值异常；");
            } else {
                String vehicle = row.getCell(columnMap.get("Car Type")).getStringCellValue();
                String modelName = "";
                String[] arr1 = vehicle.split("自动");
                if (arr1.length > 1) {
                    modelName = arr1[0].split("-")[1].trim();
                } else {
                    String[] arr2 = vehicle.split("手动");
                    modelName = arr2[0].split("-")[1].trim();
                }
                if (modelName.split(" ").length > 1) {
                    if (carBrands.containsKey(modelName.split(" ")[0])) {
                        modelName = carBrands.get(modelName.split(" ")[0]) + " " + modelName.split(" ")[1];
                    }
                }
                ApiResponse<CarModelPO> carModelPOApiResponse = productsrvFeignClient.velModelNameQuery(modelName);
                if (!carModelPOApiResponse.isSuccess() || carModelPOApiResponse.getError() != null) {
                    checkFailReason.append("Car Type不存在；");
                } else {
                    StockRangeReq storeQueryReq1 = new StockRangeReq();
                    if (pickUpStore.isSuccess() && !ObjectUtils.isEmpty(pickUpStore.getResult())) {
                        storeQueryReq1.setStoreId(pickUpStore.getResult().getId());
                        storeQueryReq1.setModelId(carModelPOApiResponse.getResult().getId());
                        ApiResponse<JipinzucheCarStockResp> jipinzucheCarStockApiResponse = ordersrvFeignClient.vehCarStock(storeQueryReq1);
                        if (null == jipinzucheCarStockApiResponse || null == jipinzucheCarStockApiResponse.getResult() || !jipinzucheCarStockApiResponse.isSuccess() || jipinzucheCarStockApiResponse.getError() != null) {
                            checkFailReason.append("车型stock不存在；");
                        }else {
                            CarStockInsuranceReq carStockInsuranceReq = new CarStockInsuranceReq();
                            carStockInsuranceReq.setStockId(Long.valueOf(jipinzucheCarStockApiResponse.getResult().getId()));
                            carStockInsuranceReq.setTitle(row.getCell(columnMap.get("Package Name")).getStringCellValue());
                            ApiResponse<CarStockInsuranceManagementModel> carStockInsuranceManagementModelApiResponse = productsrvFeignClient.velCarStockInsuranceTitleQuery(carStockInsuranceReq);
                            if (!carStockInsuranceManagementModelApiResponse.isSuccess()
                                    || carStockInsuranceManagementModelApiResponse.getError() != null
                                    || carStockInsuranceManagementModelApiResponse.getResult() == null) {
                                checkFailReason.append("车型保险package name不存在；");
                            }
                        }
                    }
                }
            }


            String sourceId = row.getCell(columnMap.get("Reservation No.")).getStringCellValue();
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setSource(source);
            orderQueryReq.setSourceOrderId(sourceId);
            isOrderExist(sourceIdSet, checkFailReason, sourceId, orderQueryReq, ordersrvFeignClient);
            lineInfo.setSourceOrdercode(sourceId);
        }
        // 设置check结果状态
        lineInfo.setIsCheckSucceed(checkFailReason.isEmpty());
        lineInfo.setCheckFailReason(checkFailReason.toString());
    }

    static void isOrderExist(Set<String> sourceIdSet, StringBuilder checkFailReason, String sourceId, OrderQueryReq orderQueryReq, OrdersrvFeignClient ordersrvFeignClient) {
        ApiResponse<RentalResp> rentalRespApiResponse = ordersrvFeignClient.vehResStatusSearch(orderQueryReq);
        boolean isExistingInDb = rentalRespApiResponse.isSuccess();
        boolean isDuplicateInFile = sourceIdSet.contains(sourceId);

        if (isExistingInDb || isDuplicateInFile) {
            if (isExistingInDb) {
                checkFailReason.append("订单号已存在库中；");
            }
            if (isDuplicateInFile) {
                checkFailReason.append("文件存在重复订单号；");
            }
        } else {
            sourceIdSet.add(sourceId); // 无重复则加入集合
        }
    }


    // 时间格式校验
    public static boolean isValidFormat(String dateStr) {
        try {
            LocalDateTime parse = LocalDateTime.parse(dateStr, FORMATTER);
            LocalDateTime now = LocalDateTime.now();

            // 检查时间是否大于当前时间
            return parse.isAfter(now);
        } catch (DateTimeParseException e) {
            return false;
        }
    }

}
