package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 接送机服务类型
 *
 * <AUTHOR> hu
 * @date 2025/7/20 18:35
 */
@Getter
public enum TransferServiceTypeEnum {

    PICK_IP(1, "接机"),
    DROP_OFF(2, "送机"),
    ;

    TransferServiceTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 服务类型code
     */
    private final Integer code;

    /**
     * 服务类型描述
     */
    private final String value;

}
