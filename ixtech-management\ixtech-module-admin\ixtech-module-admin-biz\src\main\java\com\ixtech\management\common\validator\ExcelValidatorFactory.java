package com.ixtech.management.common.validator;
import com.ixtech.management.common.exception.BizException;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.HashMap;

@Component
public class ExcelValidatorFactory {
    private final Map<String, ExcelHeaderValidator> validators;

    // 通过构造器注入所有ExcelValidator实现
    public ExcelValidatorFactory(Map<String, ExcelHeaderValidator> validatorMap) {
        this.validators = new HashMap<>();
        validatorMap.forEach((beanName, validator) -> {
            ValidatorFor annotation = validator.getClass().getAnnotation(ValidatorFor.class);
            if (annotation != null) {
                validators.put(annotation.value(), validator);
            }
        });
    }

    public ExcelHeaderValidator getValidator(String fileType) {
        ExcelHeaderValidator validator = validators.get(fileType);
        if (validator == null) {
            throw new BizException("渠道校验规则不存在: " + fileType);
        }
        return validator;
    }
}
