package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.Vendor;
import com.ixtech.management.repo.mapper.VendorMapper;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:23
 */
@Repository
public class VendorRepository {

    @Resource
    private VendorMapper vendorMapper;

    /**
     * 条件查询供应商信息
     *
     * @param id
     * @param name
     * @param companyFullName
     * @return
     */
    public List<Vendor> searchByCondition(Long id, String name, String companyFullName) {
        return vendorMapper.searchByCondition(id, name, companyFullName);
    }

    /**
     * 新增供应商
     *
     * @param vendor
     */
    public void insertSelective(Vendor vendor) {
        vendorMapper.insertSelective(vendor);
    }

    /**
     * 更新供应商
     *
     * @param vendor
     */
    public void updateByPrimaryKeySelective(Vendor vendor) {
        vendorMapper.updateByPrimaryKeySelective(vendor);
    }

    /**
     * 查询指定供应商的信息
     *
     * @param id 供应商id
     * @return
     */
    public Vendor selectById(Long id) {
        return vendorMapper.selectByPrimaryKey(id);
    }

    /**
     * 批量获取供应商信息
     *
     * @param vendorIds 供应商id列表
     * @return
     */
    public Map<Long, Vendor> selectByIds(Collection<Long> vendorIds) {
        if (CollectionUtils.isEmpty(vendorIds)) {
            return new HashMap<>();
        }
        List<Vendor> areaList = vendorMapper.selectByPrimaryKeyList(vendorIds);
        return CollectionUtils.emptyIfNull(areaList).stream()
                .collect(Collectors.toMap(Vendor::getId, Function.identity()));
    }

    /**
     * 查询所有供应商的信息
     *
     * @return
     */
    public List<Vendor> selectAll() {
        return vendorMapper.selectAll();
    }

}
