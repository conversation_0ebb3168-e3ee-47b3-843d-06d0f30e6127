package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.Vendor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 18:21
 */
@DS("ix")
@Mapper
public interface VendorMapper {

    int insert(Vendor record);

    int insertSelective(Vendor record);

    Vendor selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Vendor record);

    int updateByPrimaryKey(Vendor record);

    int updateBatch(List<Vendor> list);

    int updateBatchSelective(List<Vendor> list);

    int batchInsert(@Param("list") List<Vendor> list);

    /**
     * 批量查询供应商信息
     *
     * @param ids 供应商id列表
     * @return
     */
    List<Vendor> selectByPrimaryKeyList(@Param(value = "ids") Collection<Long> ids);

    /**
     * 条件查询供应商信息
     *
     * @param id              供应商id
     * @param name            供应商名称
     * @param companyFullName 公司全称
     * @return
     */
    List<Vendor> searchByCondition(@Param(value = "id") Long id,
                                   @Param(value = "name") String name,
                                   @Param(value = "companyFullName") String companyFullName
    );

    List<Vendor> selectAll();

}