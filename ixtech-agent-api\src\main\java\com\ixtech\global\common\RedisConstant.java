package com.ixtech.global.common;

/**
 * Redis key 全局变量
 *
 * @author: Phili
 * @date： 2025/3/27
 */
public class RedisConstant {


    /**
     * 缓存时间 3分钟
     */
    public static final int THREE_MINUTES_SECONDS = 180;

    /**
     * 缓存时间 5分钟
     */
    public static final int FIVE_MINUTES_SECONDS = 300;

    /**
     * 缓存时间 10分钟
     */
    public static final int TEN_MINUTES_SECONDS = 600;

    /**
     * 缓存时间 6小时
     */
    public static final int SIX_HOURS_SECONDS = 32400;

    /**
     * Redis Key 版本号
     */
    public static final String VERSION = "v2";
    /**
     * 订单来源查询缓存key
     */
    public static final String ORDER_SOURCE_LIST_KEY = VERSION + ":" + "ixtech:rental:channel:list";

}
