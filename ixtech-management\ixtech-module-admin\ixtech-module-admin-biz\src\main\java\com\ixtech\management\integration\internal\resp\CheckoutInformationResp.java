package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CheckoutInformationResp {

    // 还车里程
    private String checkoutMileage;
    // 实际还车时间
    private String actualCheckoutTime;
    // 预计还车时间
    private String estimatedCheckoutTime;
    // 还车车型
    private String pickupModel;
    // 还车油/电量
    private String checkoutFuel;
    // 经办人
    private String handler;

    /**
     * 车辆前方图片
     */
    private String frontImg;

    /**
     * 车辆后方图片
     */
    private String backImg;

    /**
     * 车辆右侧图片
     */
    private String rightImg;

    /**
     * 车辆左侧图片
     */
    private String leftImg;
    /**
     * etc图片
     */
    private List<String> etcImgs;
    /**
     * 油量图片
     */
    private List<String> fuleImgs;

    /**
     * 灯光检查图片
     */
    private List<String> lightImgs;

    /**
     * 车检证图片
     */
    private List<String> vehicleInspectionImgs;

    /**
     * 车损图片
     */
    private List<String> vehicleDamageImgs;

    /**
     * 车损说明
     */
    private String vehicleDamageDesc;

    /**
     * 免费延长时间
     */
    private String freeExtensionTime;

    /**
     * 燃油费
     */
    private BigDecimal fuelCost;

    /**
     * 超里程费用
     */
    private BigDecimal extraMileageFee;
}
