package com.ixtech.management.facade.api;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.VendorService;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.VendorAddResp;
import com.ixtech.management.integration.internal.resp.VendorListInfoResp;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 供应商管理
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:15
 */
@RestController
@RequestMapping("/v1/management/internal/vendor")
public class VendorController {

    @Resource
    private VendorService vendorService;

    /**
     * 查询供应商列表
     *
     * @param vendorListQueryReq 条件筛选参数
     * @return
     */
    @PermitAll
    @PostMapping(value = "/list")
    public ApiResponse<PageResponse<VendorListInfoResp>> list(@RequestBody VendorListQueryReq vendorListQueryReq) {
        PageResponse<VendorListInfoResp> result = vendorService.list(vendorListQueryReq);
        return ApiResponse.success(result);
    }

    /**
     * 新增供应商
     *
     * @param vendorAddReq
     * @return
     */
    @PermitAll
    @PostMapping(value = "/add")
    public ApiResponse<VendorAddResp> add(@Validated @RequestBody VendorAddReq vendorAddReq) {
        VendorAddResp result = vendorService.add(vendorAddReq);
        return ApiResponse.success(result);
    }

    /**
     * 更新供应商
     *
     * @param vendorUpdateReq
     * @return
     */
    @PermitAll
    @PostMapping(value = "/update")
    public ApiResponse<Void> update(@Validated @RequestBody VendorUpdateReq vendorUpdateReq) {
        vendorService.update(vendorUpdateReq);
        return ApiResponse.success();
    }

    /**
     * 供应商上下线
     *
     * @param vendorSetActiveReq
     * @return
     */
    @PermitAll
    @PostMapping(value = "/set_active")
    public ApiResponse<Void> setActive(@Validated @RequestBody VendorSetActiveReq vendorSetActiveReq) {
        vendorService.setActive(vendorSetActiveReq);
        return ApiResponse.success();
    }

    /**
     * 供应商下拉列表
     *
     * @return
     */
    @PermitAll
    @PostMapping(value = "/dropdown_list")
    public ApiResponse<List<SelectOptionResponse<Long>>> dropdownList(@RequestBody VendorDropdownReq vendorDropdownReq) {
        List<SelectOptionResponse<Long>> result = vendorService.dropdownList(vendorDropdownReq);
        return ApiResponse.success(result);
    }

}
