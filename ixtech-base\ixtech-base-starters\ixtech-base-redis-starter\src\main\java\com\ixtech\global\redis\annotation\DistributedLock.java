package com.ixtech.global.redis.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedLock {

    /**
     * 锁的 key (动态部分)。
     * <p>
     * 系统会自动在前面拼接一个统一的、在配置文件中定义的前缀 (例如 "myapp:lock:")。
     * 你只需要在这里定义能唯一标识当前操作的部分。
     * </p>
     * <p>
     * 支持 Spring Expression Language (SpEL) 表达式。
     * 例如："'user-update:' + #user.id"
     * </p>
     *
     * @return 锁的 key 的动态部分
     */
    String key();

    /**
     * 获取锁的最大等待时间。
     * 默认-1，表示不等待，立即尝试获取。如果获取失败则立即返回。
     *
     * @return 等待时间
     */
    long waitTime() default -1L;

    /**
     * 锁的租约时间（自动释放时间）。
     * 默认30秒。业务执行时间不应超过此值。
     *
     * @return 租约时间
     */
    long leaseTime() default 30L;

    /**
     * 时间单位。
     * 默认为秒。
     *
     * @return 时间单位
     */
    TimeUnit unit() default TimeUnit.SECONDS;
}
