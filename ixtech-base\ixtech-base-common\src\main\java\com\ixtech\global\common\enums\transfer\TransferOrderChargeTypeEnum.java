package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 接送机订单金额类型
 *
 * <AUTHOR> hu
 * @date 2025/7/20 18:37
 */
@Getter
public enum TransferOrderChargeTypeEnum {

    BASE_FEE(1, "基础费用"),
    ADDITIONAL_SERVICE_FEE(2, "附加服务费"),
    ASSIGNMENT_FEE(3, "指派金额"),
    CANCELLATION_FEE(4, "取消费"),
    WAITING_FEE(5, "超时等待费"),
    DRIVER_FEE(6, "司机费用"),
    ;

    TransferOrderChargeTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 金额类型code
     */
    private final Integer code;

    /**
     * 金额类型描述
     */
    private final String value;

}
