package com.ixtech.global.common.enums;

import lombok.Getter;

/**
 * 里程单位枚举类
 */
@Getter
public enum MileageUnitEnum {
    /**
     * 删除状态
     */
    KM(1, "km"),
    /**
     * 有效状态
     */
    MILES(2, "miles");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 构造方法
     *
     * @param code 码
     * @param name 单位名称
     */
    MileageUnitEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MileageUnitEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MileageUnitEnum type : MileageUnitEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
