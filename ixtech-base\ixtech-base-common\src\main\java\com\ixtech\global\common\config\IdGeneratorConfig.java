package com.ixtech.global.common.config;

import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Enumeration;

/**
 * Yitter ID 生成器 Spring 配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass(YitIdHelper.class)
public class IdGeneratorConfig {

    private static final Logger log = LoggerFactory.getLogger(IdGeneratorConfig.class);

    private final IdGeneratorProperties properties;

    public IdGeneratorConfig(IdGeneratorProperties properties) {
        this.properties = properties;
    }

    @PostConstruct
    public void init() {
        // 计算 WorkerId 的最大值
        long maxWorkerId = (1L << properties.getWorkerIdBitLength()) - 1;
        short workerId;

        // 判断用户是否配置了 workerId
        if (properties.getWorkerId() == null) {
            // 未配置，自动生成
            workerId = generateAutoWorkerId(maxWorkerId);
            log.info("未检测到 'yit.id-generator.worker-id' 配置，已自动生成 WorkerId: {}", workerId);
        } else {
            // 已配置，使用配置值
            workerId = properties.getWorkerId();
            if (workerId < 0 || workerId > maxWorkerId) {
                throw new IllegalArgumentException(String.format("配置的 WorkerId (%d) 超出范围 [0, %d]", workerId, maxWorkerId));
            }
            log.info("使用配置的 WorkerId: {}", workerId);
        }

        // 1. 创建 IdGeneratorOptions 对象
        IdGeneratorOptions options = new IdGeneratorOptions(workerId);
        options.WorkerIdBitLength = properties.getWorkerIdBitLength();
        options.SeqBitLength = properties.getSeqBitLength();

        // 2. 设置 BaseTime
        if (StringUtils.hasText(properties.getBaseTime())) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate baseDate = LocalDate.parse(properties.getBaseTime(), formatter);
                options.BaseTime = baseDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            } catch (Exception e) {
                log.error("无效的 BaseTime 日期格式: '{}'。请使用 'yyyy-MM-dd' 格式。将使用默认 BaseTime。", properties.getBaseTime(), e);
            }
        }

        // 3. 保存参数（全局只需一次）
        YitIdHelper.setIdGenerator(options);

        log.info("Yitter ID 生成器初始化成功。配置 => WorkerId: {}, WorkerIdBitLength: {}, SeqBitLength: {}, BaseTime: {}",
                options.WorkerId, options.WorkerIdBitLength, options.SeqBitLength, options.BaseTime);
        log.warn("重要提示：在分布式环境下，请确保每个节点的 WorkerId 具有唯一的值！自动生成机制不能100%保证唯一性，建议生产环境手动配置。");
    }

    /**
     * 根据机器 MAC/IP/Hostname 生成 WorkerId，确保其在 maxWorkerId 范围内
     * @param maxWorkerId 最大允许的 WorkerId
     * @return 生成的 WorkerId
     */
    private short generateAutoWorkerId(long maxWorkerId) {
        long generatedId = 0;
        try {
            // 优先使用 MAC 地址
            generatedId = getMacId();
            if (generatedId == 0) {
                // 如果 MAC 地址获取失败，则使用 IP 地址
                generatedId = getIpId();
            }
        } catch (Exception e) {
            log.warn("自动生成 WorkerId 时发生异常，将尝试使用 Hostname。错误: {}", e.getMessage());
            // 如果上述都失败，则使用 Hostname 的 hashcode
            try {
                generatedId = InetAddress.getLocalHost().getHostName().hashCode();
            } catch (Exception ex) {
                log.error("获取 Hostname 失败，将使用默认 WorkerId 0。请考虑手动配置！", ex);
            }
        }

        // 取模确保不会超过最大值
        return (short) (Math.abs(generatedId) % (maxWorkerId + 1));
    }

    private long getMacId() throws Exception {
        Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
        while (networkInterfaces.hasMoreElements()) {
            NetworkInterface ni = networkInterfaces.nextElement();
            // 过滤掉虚拟接口和回环接口
            if (ni != null && !ni.isLoopback() && ni.isUp() && !ni.isVirtual()) {
                byte[] mac = ni.getHardwareAddress();
                if (mac != null) {
                    // 使用MAC地址后两位生成ID
                    return ((long) (mac[mac.length - 2] & 0xFF) << 8) | (long) (mac[mac.length - 1] & 0xFF);
                }
            }
        }
        return 0; // 未找到MAC地址
    }

    private long getIpId() throws Exception {
        // 使用IP地址最后一部分生成ID
        byte[] ipAddress = InetAddress.getLocalHost().getAddress();
        return ipAddress[ipAddress.length - 1] & 0xFF;
    }
}
