package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合作模式枚举
 * 对应表: ix_vendor, 字段: cooperation_mode
 * (1-SaaS, 2-品牌加盟, 3-代运营服务)
 */
@Getter
@AllArgsConstructor
public enum CooperationModeEnum implements DictInf {

    SaaS(1, "SaaS"),
    BRAND_FRANCHISE(2, "品牌加盟"),
    AGENCY_OPERATION(3, "代运营服务"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }
}
