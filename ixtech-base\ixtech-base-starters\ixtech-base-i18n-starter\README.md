# 使用教程

## 1. 将翻译组件引入项目

* 添加maven依赖
```
<dependency>
    <groupId>com.ixtech.global</groupId>
    <artifactId>ixtech-base-i18n-starter</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-openfeign</artifactId>
</dependency>
<dependency>
    <groupId>com.github.ben-manes.caffeine</groupId>
    <artifactId>caffeine</artifactId>
</dependency>
```

* 在Spring Boot配置文件中配置（必填）
```
i18n:
  # 翻译服务的feign调用地址（以实际为准）（必填）
  url: http://localhost:8080
```

* 其他可选配置（选填）
```
i18n:
  # 是否启用翻译功能（默认true表示启用）（选填）
  enable: true

  # 本地缓存配置（选填）
  local-cache:
    # 是否启用本地缓存（默认为true表示启用）（选填）
    enable: false
    # 本地缓存最大缓存数量（默认1000）（选填）
    maximum-size: 5000
    # 本地缓存访问后多久过期（单位秒）（默认300）（选填）
    expire-after-access: 600
  # 调用翻译服务进行翻译的超时配置（选填）  
  feign:  
    # 配置调用翻译服务进行翻译的连接超时时间（单位毫秒）（默认2000）（选填）
    connect-timeout: 3000
    # 配置调用翻译服务进行翻译的读取超时时间（单位毫秒）（默认2000）（选填）
    read-timeout: 3000
    
```


## 2. 翻译功能使用方法

### 1. 对接口返回对象中的字段内容进行翻译
* 在需要启用翻译能力的`Controller`层或`Service`层方法上添加`@I18nTranslation`注解，让翻译组件对方法返回结果进行拦截
```
@I18nTranslation
public User getUserInfo(...){
    ...
}
```

* 在需要翻译的类字段上添加`@I18nField`注解
```
# 使用方式1（需要翻译的字段和保存了翻译资源key的字段不是同一个时）

public class  User {

    private String name;

    # name 字段为需要翻译的字段
    # nameTranslation 字段保存了name字段的翻译资源key
    # 通过link字段绑定 name 字段，表示 nameTranslation 中记录了 name 字段的翻译资源key
    # 组件会解析到 nameTranslation 中的资源key，调用翻译能力找到对应的翻译文案，然后设置到 link 绑定的 name 字段上
    @I18nField(link = "name")
    private String nameTranslation;

}

# 使用方式2（需要翻译的字段和保存了翻译资源key的字段是同一个时）

public class  User {

    # name 字段为需要翻译的字段
    # 组件会解析到 name 中的值作为翻译资源key，调用翻译能力找到对应的翻译文案，然后设置回当前字段
    @I18nField
    private String name;

}

# 使用方式3（特殊场景用法）

public class  User {

    # name 字段为需要翻译的字段
    # 组件会解析到 @I18nField 中的 resourceKey 值作为翻译资源key，调用翻译能力找到对应的翻译文案，然后设置回当前字段
    @I18nField(resourceKey = "123")
    private String name;

}
```

### 2. 对返回的异常描述进行翻译
* 对继承了`ErrorCode`的错误枚举，重写`getResourceKey`方法，返回该错误枚举对应的翻译资源key
```
#如：这样写表示使用枚举的错误码code作为翻译资源key
@Override
public String getResourceKey() {
    return getFullCode();
}
```

* 在代码中抛出异常时，组件会在全局异常处理器将异常转为携带错误码`code`和错误信息`message`的`ApiResponse`对象后，查找对应的翻译资源key进行翻译，然后设置回`message`参数中
```
# 这样写会尝试通过获取getResourceKey方法获取该枚举的翻译资源key，来获取翻译文案
throw ServerException.of(ErrorCodeEnum.PARAM_INVALID_TIME_FORMAT);

# 这样写表示开发人员想要返回自定义的错误文案，此时翻译器不会对错误文案进行翻译
throw ServerException.of(ErrorCodeEnum.PARAM_INVALID_TIME_FORMAT, "错误文案");
```

### 3. 直接在代码中引入`I18nTranslationExecutor`翻译执行器，手动进行翻译
```
#在代码中注入翻译执行器
@Autowired
private I18nTranslationExecutor i18nTranslationExecutor;

...
# 翻译资源key
String resourceKey = "123";
# 使用翻译执行器获取翻译文案
String translateText = i18nTranslationExecutor.getTranslation(resourceKey);

...

```

### 4. 对`Validator`相关注解的错误文案进行翻译
推荐统一使用英文错误文案
```
@NotNull(message="id is required")
private Long id;
```
如有必要对该场景进行国际化翻译，可以使用`{{resourceKey}}`的方式设置错误文案的翻译资源key
```
@NotNull(message="{{123456}}")
private Long id;
```

## 3. 翻译资源key的维护

翻译功能会通过翻译资源key来获取翻译文案，所以在使用前需在数据库`翻译资源表i18n_resource`中注册翻译资源key，
并在`翻译文案表i18n_translation`中维护翻译资源key在各个语言下的翻译文案

目前已对芋道商户系统的 部分菜单名称、字典数据、错误码描述、参数校验错误文案进行了翻译。
如，现在我需要对“字典”业务数据进行翻译，示例流程：
```
# 1. 初始化翻译资源
INSERT INTO i18n_resource(name, resource_key, resource_type, `domain`, module) 
VALUES
('男', 'yd.dict.1', 1, 0, 2),
('女', 'yd.dict.2', 1, 0, 2),

('开启', 'yd.dict.3', 1, 0, 2),
('关闭', 'yd.dict.4', 1, 0, 2),

('是', 'yd.dict.5', 1, 0, 2),
('否', 'yd.dict.6', 1, 0, 2),

('系统消息', 'yd.dict.7', 1, 0, 2),
('通知公告', 'yd.dict.8', 1, 0, 2)
;

# 2. 为翻译资源添加翻译文案
INSERT INTO i18n_translation(resource_key, language_code, content) 
VALUES 
('yd.dict.1', 'zh-CN', '男'),
('yd.dict.1', 'en', 'Man'),
('yd.dict.2', 'zh-CN', '女'),
('yd.dict.2', 'en', 'Woman'),

('yd.dict.3', 'zh-CN', '开启'),
('yd.dict.3', 'en', 'Enable'),
('yd.dict.4', 'zh-CN', '关闭'),
('yd.dict.4', 'en', 'Disable'),

('yd.dict.5', 'zh-CN', '是'),
('yd.dict.5', 'en', 'True'),
('yd.dict.6', 'zh-CN', '否'),
('yd.dict.6', 'en', 'False'),

('yd.dict.7', 'zh-CN', '系统消息'),
('yd.dict.7', 'en', 'System'),
('yd.dict.8', 'zh-CN', '通知公告'),
('yd.dict.8', 'en', 'Notification')
;

# 3. 为字典表增加翻译资源key，用于对字典数据进行翻译
ALTER TABLE system_dict_data 
ADD label_translation varchar(16) NULL COMMENT 'label的翻译资源key';

# 4. 维护字典数据对应的翻译资源key
update system_dict_data set label_translation = 'yd.dict.7' WHERE label = '系统消息' AND dict_type = 'system_notify_template_type';
update system_dict_data set label_translation = 'yd.dict.8' WHERE label = '通知公告' AND dict_type = 'system_notify_template_type';

```
可参考类似的配置方式
