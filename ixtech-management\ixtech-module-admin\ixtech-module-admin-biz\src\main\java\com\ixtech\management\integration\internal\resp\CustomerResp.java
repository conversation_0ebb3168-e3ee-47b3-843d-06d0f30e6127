package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerResp {

    private String surname; // 客户姓
    private String firstName; // 客户名
    private Integer age; // 客户年龄
    private Boolean mainDriver; // 是否是主驾驶人：0-不是，1-是
    private String countryCode;//客源地
    private String email; // 电子邮件地址
    private String telephoneAreaCode; // 电话区号
    private String telephoneNumber; // 电话号码
    private String flightNumber; // 航班号
    private String driverLicenceNumber; // 驾照号
    private String passportNumber; // 护照号
    private List<String> licenceImgs; // 证件图片
    private String driverLicenceExpiration; // 驾照有效期
    private String passportExpiration; // 护照有效期
    private String ticketAddress; // 罚单地址

}
