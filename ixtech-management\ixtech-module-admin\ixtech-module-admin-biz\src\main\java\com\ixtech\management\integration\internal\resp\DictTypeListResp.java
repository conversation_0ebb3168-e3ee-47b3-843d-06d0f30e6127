package com.ixtech.management.integration.internal.resp;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典类型列表响应
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DictTypeListResp {
    private Map<String, List<DictItemResp>> data = new HashMap<>();

    public void addDictList(String type, List<DictItemResp> dictList) {
        data.put(type, dictList);
    }
}
