<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ixtech.global</groupId>
    <artifactId>ixtech-base-parent</artifactId>
    <version>1.0.19-SNAPSHOT</version>
    <relativePath>../ixtech-base-parent/pom.xml</relativePath>
  </parent>
  <artifactId>ixtech-base-starters</artifactId>
  <version>1.0.19-SNAPSHOT</version>
  <packaging>pom</packaging>
  <modules>
    <module>ixtech-base-mysql-starter</module>
    <module>ixtech-base-redis-starter</module>
    <module>ixtech-base-feign-starter</module>
    <module>ixtech-base-i18n-starter</module>
  </modules>
  <properties>
    <project.basedir>${basedir}/..</project.basedir>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.ixtech.global</groupId>
        <artifactId>ixtech-base-mysql-starter</artifactId>
        <version>1.0.19-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ixtech.global</groupId>
        <artifactId>ixtech-base-feign-starter</artifactId>
        <version>1.0.19-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ixtech.global</groupId>
        <artifactId>ixtech-base-redis-starter</artifactId>
        <version>1.0.19-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ixtech.global</groupId>
        <artifactId>ixtech-base-i18n-starter</artifactId>
        <version>1.0.19-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.ixtech.global</groupId>
        <artifactId>ixtech-base-common</artifactId>
        <version>1.0.19-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.18.34</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>3.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>3.5.16</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>1.2.21</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>8.4.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.8</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-collections4</artifactId>
        <version>4.3</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>3.27.0</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>javax.annotation</groupId>
        <artifactId>javax.annotation-api</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
        <version>4.2.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        <version>4.2.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>33.2.0-jre</version>
      </dependency>
      <dependency>
        <groupId>org.hashids</groupId>
        <artifactId>hashids</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis.generator</groupId>
        <artifactId>mybatis-generator-core</artifactId>
        <version>1.3.7</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis.generator</groupId>
        <artifactId>mybatis-generator-maven-plugin</artifactId>
        <version>1.3.7</version>
      </dependency>
      <dependency>
        <groupId>org.jodd</groupId>
        <artifactId>jodd-bean</artifactId>
        <version>5.0.13</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>5.12.1</version>
      </dependency>
      <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper</artifactId>
        <version>6.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>2.16.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.16.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr310</artifactId>
        <version>2.16.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>2.14.5</version>
      </dependency>
      <dependency>
        <groupId>net.iakovlev</groupId>
        <artifactId>timeshape</artifactId>
        <version>2024a.25</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>1.6.3</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.aliyun.oss</groupId>
        <artifactId>aliyun-sdk-oss</artifactId>
        <version>3.17.4</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson</artifactId>
        <version>12.3</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>4.0.3</version>
      </dependency>
      <dependency>
        <groupId>commons-beanutils</groupId>
        <artifactId>commons-beanutils</artifactId>
        <version>1.9.4</version>
      </dependency>
      <dependency>
        <groupId>com.github.yitter</groupId>
        <artifactId>yitter-idgenerator</artifactId>
        <version>1.0.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-console</artifactId>
        <version>6.1.6</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-spring</artifactId>
        <version>6.1.6</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>angus-core</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>angus-mail</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>dsn</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>gimap</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>imap</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>jakarta.mail</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>logging-mailhandler</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>pop3</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>smtp</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjrt</artifactId>
        <version>1.9.23</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjtools</artifactId>
        <version>1.9.23</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjweaver</artifactId>
        <version>1.9.23</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility</artifactId>
        <version>4.2.2</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-groovy</artifactId>
        <version>4.2.2</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-kotlin</artifactId>
        <version>4.2.2</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-scala</artifactId>
        <version>4.2.2</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>1.15.11</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-agent</artifactId>
        <version>1.15.11</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-api</artifactId>
        <version>2.6.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-config</artifactId>
        <version>2.6.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-core</artifactId>
        <version>2.6.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-jcache</artifactId>
        <version>2.6.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-micrometer</artifactId>
        <version>2.6.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-spring</artifactId>
        <version>2.6.1.Final</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
        <version>3.1.8</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>guava</artifactId>
        <version>3.1.8</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>jcache</artifactId>
        <version>3.1.8</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>simulator</artifactId>
        <version>3.1.8</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>java-driver-core</artifactId>
        <version>4.18.1</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml</groupId>
        <artifactId>classmate</artifactId>
        <version>1.7.0</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-dbcp2</artifactId>
        <version>2.12.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>commons-pool</groupId>
        <artifactId>commons-pool</artifactId>
        <version>1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>2.12.1</version>
      </dependency>
      <dependency>
        <groupId>com.couchbase.client</groupId>
        <artifactId>java-client</artifactId>
        <version>3.7.8</version>
      </dependency>
      <dependency>
        <groupId>org.crac</groupId>
        <artifactId>crac</artifactId>
        <version>1.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.ibm.db2</groupId>
        <artifactId>jcc</artifactId>
        <version>11.5.9.0</version>
      </dependency>
      <dependency>
        <groupId>io.spring.gradle</groupId>
        <artifactId>dependency-management-plugin</artifactId>
        <version>1.1.7</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derby</artifactId>
        <version>10.16.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyclient</artifactId>
        <version>10.16.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbynet</artifactId>
        <version>10.16.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyoptionaltools</artifactId>
        <version>10.16.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyshared</artifactId>
        <version>10.16.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbytools</artifactId>
        <version>10.16.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>3.10.8</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>3.10.8</version>
        <classifier>jakarta</classifier>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-clustered</artifactId>
        <version>3.10.8</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-transactions</artifactId>
        <version>3.10.8</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-transactions</artifactId>
        <version>3.10.8</version>
        <classifier>jakarta</classifier>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client</artifactId>
        <version>8.15.5</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client-sniffer</artifactId>
        <version>8.15.5</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>co.elastic.clients</groupId>
        <artifactId>elasticsearch-java</artifactId>
        <version>8.15.5</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-commandline</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-core</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-cassandra</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-db2</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-derby</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-hsqldb</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-informix</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-mongodb</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-oracle</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-postgresql</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-redshift</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-saphana</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-snowflake</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-sybasease</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-firebird</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-gcp-bigquery</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-gcp-spanner</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-mysql</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-singlestore</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-sqlserver</artifactId>
        <version>10.20.1</version>
      </dependency>
      <dependency>
        <groupId>org.freemarker</groupId>
        <artifactId>freemarker</artifactId>
        <version>2.3.34</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.web</groupId>
        <artifactId>jakarta.servlet.jsp.jstl</artifactId>
        <version>3.0.1</version>
      </dependency>
      <dependency>
        <groupId>com.graphql-java</groupId>
        <artifactId>graphql-java</artifactId>
        <version>22.3</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>2.11.0</version>
      </dependency>
      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>2.3.232</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast</artifactId>
        <version>5.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-spring</artifactId>
        <version>5.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-agroal</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-ant</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-c3p0</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-community-dialects</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-core</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-envers</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-graalvm</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-hikaricp</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-jcache</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-jpamodelgen</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-micrometer</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-proxool</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-spatial</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-testing</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-vibur</artifactId>
        <version>6.6.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>8.0.2.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator-annotation-processor</artifactId>
        <version>8.0.2.Final</version>
      </dependency>
      <dependency>
        <groupId>com.zaxxer</groupId>
        <artifactId>HikariCP</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>org.hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>2.7.3</version>
      </dependency>
      <dependency>
        <groupId>org.htmlunit</groupId>
        <artifactId>htmlunit</artifactId>
        <version>4.5.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpasyncclient</artifactId>
        <version>4.1.5</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
        <version>5.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-cache</artifactId>
        <version>5.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-fluent</artifactId>
        <version>5.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>4.4.16</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore-nio</artifactId>
        <version>4.4.16</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5</artifactId>
        <version>5.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-h2</artifactId>
        <version>5.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-reactive</artifactId>
        <version>5.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.influxdb</groupId>
        <artifactId>influxdb-java</artifactId>
        <version>2.24</version>
      </dependency>
      <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>2.1.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
        <version>2.1.1</version>
      </dependency>
      <dependency>
        <groupId>jakarta.inject</groupId>
        <artifactId>jakarta.inject-api</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>jakarta.jms</groupId>
        <artifactId>jakarta.jms-api</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>jakarta.json</groupId>
        <artifactId>jakarta.json-api</artifactId>
        <version>2.1.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.json.bind</groupId>
        <artifactId>jakarta.json.bind-api</artifactId>
        <version>3.0.1</version>
      </dependency>
      <dependency>
        <groupId>jakarta.mail</groupId>
        <artifactId>jakarta.mail-api</artifactId>
        <version>2.1.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.management.j2ee</groupId>
        <artifactId>jakarta.management.j2ee-api</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>jakarta.persistence</groupId>
        <artifactId>jakarta.persistence-api</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>6.0.0</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet.jsp.jstl</groupId>
        <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.transaction</groupId>
        <artifactId>jakarta.transaction-api</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>jakarta.validation</groupId>
        <artifactId>jakarta.validation-api</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.websocket</groupId>
        <artifactId>jakarta.websocket-api</artifactId>
        <version>2.1.1</version>
      </dependency>
      <dependency>
        <groupId>jakarta.websocket</groupId>
        <artifactId>jakarta.websocket-client-api</artifactId>
        <version>2.1.1</version>
      </dependency>
      <dependency>
        <groupId>jakarta.ws.rs</groupId>
        <artifactId>jakarta.ws.rs-api</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>4.0.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.soap</groupId>
        <artifactId>jakarta.xml.soap-api</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.ws</groupId>
        <artifactId>jakarta.xml.ws-api</artifactId>
        <version>4.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler</artifactId>
        <version>3.1.12</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler-jdk</artifactId>
        <version>3.1.12</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>3.1.12</version>
      </dependency>
      <dependency>
        <groupId>javax.cache</groupId>
        <artifactId>cache-api</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>javax.money</groupId>
        <artifactId>money-api</artifactId>
        <version>1.1</version>
      </dependency>
      <dependency>
        <groupId>jaxen</groupId>
        <artifactId>jaxen</artifactId>
        <version>2.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.firebirdsql.jdbc</groupId>
        <artifactId>jaybird</artifactId>
        <version>5.0.6.java11</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging</artifactId>
        <version>3.6.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.jdom</groupId>
        <artifactId>jdom2</artifactId>
        <version>*******</version>
      </dependency>
      <dependency>
        <groupId>redis.clients</groupId>
        <artifactId>jedis</artifactId>
        <version>5.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-reactive-httpclient</artifactId>
        <version>4.0.9</version>
      </dependency>
      <dependency>
        <groupId>com.samskivert</groupId>
        <artifactId>jmustache</artifactId>
        <version>1.16</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq</artifactId>
        <version>3.19.21</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-codegen</artifactId>
        <version>3.19.21</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-kotlin</artifactId>
        <version>3.19.21</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-meta</artifactId>
        <version>3.19.21</version>
      </dependency>
      <dependency>
        <groupId>com.jayway.jsonpath</groupId>
        <artifactId>json-path</artifactId>
        <version>2.9.0</version>
      </dependency>
      <dependency>
        <groupId>com.jayway.jsonpath</groupId>
        <artifactId>json-path-assert</artifactId>
        <version>2.9.0</version>
      </dependency>
      <dependency>
        <groupId>net.minidev</groupId>
        <artifactId>json-smart</artifactId>
        <version>2.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.skyscreamer</groupId>
        <artifactId>jsonassert</artifactId>
        <version>1.5.3</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.jtds</groupId>
        <artifactId>jtds</artifactId>
        <version>1.3.1</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-api</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-basic-auth-extension</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-file</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-json</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-mirror</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-mirror-client</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-runtime</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-transforms</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>generator</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>3.8.1</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-log4j-appender</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-metadata</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-raft</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-server</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-server-common</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-server-common</artifactId>
        <version>3.8.1</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-shell</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-storage</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-storage-api</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.12</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.13</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-test-utils</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-tools</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.12</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.12</artifactId>
        <version>3.8.1</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.13</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.13</artifactId>
        <version>3.8.1</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>trogdor</artifactId>
        <version>3.8.1</version>
      </dependency>
      <dependency>
        <groupId>io.lettuce</groupId>
        <artifactId>lettuce-core</artifactId>
        <version>6.4.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-cdi</artifactId>
        <version>4.29.2</version>
      </dependency>
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-core</artifactId>
        <version>4.29.2</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.5.18</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>1.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.mariadb.jdbc</groupId>
        <artifactId>mariadb-java-client</artifactId>
        <version>3.4.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-stackdriver</artifactId>
        <version>1.14.5</version>
        <exclusions>
          <exclusion>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>bson</artifactId>
        <version>5.2.1</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>bson-record-codec</artifactId>
        <version>5.2.1</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-core</artifactId>
        <version>5.2.1</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-legacy</artifactId>
        <version>5.2.1</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-reactivestreams</artifactId>
        <version>5.2.1</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-sync</artifactId>
        <version>5.2.1</version>
      </dependency>
      <dependency>
        <groupId>com.microsoft.sqlserver</groupId>
        <artifactId>mssql-jdbc</artifactId>
        <version>12.8.1.jre11</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.nekohtml</groupId>
        <artifactId>nekohtml</artifactId>
        <version>1.9.22</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j.driver</groupId>
        <artifactId>neo4j-java-driver</artifactId>
        <version>5.28.3</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.ha</groupId>
        <artifactId>ons</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.ha</groupId>
        <artifactId>simplefan</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc11</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc11-production</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc8-production</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>rsi</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ucp</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ucp11</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.nls</groupId>
        <artifactId>orai18n</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.security</groupId>
        <artifactId>oraclepki</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.xml</groupId>
        <artifactId>xdb</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.xml</groupId>
        <artifactId>xmlparserv2</artifactId>
        <version>23.5.0.24.07</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.r2dbc</groupId>
        <artifactId>oracle-r2dbc</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.messaginghub</groupId>
        <artifactId>pooled-jms</artifactId>
        <version>3.1.7</version>
      </dependency>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>42.7.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-adapter</artifactId>
        <version>0.5.10</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-api</artifactId>
        <version>0.5.10</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-jackson</artifactId>
        <version>0.5.10</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-producer-cache-caffeine-shaded</artifactId>
        <version>0.5.10</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-producer-cache-caffeine</artifactId>
        <version>0.5.10</version>
      </dependency>
      <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz</artifactId>
        <version>2.3.2</version>
        <exclusions>
          <exclusion>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.zaxxer</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz-jobs</artifactId>
        <version>2.3.2</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-h2</artifactId>
        <version>1.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.mariadb</groupId>
        <artifactId>r2dbc-mariadb</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-mssql</artifactId>
        <version>1.0.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.asyncer</groupId>
        <artifactId>r2dbc-mysql</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-pool</artifactId>
        <version>1.0.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>r2dbc-postgresql</artifactId>
        <version>1.0.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-proxy</artifactId>
        <version>1.1.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-spi</artifactId>
        <version>1.0.0.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.rabbitmq</groupId>
        <artifactId>amqp-client</artifactId>
        <version>5.22.0</version>
      </dependency>
      <dependency>
        <groupId>com.rabbitmq</groupId>
        <artifactId>stream-client</artifactId>
        <version>0.18.0</version>
      </dependency>
      <dependency>
        <groupId>org.reactivestreams</groupId>
        <artifactId>reactive-streams</artifactId>
        <version>1.0.4</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex.rxjava3</groupId>
        <artifactId>rxjava</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator-autoconfigure</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure-processor</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-buildpack-platform</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-metadata</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-devtools</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-docker-compose</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-jarmode-tools</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader-classic</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader-tools</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-properties-migrator</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-activemq</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-amqp</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-artemis</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-batch</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-cache</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-cassandra</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-cassandra-reactive</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-couchbase</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-couchbase-reactive</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jdbc</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-ldap</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-neo4j</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-r2dbc</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-rest</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-freemarker</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-graphql</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-groovy-templates</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-hateoas</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-integration</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jdbc</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jersey</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jetty</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jooq</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-json</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-log4j2</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-logging</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mail</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mustache</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-authorization-server</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-client</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-pulsar</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-pulsar-reactive</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-quartz</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-reactor-netty</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-rsocket</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-thymeleaf</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-tomcat</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-undertow</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web-services</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test-autoconfigure</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-testcontainers</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.messaging.saaj</groupId>
        <artifactId>saaj-impl</artifactId>
        <version>3.0.4</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>htmlunit3-driver</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>com.sendgrid</groupId>
        <artifactId>sendgrid-java</artifactId>
        <version>4.10.3</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl-over-slf4j</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jul-to-slf4j</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>log4j-over-slf4j</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-ext</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk-platform-logging</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk14</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-nop</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-reload4j</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>2.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>2.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-authorization-server</artifactId>
        <version>1.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.graphql</groupId>
        <artifactId>spring-graphql</artifactId>
        <version>1.3.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.graphql</groupId>
        <artifactId>spring-graphql-test</artifactId>
        <version>1.3.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.hateoas</groupId>
        <artifactId>spring-hateoas</artifactId>
        <version>2.4.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka</artifactId>
        <version>3.3.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka-test</artifactId>
        <version>3.3.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-core</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-ldif-core</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-odm</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-test</artifactId>
        <version>3.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.retry</groupId>
        <artifactId>spring-retry</artifactId>
        <version>2.0.11</version>
      </dependency>
      <dependency>
        <groupId>org.xerial</groupId>
        <artifactId>sqlite-jdbc</artifactId>
        <version>3.47.2.0</version>
      </dependency>
      <dependency>
        <groupId>com.redis</groupId>
        <artifactId>testcontainers-redis</artifactId>
        <version>2.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf</groupId>
        <artifactId>thymeleaf</artifactId>
        <version>3.1.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf</groupId>
        <artifactId>thymeleaf-spring6</artifactId>
        <version>3.1.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.github.mxab.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-data-attribute</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-springsecurity6</artifactId>
        <version>3.1.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>nz.net.ultraq.thymeleaf</groupId>
        <artifactId>thymeleaf-layout-dialect</artifactId>
        <version>3.3.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-annotations-api</artifactId>
        <version>10.1.39</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-jdbc</artifactId>
        <version>10.1.39</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-jsp-api</artifactId>
        <version>10.1.39</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>10.1.39</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-el</artifactId>
        <version>10.1.39</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-jasper</artifactId>
        <version>10.1.39</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-websocket</artifactId>
        <version>10.1.39</version>
      </dependency>
      <dependency>
        <groupId>com.unboundid</groupId>
        <artifactId>unboundid-ldapsdk</artifactId>
        <version>6.0.11</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-core</artifactId>
        <version>2.3.18.Final</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-servlet</artifactId>
        <version>2.3.18.Final</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-websockets-jsr</artifactId>
        <version>2.3.18.Final</version>
      </dependency>
      <dependency>
        <groupId>org.webjars</groupId>
        <artifactId>webjars-locator-core</artifactId>
        <version>0.59</version>
      </dependency>
      <dependency>
        <groupId>org.webjars</groupId>
        <artifactId>webjars-locator-lite</artifactId>
        <version>1.0.1</version>
      </dependency>
      <dependency>
        <groupId>wsdl4j</groupId>
        <artifactId>wsdl4j</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-assertj</artifactId>
        <version>2.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-assertj3</artifactId>
        <version>2.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-core</artifactId>
        <version>2.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-jakarta-jaxb-impl</artifactId>
        <version>2.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-legacy</artifactId>
        <version>2.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-matchers</artifactId>
        <version>2.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-placeholders</artifactId>
        <version>2.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse</groupId>
        <artifactId>yasson</artifactId>
        <version>3.0.4</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-all</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-amqp</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-blueprint</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-client</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-http</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jaas</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jdbc-store</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-kahadb-store</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-karaf</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jms-pool</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-log4j-appender</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-mqtt</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-pool</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-generator</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-legacy</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-osgi</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-ra</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-rar</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-run</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-runtime-config</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-shiro</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-stomp</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web-console</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web-demo</artifactId>
        <version>6.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-amqp-protocol</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-boot</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-branding</artifactId>
        <version>2.37.0</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-cdi-client</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-cli</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-commons</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-console</artifactId>
        <version>2.37.0</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-core-client</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-core-client-all</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-core-client-osgi</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-dto</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-features</artifactId>
        <version>2.37.0</version>
        <type>xml</type>
        <classifier>features</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-hornetq-protocol</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-hqclient-protocol</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-client</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-client-all</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-openwire-protocol</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-ra</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-server</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-service-extensions</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jdbc-store</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jms-client</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jms-client-all</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jms-client-osgi</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jms-server</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-journal</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-mqtt-protocol</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-openwire-protocol</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-plugin</artifactId>
        <version>2.37.0</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-lockmanager-api</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-lockmanager-ri</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-ra</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-selector</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-server</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-server-osgi</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-service-extensions</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-stomp-protocol</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-web</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-website</artifactId>
        <version>2.37.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.plugin-tools</groupId>
        <artifactId>maven-plugin-annotations</artifactId>
        <version>3.13.1</version>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.26.3</version>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-guava</artifactId>
        <version>3.26.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.zipkin2</groupId>
        <artifactId>zipkin</artifactId>
        <version>2.27.1</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-okhttp3</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-libthrift</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-urlconnection</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-kafka</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-amqp-client</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-activemq-client</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-spring-beans</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-brave</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-metrics-micrometer</artifactId>
        <version>3.4.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-tests</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-jfr</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-log4j2</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-log4j12</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-slf4j</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-benchmarks</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-dubbo</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-grpc</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-http</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-http-tests</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-http-tests-jakarta</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-httpasyncclient</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-httpclient</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-httpclient5</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jakarta-jms</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jaxrs2</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jersey-server</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jms</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jms-jakarta</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-kafka-clients</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-kafka-streams</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-messaging</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mongodb</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql6</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql8</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-netty-codec-http</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-okhttp3</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-rpc</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-servlet</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-servlet-jakarta</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-rabbit</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-web</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-webmvc</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-vertx-web</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-spring-beans</artifactId>
        <version>6.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>java-driver-core-shaded</artifactId>
        <version>4.18.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>java-driver-mapper-processor</artifactId>
        <version>4.18.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>java-driver-mapper-runtime</artifactId>
        <version>4.18.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>java-driver-query-builder</artifactId>
        <version>4.18.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>java-driver-test-infra</artifactId>
        <version>4.18.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>java-driver-metrics-micrometer</artifactId>
        <version>4.18.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>java-driver-metrics-microprofile</artifactId>
        <version>4.18.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>native-protocol</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-shaded-guava</artifactId>
        <version>25.1-jre-graal-sub-1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-runtime</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-core</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-xjc</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-jxc</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>codemodel</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>txw2</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>xsom</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-impl</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-core</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-xjc</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-jxc</artifactId>
        <version>4.0.5</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>4.0.2</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>org.jvnet.staxex</groupId>
        <artifactId>stax-ex</artifactId>
        <version>2.1.0</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.fastinfoset</groupId>
        <artifactId>FastInfoset</artifactId>
        <version>2.1.1</version>
        <classifier>sources</classifier>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-runtime</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-core</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-xjc</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-jxc</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>codemodel</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>txw2</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>xsom</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-impl</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-core</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-xjc</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-jxc</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-osgi</artifactId>
        <version>4.0.5</version>
      </dependency>
      <dependency>
        <groupId>com.sun.istack</groupId>
        <artifactId>istack-commons-runtime</artifactId>
        <version>4.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.fastinfoset</groupId>
        <artifactId>FastInfoset</artifactId>
        <version>2.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.jvnet.staxex</groupId>
        <artifactId>stax-ex</artifactId>
        <version>2.1.0</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>angus-activation</artifactId>
        <version>2.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-ant</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-astbuilder</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-cli-commons</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-cli-picocli</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-console</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-contracts</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-datetime</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-dateutil</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-docgenerator</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-ginq</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-groovydoc</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-groovysh</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-jmx</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-json</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-jsr223</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-macro</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-macro-library</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-nio</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-servlet</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-sql</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-swing</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-templates</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-test</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-test-junit5</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-testng</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-toml</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-typecheckers</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-xml</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-yaml</artifactId>
        <version>4.0.26</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-api</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-jdbc</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-jdbc-common</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-sql</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-remote</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-rocksdb</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-common</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-embedded</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-remote</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-checkstyle</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cli-client</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hotrod</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-client-hotrod</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-client-rest</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-key-value-store-client</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-clustered-counter</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-clustered-lock</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-commons</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-commons-test</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-component-annotations</artifactId>
        <version>15.0.14.Final</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-component-processor</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-core</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jboss-marshalling</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hibernate-cache-commons</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hibernate-cache-spi</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hibernate-cache-v62</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache-commons</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache-remote</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-console</artifactId>
        <version>15.0.11.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-logging-annotations</artifactId>
        <version>15.0.14.Final</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-logging-processor</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-multimap</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-objectfilter</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query-core</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query-dsl</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-remote-query-client</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-remote-query-server</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-scripting</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-core</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-hotrod</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-memcached</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-resp</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-rest</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-router</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-runtime</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-runtime</artifactId>
        <version>15.0.14.Final</version>
        <classifier>loader</classifier>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-testdriver-core</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-testdriver-junit4</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-testdriver-junit5</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring6-common</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring6-embedded</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring6-remote</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring-boot3-starter-embedded</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring-boot3-starter-remote</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tasks</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tasks-api</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tools</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-anchored-keys</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-commons-graalvm</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-core-graalvm</artifactId>
        <version>15.0.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan.protostream</groupId>
        <artifactId>protostream</artifactId>
        <version>5.0.13.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan.protostream</groupId>
        <artifactId>protostream-types</artifactId>
        <version>5.0.13.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan.protostream</groupId>
        <artifactId>protostream-processor</artifactId>
        <version>5.0.13.Final</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-avro</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-cbor</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-csv</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-ion</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-properties</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-protobuf</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-smile</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-toml</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-xml</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-yaml</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-eclipse-collections</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-guava</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate4</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate5</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate5-jakarta</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate6</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hppc</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jakarta-jsonp</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jaxrs</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-joda</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-joda-money</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jdk8</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-json-org</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr353</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-pcollections</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-base</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-cbor-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-json-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-smile-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-xml-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-yaml-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-base</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-cbor-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-json-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-smile-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-xml-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-yaml-provider</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-all</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-annotation-support</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-extension-javatime</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-objects</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-retrofit2</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-stree</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-afterburner</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-android-record</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-blackbird</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-guice</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-guice7</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jaxb-annotations</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jakarta-xmlbind-annotations</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jsonSchema</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jsonSchema-jakarta</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-kotlin</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-mrbean</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-no-ctor-deser</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-osgi</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-parameter-names</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-paranamer</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.11</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.12</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.13</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_3</artifactId>
        <version>2.18.3</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-common</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-client</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-server</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.bundles</groupId>
        <artifactId>jaxrs-ri</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-apache-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-apache5-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-helidon-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-grizzly-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jnh-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jetty-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jetty11-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jetty-http2-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jdk-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-netty-connector</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jetty-http</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jetty11-http</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jetty-http2</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-grizzly2-http</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-grizzly2-servlet</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jetty-servlet</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jdk-http</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-netty-http</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-servlet</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-servlet-core</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-simple-http</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers.glassfish</groupId>
        <artifactId>jersey-gf-ejb</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-bean-validation</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-entity-filtering</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-micrometer</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-metainf-services</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.microprofile</groupId>
        <artifactId>jersey-mp-config</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-bean-validation</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-freemarker</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-jsp</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-mustache</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-proxy-client</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-spring6</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-declarative-linking</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-wadl-doclet</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-weld2-se</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-transaction</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-validation</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-servlet</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-ban-custom-hk2-binding</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi-rs-inject</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-guava</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-rxjava</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-rxjava2</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.microprofile</groupId>
        <artifactId>jersey-mp-rest-client</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-jaxb</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-jackson</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-jettison</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-processing</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-gson</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-binding</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-kryo</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-moxy</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-multipart</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-sse</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-client</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-server</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-signature</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth2-client</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.inject</groupId>
        <artifactId>jersey-hk2</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.inject</groupId>
        <artifactId>jersey-cdi2-se</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework</groupId>
        <artifactId>jersey-test-framework-core</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-bundle</artifactId>
        <version>3.1.10</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-external</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-grizzly2</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-inmemory</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-jdk-http</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-simple</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-jetty</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-jetty-http2</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-netty</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework</groupId>
        <artifactId>jersey-test-framework-util</artifactId>
        <version>3.1.10</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-annotations</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-apache-jsp</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-cdi</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-fcgi-proxy</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-glassfish-jstl</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-jaspi</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-jndi</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-jspc-maven-plugin</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-maven-plugin</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-plus</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-proxy</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-quickstart</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-runner</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-servlet</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-servlets</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-webapp</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.osgi</groupId>
        <artifactId>jetty-ee10-osgi-alpn</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.osgi</groupId>
        <artifactId>jetty-ee10-osgi-boot</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.osgi</groupId>
        <artifactId>jetty-ee10-osgi-boot-jsp</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.websocket</groupId>
        <artifactId>jetty-ee10-websocket-jakarta-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.websocket</groupId>
        <artifactId>jetty-ee10-websocket-jakarta-client-webapp</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.websocket</groupId>
        <artifactId>jetty-ee10-websocket-jakarta-common</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.websocket</groupId>
        <artifactId>jetty-ee10-websocket-jakarta-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.websocket</groupId>
        <artifactId>jetty-ee10-websocket-jetty-client-webapp</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.websocket</groupId>
        <artifactId>jetty-ee10-websocket-jetty-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10.websocket</groupId>
        <artifactId>jetty-ee10-websocket-servlet</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-deploy</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-ee</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http-spi</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http-tools</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-io</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jmx</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jndi</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-keystore</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-openid</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-osgi</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-plus</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-proxy</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-rewrite</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-security</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-session</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-slf4j-impl</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-start</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-unixdomain-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util-ajax</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-xml</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.demos</groupId>
        <artifactId>jetty-demo-handler</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>jetty-fcgi-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>jetty-fcgi-proxy</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>jetty-fcgi-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>jetty-http2-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>jetty-http2-client-transport</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>jetty-http2-common</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>jetty-http2-hpack</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>jetty-http2-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http3</groupId>
        <artifactId>jetty-http3-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http3</groupId>
        <artifactId>jetty-http3-client-transport</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http3</groupId>
        <artifactId>jetty-http3-common</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http3</groupId>
        <artifactId>jetty-http3-qpack</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http3</groupId>
        <artifactId>jetty-http3-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.quic</groupId>
        <artifactId>jetty-quic-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.quic</groupId>
        <artifactId>jetty-quic-common</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.quic</groupId>
        <artifactId>jetty-quic-quiche-common</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.quic</groupId>
        <artifactId>jetty-quic-quiche-foreign</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.quic</groupId>
        <artifactId>jetty-quic-quiche-jna</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.quic</groupId>
        <artifactId>jetty-quic-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>jetty-websocket-core-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>jetty-websocket-core-common</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>jetty-websocket-core-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>jetty-websocket-jetty-api</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>jetty-websocket-jetty-client</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>jetty-websocket-jetty-common</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>jetty-websocket-jetty-server</artifactId>
        <version>12.0.18</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>5.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-migrationsupport</artifactId>
        <version>5.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>5.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-commons</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-console</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-engine</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-jfr</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-launcher</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-reporting</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-runner</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-api</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-commons</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-engine</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-testkit</artifactId>
        <version>1.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
        <version>5.11.4</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk7</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk8</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-js</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-common</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-reflect</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-osgi-bundle</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-junit</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-junit5</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-testng</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-js</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-common</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-annotations-common</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-main-kts</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-script-runtime</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-common</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-jvm</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-jvm-host</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-ide-services</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-compiler</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-compiler-embeddable</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-daemon-client</artifactId>
        <version>1.9.25</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-android</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core-jvm</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-debug</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-guava</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-javafx</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-jdk8</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-jdk9</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-play-services</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-reactive</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-reactor</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-rx2</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-rx3</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-slf4j</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-swing</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-test-jvm</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-test</artifactId>
        <version>1.8.1</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-cbor-jvm</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-cbor</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-core-jvm</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-core</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-hocon</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-json-jvm</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-json</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-json-okio-jvm</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-json-okio</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-properties-jvm</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-properties</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-protobuf-jvm</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-protobuf</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-1.2-api</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api-test</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-appserver</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-cassandra</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core-test</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-couchdb</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-docker</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-flume-ng</artifactId>
        <version>2.23.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-iostreams</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jakarta-smtp</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jakarta-web</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jcl</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpa</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpl</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jul</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-layout-template-json</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-mongodb4</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-mongodb</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j2-impl</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-spring-boot</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-spring-cloud-config-client</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-taglib</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-to-jul</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-to-slf4j</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-web</artifactId>
        <version>2.24.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging</groupId>
        <artifactId>logging-parent</artifactId>
        <version>11.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>biz.aQute.bnd.annotation</artifactId>
        <version>7.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-annotations</artifactId>
        <version>4.8.6</version>
      </dependency>
      <dependency>
        <groupId>org.jspecify</groupId>
        <artifactId>jspecify</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>osgi.annotation</artifactId>
        <version>8.1.0</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>org.osgi.annotation.bundle</artifactId>
        <version>2.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>org.osgi.annotation.versioning</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-commons</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-core</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jakarta9</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-java11</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-java21</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jetty11</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jetty12</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-observation</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-observation-test</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-appoptics</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-atlas</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-azure-monitor</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-cloudwatch2</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-datadog</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-dynatrace</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-elastic</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-ganglia</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-graphite</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-health</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-humio</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-influx</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-jmx</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-kairos</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-new-relic</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-opentsdb</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-otlp</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus-simpleclient</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-signalfx</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-statsd</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-wavefront</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-test</artifactId>
        <version>1.14.5</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>context-propagation</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>docs</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-bridge-brave</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-bridge-otel</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-integration-test</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-reporter-wavefront</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-test</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>5.14.2</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-android</artifactId>
        <version>5.14.2</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-errorprone</artifactId>
        <version>5.14.2</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>5.14.2</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-proxy</artifactId>
        <version>5.14.2</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-subclass</artifactId>
        <version>5.14.2</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-buffer</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-dns</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-haproxy</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-http</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-http2</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-memcache</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-mqtt</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-redis</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-smtp</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-socks</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-stomp</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-xml</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-common</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-dev-tools</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler-proxy</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler-ssl-ocsp</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-rxtx</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-sctp</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-udt</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-example</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-all</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns-classes-macos</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns-native-macos</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns-native-macos</artifactId>
        <version>4.1.119.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns-native-macos</artifactId>
        <version>4.1.119.Final</version>
        <classifier>osx-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.119.Final</version>
        <classifier>linux-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.119.Final</version>
        <classifier>linux-riscv64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.119.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.119.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.119.Final</version>
        <classifier>osx-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-classes-epoll</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.119.Final</version>
        <classifier>linux-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.119.Final</version>
        <classifier>linux-riscv64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.119.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-classes-kqueue</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-kqueue</artifactId>
        <version>4.1.119.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-kqueue</artifactId>
        <version>4.1.119.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-kqueue</artifactId>
        <version>4.1.119.Final</version>
        <classifier>osx-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-classes</artifactId>
        <version>2.0.70.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.70.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.70.Final</version>
        <classifier>linux-x86_64-fedora</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.70.Final</version>
        <classifier>linux-aarch_64-fedora</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.70.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.70.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.70.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.70.Final</version>
        <classifier>linux-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.70.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.70.Final</version>
        <classifier>osx-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.70.Final</version>
        <classifier>windows-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-context</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-opentracing-shim</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-api</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-common</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-logging</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-logging-otlp</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-zipkin</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-extension-kotlin</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-extension-trace-propagators</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk-common</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk-logs</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk-metrics</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk-testing</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk-trace</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk-extension-autoconfigure</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk-extension-autoconfigure-spi</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-sdk-extension-jaeger-remote-sampler</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-otlp</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-otlp-common</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-sender-grpc-managed-channel</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-sender-jdk</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-exporter-sender-okhttp</artifactId>
        <version>1.43.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-config</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-core</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-exporter-common</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-exporter-httpserver</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-exporter-opentelemetry</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-exporter-pushgateway</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-exporter-servlet-jakarta</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-exporter-servlet-javax</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-exposition-formats</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-instrumentation-dropwizard</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-instrumentation-dropwizard5</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-instrumentation-caffeine</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-instrumentation-guava</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-instrumentation-jvm</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-model</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-simpleclient-bridge</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-tracer</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-tracer-common</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-tracer-initializer</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-tracer-otel</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>prometheus-metrics-tracer-otel-agent</artifactId>
        <version>1.3.6</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_caffeine</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_common</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_dropwizard</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_graphite_bridge</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_guava</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_hibernate</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_hotspot</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_httpserver</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_tracer_common</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_jetty</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_jetty_jdk8</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_log4j</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_log4j2</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_logback</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_pushgateway</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_servlet</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_servlet_jakarta</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_spring_boot</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_spring_web</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_tracer_otel</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_tracer_otel_agent</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_vertx</artifactId>
        <version>0.16.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>bouncy-castle-bc</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>bouncy-castle-bcfips</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>bouncy-castle-parent</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>buildtools</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>distribution</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>docker-images</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>jclouds-shaded</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>managed-ledger</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-all-docker-image</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-broker-auth-athenz</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-broker-auth-oidc</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-broker-auth-sasl</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-broker-common</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-broker</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-cli-utils</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-1x-base</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-1x</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-2x-shaded</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-admin-api</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-admin-original</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-admin</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-all</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-api</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-auth-athenz</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-auth-sasl</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-messagecrypto-bc</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-original</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-tools-api</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-tools</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-common</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-config-validation</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-docker-image</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-docs-tools</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-api-examples-builtin</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-api-examples</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-api</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-instance</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-local-runner-original</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-local-runner</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-proto</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-runtime-all</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-runtime</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-secrets</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-utils</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-worker</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-aerospike</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-alluxio</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-aws</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-batch-data-generator</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-batch-discovery-triggerers</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-canal</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-cassandra</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-common</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-core</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-data-generator</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-core</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-mongodb</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-mssql</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-mysql</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-oracle</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-postgres</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-distribution</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-docs</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-dynamodb</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-elastic-search</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-file</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-flume</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-hbase</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-hdfs3</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-http</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-influxdb</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-clickhouse</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-core</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-mariadb</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-openmldb</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-postgres</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-sqlite</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-kafka-connect-adaptor-nar</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-kafka-connect-adaptor</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-kafka</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-kinesis</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-mongo</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-netty</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-nsq</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-rabbitmq</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-redis</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-solr</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-twitter</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-metadata</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-offloader-distribution</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-package-bookkeeper-storage</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-package-core</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-package-filesystem-storage</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-package-management</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-proxy</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-server-distribution</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-shell-distribution</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-testclient</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-transaction-common</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-transaction-coordinator</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-transaction-parent</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-websocket</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>structured-event-log</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>testmocks</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>tiered-storage-file-system</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>tiered-storage-jcloud</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>tiered-storage-parent</artifactId>
        <version>3.3.5</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-core</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-codegen</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>codegen-utils</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-spatial</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-apt</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-collections</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-guava</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-sql</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-sql-spatial</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-sql-codegen</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-sql-spring</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-jpa</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-jpa-codegen</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-jdo</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-kotlin-codegen</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-lucene3</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-lucene4</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-lucene5</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-hibernate-search</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-mongodb</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-scala</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-kotlin</artifactId>
        <version>5.1.0</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-core</artifactId>
        <version>3.7.4</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-test</artifactId>
        <version>3.7.4</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-tools</artifactId>
        <version>3.7.4</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-core-micrometer</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-extra</artifactId>
        <version>3.5.2</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-adapter</artifactId>
        <version>3.5.2</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty-core</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty-http</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty-http-brave</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-pool</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-pool-micrometer</artifactId>
        <version>0.2.2</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.kotlin</groupId>
        <artifactId>reactor-kotlin-extensions</artifactId>
        <version>1.2.3</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.kafka</groupId>
        <artifactId>reactor-kafka</artifactId>
        <version>1.3.23</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>json-schema-validator</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>rest-assured-common</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>json-path</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>xml-path</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>rest-assured</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-commons</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-mock-mvc</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>scala-support</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>scala-extensions</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-web-test-client</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>kotlin-extensions</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-mock-mvc-kotlin-extensions</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-web-test-client-kotlin-extensions</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>rest-assured-all</artifactId>
        <version>5.5.1</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-core</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-load-balancer</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-micrometer</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-test</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-transport-local</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-transport-netty</artifactId>
        <version>1.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-api</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-chrome-driver</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-chromium-driver</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-devtools-v127</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-devtools-v128</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-devtools-v129</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-devtools-v85</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-edge-driver</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-firefox-driver</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-grid</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-http</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-ie-driver</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-java</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-json</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-manager</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-remote-driver</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-safari-driver</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-session-map-jdbc</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-session-map-redis</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-support</artifactId>
        <version>4.25.0</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-amqp</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit-junit</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit-stream</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit-test</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-core</artifactId>
        <version>5.2.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-infrastructure</artifactId>
        <version>5.2.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-integration</artifactId>
        <version>5.2.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-test</artifactId>
        <version>5.2.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-cassandra</artifactId>
        <version>4.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-commons</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-couchbase</artifactId>
        <version>5.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-elasticsearch</artifactId>
        <version>5.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-jdbc</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-r2dbc</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-relational</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-jpa</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-envers</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-mongodb</artifactId>
        <version>4.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-neo4j</artifactId>
        <version>7.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-redis</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-webmvc</artifactId>
        <version>4.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-core</artifactId>
        <version>4.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-hal-explorer</artifactId>
        <version>4.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-keyvalue</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-ldap</artifactId>
        <version>3.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aop</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aspects</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-beans</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context-indexer</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context-support</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-core</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-core-test</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-expression</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-instrument</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jcl</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jdbc</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jms</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-messaging</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-orm</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-oxm</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-r2dbc</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-test</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-tx</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webflux</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-websocket</artifactId>
        <version>6.2.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-amqp</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-camel</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-cassandra</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-core</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-debezium</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-event</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-feed</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-file</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ftp</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-graphql</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-groovy</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-hazelcast</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-http</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ip</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jdbc</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jms</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jmx</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jpa</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-kafka</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mail</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mongodb</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mqtt</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-r2dbc</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-redis</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-rsocket</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-scripting</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-sftp</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-smb</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-stomp</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-stream</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-syslog</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-test</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-test-support</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-webflux</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-websocket</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ws</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-xml</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-xmpp</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-zeromq</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-zip</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-zookeeper</artifactId>
        <version>6.4.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.pulsar</groupId>
        <artifactId>spring-pulsar</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.pulsar</groupId>
        <artifactId>spring-pulsar-cache-provider</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.pulsar</groupId>
        <artifactId>spring-pulsar-cache-provider-caffeine</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.pulsar</groupId>
        <artifactId>spring-pulsar-reactive</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.pulsar</groupId>
        <artifactId>spring-pulsar-test</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-asciidoctor</artifactId>
        <version>3.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-core</artifactId>
        <version>3.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-mockmvc</artifactId>
        <version>3.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-restassured</artifactId>
        <version>3.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-webtestclient</artifactId>
        <version>3.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-acl</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-aspects</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-cas</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-config</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-core</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-crypto</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-data</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-ldap</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-messaging</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-client</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-core</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-jose</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-resource-server</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-rsocket</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-saml2-service-provider</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-taglibs</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-test</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-web</artifactId>
        <version>6.4.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-core</artifactId>
        <version>3.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-mongodb</artifactId>
        <version>3.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-redis</artifactId>
        <version>3.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-hazelcast</artifactId>
        <version>3.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-jdbc</artifactId>
        <version>3.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-core</artifactId>
        <version>4.0.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-security</artifactId>
        <version>4.0.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-support</artifactId>
        <version>4.0.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-test</artifactId>
        <version>4.0.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-xml</artifactId>
        <version>4.0.12</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>activemq</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>azure</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>cassandra</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>chromadb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>clickhouse</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>cockroachdb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>consul</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>couchbase</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>cratedb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>database-commons</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>databend</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>db2</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>dynalite</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>gcloud</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>grafana</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>hivemq</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>influxdb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>jdbc</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>k3s</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>k6</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>kafka</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>ldap</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>localstack</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mariadb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>milvus</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>minio</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mockserver</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mongodb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mssqlserver</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>mysql</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>neo4j</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>nginx</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>oceanbase</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>ollama</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>openfga</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>oracle-free</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>oracle-xe</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>orientdb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>pinecone</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>postgresql</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>presto</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>pulsar</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>qdrant</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>questdb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>r2dbc</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>rabbitmq</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>redpanda</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>scylladb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>selenium</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>solace</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>solr</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>spock</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>tidb</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>timeplus</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>toxiproxy</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>trino</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>typesense</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>vault</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>weaviate</artifactId>
        <version>1.20.6</version>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>yugabytedb</artifactId>
        <version>1.20.6</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.7.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
