package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 到达详情对象，包含交通工具和运营公司信息。
 */
@Data
public class ArrivalDetailReq {

    /**
     * 交通工具代码（如 "14"），可选
     */
    @JsonProperty("transportation_code")
    private String transportationCode;

    /**
     * 到达交通工具编号（如航班号 "4293"），可选
     */
    @JsonProperty("number")
    private String number;

    /**
     * 运营公司代码（如 "FR"），可选
     */
    @JsonProperty("operating_company_code")
    private String operatingCompanyCode;

    /**
     * 运营公司代码上下文，可选
     */
    @JsonProperty("operating_company_code_context")
    private String operatingCompanyCodeContext;
}
