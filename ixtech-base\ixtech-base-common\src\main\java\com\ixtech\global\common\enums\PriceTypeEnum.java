package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计价方式枚举
 */
@Getter
@AllArgsConstructor
public enum PriceTypeEnum implements DictInf {

    PER_DAY(1, "按天计价");

    private final Integer value;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.value);
    }

    public static PriceTypeEnum fromCode(Integer code) {
        return DictInf.fromCode(PriceTypeEnum.values(), code);
    }
}
