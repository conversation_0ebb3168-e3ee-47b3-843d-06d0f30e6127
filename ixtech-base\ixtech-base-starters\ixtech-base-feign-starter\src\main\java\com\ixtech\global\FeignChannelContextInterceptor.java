package com.ixtech.global;

import com.ixtech.global.common.context.ChannelContextHolder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.util.ObjectUtils; // 导入 ObjectUtils

/**
 * 渠道上下文 feign拦截器
 */
public class FeignChannelContextInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        // 假设渠道 ID 从上下文中获取
        Long channelId = ChannelContextHolder.getChannelId();
        if (channelId != null) {
            template.header(ChannelContextHolder.CHANNEL_ID_HEADER, channelId.toString());
        }

        // 步骤 1 & 2: 获取并设置 Channel Code
        String channelCode = ChannelContextHolder.getChannelCode();
        if (!ObjectUtils.isEmpty(channelCode)) { // 检查渠道代码是否为空
            template.header(ChannelContextHolder.CHANNEL_CODE_HEADER, channelCode);
        }
    }
}
