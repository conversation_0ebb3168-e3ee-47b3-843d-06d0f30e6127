package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 渠道性质枚举
 * 对应表: rental_vendor_channel_config, 字段: nature
 * (1-默认, 2-自有品牌渠道)
 */
@Getter
@AllArgsConstructor
public enum ChannelNatureEnum implements DictInf {

    DEFAULT(1, "默认"),
    OWN_BRAND_CHANNEL(2, "自有品牌渠道"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }
}
