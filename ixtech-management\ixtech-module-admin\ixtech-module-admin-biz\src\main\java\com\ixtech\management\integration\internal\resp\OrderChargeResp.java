package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderChargeResp {
    private String projectName;
    private String receivableAmount; // 应收金额
    private BigDecimal receivedAmount; // 实收金额
    private String refundAmount;
    private String paymentMethod;
    private String status; // 状态
    private String operator; // 经办人
    private String operationTime; // 操作时间
}
