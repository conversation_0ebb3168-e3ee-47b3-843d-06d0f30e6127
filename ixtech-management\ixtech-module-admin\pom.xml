<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao</artifactId>
        <version>${revision}</version>
    </parent>
    <modules>
        <module>ixtech-module-admin-api</module>
        <module>ixtech-module-admin-biz</module>
    </modules>
    <artifactId>ixtech-module-admin</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>
        admin模块，主要实现ixtech平台订单、车辆等模块的管理功能。
    </description>

</project>