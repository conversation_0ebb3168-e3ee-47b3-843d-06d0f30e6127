package com.ixtech.management.common.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 *
 * system 系统，使用 1-002-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 订单模块 ==========
    ErrorCode INVALID_ORDER_STATUS = new ErrorCode(2_002_000_000, "订单状态非法");
    ErrorCode INVALID_TIME = new ErrorCode(2_002_000_001, "非法时间格式");
    ErrorCode INVALID_MODELID_STOREID = new ErrorCode(2_002_000_004, "车型和取车门店缺失");
    ErrorCode INVALID_CAR_STOCK_ID = new ErrorCode(2_002_000_005, "未查询到门店车型信息");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(2_002_000_007, "手机号不存在");
    ErrorCode AUTH_REGISTER_CAPTCHA_CODE_ERROR = new ErrorCode(2_002_000_008, "验证码不正确，原因：{}");

}
