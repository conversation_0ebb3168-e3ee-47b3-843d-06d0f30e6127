package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 租车响应对象，包含租车预订的返回信息。
 */
@Data
public class RentalResp {

    /**
     * 预订状态，例如 "Cancelled" 或 "Confirmed"。
     */
    @JsonProperty("reservation_status")
    private String reservationStatus;

    /**
     * 唯一标识符，例如订单 ID。
     */
    @JsonProperty("order_code")
    private String orderCode;

    /**
     * 订单确认码。
     */
    @JsonProperty("confirm_code")
    private String confirmCode;

    /**
     * 取车时间，格式为 yyyy-MM-dd HH:mm。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonProperty("pick_up_date_time")
    private LocalDateTime pickUpDateTime;

    /**
     * 还车时间，格式为 yyyy-MM-dd HH:mm。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonProperty("return_date_time")
    private LocalDateTime returnDateTime;

    /**
     * 取车地点信息。
     */
    @JsonProperty("pickup_location")
    private LocationResp pickupLocation;

    /**
     * 还车地点信息。
     */
    @JsonProperty("return_location")
    private LocationResp returnLocation;

    /**
     * 车辆信息。
     */
    @JsonProperty("vehicle")
    private VehicleResp vehicle;

    /**
     * 供应商信息。
     */
    @JsonProperty("vendor")
    private VendorResp vendor;
}
