package com.ixtech.global.integration.internal.client;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.config.FeignConfig;
import com.ixtech.global.integration.internal.req.OrderQueryReq;
import com.ixtech.global.integration.internal.req.RentalRequestReq;
import com.ixtech.global.integration.internal.resp.OrderCancelResp;
import com.ixtech.global.integration.internal.resp.RentalResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 车辆订单服务的 OpenFeign 客户端接口
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@FeignClient(name = "ordersrv", path = "/v1/ordersrv/internal/order", configuration = FeignConfig.class)
public interface CarOrderFeignClient {

    /**
     * 订单预订接口
     *
     * @param req 订单预订请求参数
     * @return 包含预订响应的 ApiResponse
     */
    @PostMapping("/veh_res")
    ApiResponse<RentalResp> vehRes(@RequestBody RentalRequestReq req);

    /**
     * 订单取消接口
     *
     * @param req 订单查询请求参数
     * @return 包含取消响应的 ApiResponse
     */
    @PostMapping("/veh_cancel")
    ApiResponse<OrderCancelResp> vehCancel(@RequestBody OrderQueryReq req);

    /**
     * 订单查询接口
     *
     * @param req 订单查询请求参数
     * @return 包含订单状态响应的 ApiResponse
     */
    @PostMapping("/veh_res_status_search")
    ApiResponse<RentalResp> vehResStatusSearch(@RequestBody OrderQueryReq req);
}
