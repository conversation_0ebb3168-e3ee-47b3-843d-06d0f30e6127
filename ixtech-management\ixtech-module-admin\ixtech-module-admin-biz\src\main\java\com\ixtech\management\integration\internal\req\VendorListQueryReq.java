package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.ixtech.management.common.dto.PageRequest;
import lombok.Data;

/**
 * 供应商列表请求Req
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:50
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VendorListQueryReq extends PageRequest {

    /**
     * 供应商id
     */
    private Long id;

    /**
     * 供应商简称
     */
    private String name;

    /**
     * 公司全称
     */
    private String companyFullName;

}
