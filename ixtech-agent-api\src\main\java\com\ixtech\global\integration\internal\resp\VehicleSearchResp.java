package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 车型搜索 resp
 *
 * @author: Phili
 * @date： 2025/3/31
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehicleSearchResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 是否配备空调
  */
 private Boolean airConditionInd;

 /**
  * 行李数量
  */
 private Integer baggageQuantity;

 /**
  * 车辆代码
  */
 private String code;

 /**
  * 车辆品牌
  */
 private String brand;

 /**
  * 代码上下文
  */
 private String codeContext;

 /**
  * 车辆描述
  */
 private String description;

 /**
  * 驱动类型
  */
 private String driveType;

 /**
  * 能源类型
  */
 private String energyType;

 /**
  * 车型年份
  */
 private Double modelYear;

 /**
  * 乘客数量
  */
 private Integer passengerQuantity;

 /**
  * 图片URL
  */
 private String pictureUrl;

 /**
  * 变速箱类型
  */
 private String transmissionType;

 /**
  * 车辆分类信息
  */
 private VehClass vehClass;

 /**
  * 车辆类型信息
  */
 private VehType vehType;

 /**
  * 车辆分类
  */
 @Data
 public static class VehClass {

  /**
   * 车辆尺寸
   */
  private String size;
 }

 /**
  * 车辆类型
  */
 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class VehType {

  /**
   * 车门数量
   */
  private Integer doorCount;

  /**
   * 车辆类别
   */
  private String vehicleCategory;
 }
}
