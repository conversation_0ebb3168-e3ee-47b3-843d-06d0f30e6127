package com.ixtech.global;

import com.ixtech.global.common.constant.IxtechConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * @description: Feign请求Header Config
 * @author: JP
 * @date： 2025/3/18
 */
public class FeignInterceptor implements RequestInterceptor {
 private static final String USER_SOURCE_HEADER = "X-User-Source";

 @Override
 public void apply(RequestTemplate template) {
  // 从请求上下文中获取当前请求
  ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
  HttpServletRequest request = attributes.getRequest();
  // 从请求属性中获取userType（假设已经在前置过滤器或拦截器中设置）
  Object userType = request.getAttribute(IxtechConstants.HEADER_USER_SOURCE);

  if (userType != null) {
   // 将userType添加到请求头
   template.header(USER_SOURCE_HEADER, userType.toString());
  }
 }
}
