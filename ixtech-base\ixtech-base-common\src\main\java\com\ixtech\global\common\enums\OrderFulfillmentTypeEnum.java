package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单履约类型枚举
 * (1-普通订单，2-送车上门，3-自主取还)
 */
@Getter
@AllArgsConstructor
public enum OrderFulfillmentTypeEnum implements DictInf {

    NORMAL(1, "普通订单"),
    DELIVERY(2, "送车上门"),
    SELF_SERVICE(3, "自主取还"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static OrderFulfillmentTypeEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}