package com.ixtech.management.integration.internal.client;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.integration.internal.req.DictTypeListReq;
import com.ixtech.management.integration.internal.req.MerchantVendorQueryReq;
import com.ixtech.management.integration.internal.req.common.SelectOption;
import com.ixtech.management.integration.internal.req.common.SelectOptionReq;
import com.ixtech.management.integration.internal.resp.DictTypeListResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * dict 服务 Feign 客户端接口
 */
@Component
@FeignClient(name = "vendormanagementsrv")
public interface VendormanagementsrvClientV2 {
    /**
     * 根据字典类型列表获取字典
     *
     * @param req 包含字典类型列表的请求
     * @return 字典数据
     */
    @PostMapping("/v2/vendormanagementsrv/internal/dict/list")
    ApiResponse<DictTypeListResp> getDictByTypeList(@RequestBody DictTypeListReq req);

    @PostMapping("/v2/vendormanagementsrv/internal/vendor/get_by_id")
    ApiResponse<com.ixtech.merchant.resp.IxVendorResp> getVendorByVendorId(@RequestBody MerchantVendorQueryReq req);

    @PostMapping("/v2/vendormanagementsrv/internal/vendor/get_store_list")
    ApiResponse<List<SelectOption>> getStoreList(@RequestBody SelectOptionReq req);

    @PostMapping("/v2/vendormanagementsrv/internal/vendor/get_vehicle_group_list")
    ApiResponse<List<SelectOption>> getVehicleGroupList(@RequestBody SelectOptionReq req);

    @PostMapping("/v2/vendormanagementsrv/internal/vendor/get_channel_list")
    ApiResponse<List<SelectOption>> getChannelList(SelectOptionReq req);
}
