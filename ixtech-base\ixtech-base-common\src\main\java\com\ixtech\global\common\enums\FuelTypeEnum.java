package com.ixtech.global.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FuelTypeEnum {

    GASOLINE(1, "汽油"),
    DIESEL_OIL(2, "柴油"),
    PURE_ELECTRIC(3, "纯电动"),
    HYBRID(4, "混动");

    /**
     * 类型值 (对应数据库中的值)
     */
    private final Integer value;

    /**
     * 类型名称 (中文描述)
     */
    private final String label;


    /**
     * 根据类型值获取对应的中文标签
     *
     * @return 对应的中文标签，如果值无效则返回null
     */
    public static String getLabelByCode(Integer value) {
        if (value == null) {
            return null;
        }
        for (FuelTypeEnum type : FuelTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type.getLabel();
            }
        }
        return null;
    }
}