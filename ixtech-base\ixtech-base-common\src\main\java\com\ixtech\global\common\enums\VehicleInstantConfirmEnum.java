package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否立即确认枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VehicleInstantConfirmEnum implements DictInf {

    /**
     * 无库存时手动确认
     */
    MANUAL_CONFIRM("0", "无库存时，手动确认"),


    /**
     * 立即确认
     */
    IMMEDIATE_CONFIRM("1", "立即确认");


    /**
     * 状态值
     */
    private final String value;
    
    /**
     * 状态名称
     */
    private final String label;

    public static String getLabelByValue(String value) {
        for (VehicleInstantConfirmEnum e : values()) {
            if (String.valueOf(e.value).equals(value)) {
                return e.label;
            }
        }
        return "";
    }
}