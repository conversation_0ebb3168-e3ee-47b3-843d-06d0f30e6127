package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.CarBrand;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:43
 */
@DS("ix")
@Mapper
public interface CarBrandMapper {

    int insert(CarBrand record);

    int insertSelective(CarBrand record);

    CarBrand selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CarBrand record);

    int updateByPrimaryKey(CarBrand record);

    int updateBatch(List<CarBrand> list);

    int updateBatchSelective(List<CarBrand> list);

    int batchInsert(@Param("list") List<CarBrand> list);

    /**
     * 批量查询品牌信息
     *
     * @param ids
     * @return
     */
    List<CarBrand> selectByPrimaryKeyList(@Param(value = "ids") Collection<Long> ids);

}