package com.ixtech.global.common.listener.validator;

import com.ixtech.global.common.listener.event.ApplicationStartCheckEvent;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.stereotype.Component;

/**
 * 应用服务启动校验
 *
 * <AUTHOR>
 * @date 2025-3-12
 */
@Component
public class ApplicationStartValidator implements ApplicationListener<ApplicationReadyEvent> {

 @Override
 public void onApplicationEvent(ApplicationReadyEvent event) {
  ConfigurableEnvironment environment = event.getApplicationContext().getEnvironment();
  // 获取应用服务名称
  String applicationName = environment.getProperty("spring.application.name");
  // 获取应用服务端口号
  String applicationPort = environment.getProperty("server.port");
  // 校验名称和端口号
  if (applicationName == null || applicationName.trim().isEmpty()) {
   throw new AssertionError("spring.application.name is not configured!");
  }
  if (applicationPort == null || applicationPort.trim().isEmpty()) {
   throw new AssertionError("server.port is not configured!");
  }
  // 发布自定义事件
  event.getApplicationContext().publishEvent(new ApplicationStartCheckEvent(this, applicationName, applicationPort));
 }

 @EventListener
 public void handleApplicationCheckEvent(ApplicationStartCheckEvent event) {
  System.out.println("Application name validated: " + event.getApplicationName());
  System.out.println("Application server port validated: " + event.getPort());
 }
}
