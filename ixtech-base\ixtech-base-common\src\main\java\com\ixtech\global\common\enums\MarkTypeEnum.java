package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 备注类型枚举
 * 对应表: platform_order_remark, 字段: mark_type
 * (1-客户备注，2-商户备注，3-渠道备注，4-运营平台备注)
 */
@Getter
@AllArgsConstructor
public enum MarkTypeEnum implements DictInf {

    CUSTOMER(1, "客户备注"),
    MERCHANT(2, "商户备注"),
    CHANNEL(3, "渠道备注"),
    PLATFORM(4, "运营平台备注"),
    DRIVER(5, "司机备注")
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static MarkTypeEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
