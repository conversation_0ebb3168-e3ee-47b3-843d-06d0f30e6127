package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * 任务导出状态枚举
 * 对应表: , 字段: status
 * 任务状态：0-进行中，1-成功，2-任务失败，3-有异常'
 */
@Getter
@AllArgsConstructor
public enum FileTaskStatusEnum implements DictInf {

    PROCESSING(0, "进行中"),
    SUCCESS(1, "成功"),
    FAIL(2, "任务失败"),
    ERROR(3, "有异常"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }



    public static String getNameByCode(Integer code){
        for (FileTaskStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getLabel();
            }
        }
        return "";
    }

}
