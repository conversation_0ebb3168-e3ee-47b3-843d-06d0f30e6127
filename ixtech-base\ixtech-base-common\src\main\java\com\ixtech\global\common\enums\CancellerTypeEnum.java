package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 取消方类型枚举
 * 对应表: platform_order_cancel, 字段: canceller_type
 * (1-客户，2-商户，3-运营平台，4-过期系统取消)
 */
@Getter
@AllArgsConstructor
public enum CancellerTypeEnum implements DictInf {

    CUSTOMER(1, "客户"),
    MERCHANT(2, "商户"),
    PLATFORM(3, "运营平台"),
    SYSTEM(4, "过期系统取消"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

}
