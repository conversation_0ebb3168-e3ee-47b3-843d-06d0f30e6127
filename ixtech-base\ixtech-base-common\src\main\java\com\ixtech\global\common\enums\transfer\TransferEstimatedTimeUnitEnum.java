package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 预计用时单位
 *
 * <AUTHOR> hu
 * @date 2025/7/23
 */
@Getter
public enum TransferEstimatedTimeUnitEnum {

    SECOND(1, "秒"),
    MINUTE(2, "分"),
    ;

    TransferEstimatedTimeUnitEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 单位编码
     */
    private final Integer code;

    /**
     * 单位描述
     */
    private final String value;
    // 新增：根据编码获取单位描述
    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferEstimatedTimeUnitEnum unit : values()) {
            if (unit.code.equals(code)) {
                return unit.value;
            }
        }
        return null; // 编码不存在时返回null
    }
}