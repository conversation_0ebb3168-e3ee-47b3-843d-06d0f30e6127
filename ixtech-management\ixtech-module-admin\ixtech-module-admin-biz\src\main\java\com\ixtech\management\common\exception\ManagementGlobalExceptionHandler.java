package com.ixtech.management.common.exception;

import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.ixtech.management.common.dto.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBufferLimitException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import static cn.iocoder.yudao.framework.web.core.handler.GlobalExceptionHandler.IGNORE_ERROR_MESSAGES;

@RestControllerAdvice
public class ManagementGlobalExceptionHandler {
    @Generated
    private static final Logger log = LoggerFactory.getLogger(ManagementGlobalExceptionHandler.class);

    @ExceptionHandler({DataBufferLimitException.class})
    public ApiResponse<?> dataBufferLimitExceptionHandler(DataBufferLimitException ex) {
        log.warn("[dataBufferLimitExceptionHandler]", ex);
        return ApiResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "请求体数据量过大！");
    }

    @ExceptionHandler({MaxUploadSizeExceededException.class})
    public ApiResponse<?> maxUploadSizeExceededExceptionHandler(MaxUploadSizeExceededException ex) {
        log.warn("[maxUploadSizeExceededExceptionHandler]", ex);
        return ApiResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "上传文件数据量过大！");
    }

    @ExceptionHandler({IllegalMonitorStateException.class})
    public ApiResponse<?> illegalMonitorStateExceptionHandler(IllegalMonitorStateException ex) {
        log.warn("[illegalMonitorStateExceptionHandler]", ex);
        return ApiResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "系统繁忙，请稍后重试！");
    }

    @ExceptionHandler({MissingServletRequestParameterException.class})
    public ApiResponse<?> missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException ex) {
        log.warn("[missingServletRequestParameterExceptionHandler]", ex);
        return ApiResponse.fail(HttpStatus.BAD_REQUEST.value(), String.format("请求参数缺失:%s", ex.getParameterName()));
    }

    @ExceptionHandler({MethodArgumentTypeMismatchException.class})
    public ApiResponse<?> methodArgumentTypeMismatchExceptionHandler(MethodArgumentTypeMismatchException ex) {
        log.warn("[missingServletRequestParameterExceptionHandler]", ex);
        return ApiResponse.fail(HttpStatus.BAD_REQUEST.value(), String.format("请求参数类型错误:%s", ex.getMessage()));
    }

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ApiResponse<?> methodArgumentNotValidExceptionExceptionHandler(MethodArgumentNotValidException ex) {
        log.warn("[methodArgumentNotValidExceptionExceptionHandler]", ex);
        FieldError fieldError = ex.getBindingResult().getFieldError();

        assert fieldError != null;

        return ApiResponse.fail(HttpStatus.BAD_REQUEST.value(), String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
    }

    @ExceptionHandler({BindException.class})
    public ApiResponse<?> bindExceptionHandler(BindException ex) {
        log.warn("[handleBindException]", ex);
        FieldError fieldError = ex.getFieldError();

        assert fieldError != null;

        return ApiResponse.fail(HttpStatus.BAD_REQUEST.value(), String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
    }

    @ExceptionHandler({BizException.class})
    public ApiResponse<?> serviceExceptionHandler(BizException ex) {
        log.info("[bizExceptionHandler]", ex);
        return ApiResponse.fail(ex.getCode(), ex.getMessage());
    }

    /**
     * 处理业务异常 ServiceException
     *
     * 例如说，商品库存不足，用户手机号已存在。
     */
    @ExceptionHandler(value = ServiceException.class)
    public CommonResult<?> serviceExceptionHandler(ServiceException ex) {
        // 不包含的时候，才进行打印，避免 ex 堆栈过多
        if (!IGNORE_ERROR_MESSAGES.contains(ex.getMessage())) {
            // 即使打印，也只打印第一层 StackTraceElement，并且使用 warn 在控制台输出，更容易看到
            try {
                StackTraceElement[] stackTraces = ex.getStackTrace();
                for (StackTraceElement stackTrace : stackTraces) {
                    if (ObjUtil.notEqual(stackTrace.getClassName(), ServiceExceptionUtil.class.getName())) {
                        log.warn("[serviceExceptionHandler]\n\t{}", stackTrace);
                        break;
                    }
                }
            } catch (Exception ignored) {
                // 忽略日志，避免影响主流程
            }
        }
        return CommonResult.error(ex.getCode(), ex.getMessage());
    }

    @ExceptionHandler({Exception.class})
    public ApiResponse<?> defaultExceptionHandler(HttpServletRequest req, Throwable ex) {
        log.warn("[defaultExceptionHandler]", ex);
        return ApiResponse.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage());
    }
}
