<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.AreaMapper">
    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.Area">
        <!--@mbg.generated-->
        <!--@Table jipinzuche_area-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="active" jdbcType="BIT" property="active"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="name_en" jdbcType="VARCHAR" property="nameEn"/>
        <result column="category" jdbcType="INTEGER" property="category"/>
        <result column="parentid" jdbcType="INTEGER" property="parentid"/>
        <result column="first_letter" jdbcType="VARCHAR" property="firstLetter"/>
        <result column="time_zone_city" jdbcType="VARCHAR" property="timeZoneCity"/>
        <result column="time_zone" jdbcType="REAL" property="timeZone"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, active, deleted, create_time, update_time, code, `name`, name_en, category, parentid,
        first_letter, time_zone_city, time_zone, `status`
    </sql>
    <sql id="Ignore_Deleted">
        status != -1
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from jipinzuche_area
        where id = #{id,jdbcType=INTEGER} AND
        <include refid="Ignore_Deleted"/>
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ixtech.management.repo.entity.Area"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_area (active, deleted, create_time,
        update_time, code, `name`,
        name_en, category, parentid,
        first_letter, time_zone_city, time_zone,
        `status`)
        values (#{active,jdbcType=BIT}, #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
        #{nameEn,jdbcType=VARCHAR}, #{category,jdbcType=INTEGER}, #{parentid,jdbcType=INTEGER},
        #{firstLetter,jdbcType=VARCHAR}, #{timeZoneCity,jdbcType=VARCHAR}, #{timeZone,jdbcType=REAL},
        #{status,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ixtech.management.repo.entity.Area"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="active != null">
                active,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="nameEn != null">
                name_en,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="parentid != null">
                parentid,
            </if>
            <if test="firstLetter != null">
                first_letter,
            </if>
            <if test="timeZoneCity != null">
                time_zone_city,
            </if>
            <if test="timeZone != null">
                time_zone,
            </if>
            <if test="status != null">
                `status`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="active != null">
                #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameEn != null">
                #{nameEn,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=INTEGER},
            </if>
            <if test="parentid != null">
                #{parentid,jdbcType=INTEGER},
            </if>
            <if test="firstLetter != null">
                #{firstLetter,jdbcType=VARCHAR},
            </if>
            <if test="timeZoneCity != null">
                #{timeZoneCity,jdbcType=VARCHAR},
            </if>
            <if test="timeZone != null">
                #{timeZone,jdbcType=REAL},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ixtech.management.repo.entity.Area">
        <!--@mbg.generated-->
        update jipinzuche_area
        <set>
            <if test="active != null">
                active = #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameEn != null">
                name_en = #{nameEn,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=INTEGER},
            </if>
            <if test="parentid != null">
                parentid = #{parentid,jdbcType=INTEGER},
            </if>
            <if test="firstLetter != null">
                first_letter = #{firstLetter,jdbcType=VARCHAR},
            </if>
            <if test="timeZoneCity != null">
                time_zone_city = #{timeZoneCity,jdbcType=VARCHAR},
            </if>
            <if test="timeZone != null">
                time_zone = #{timeZone,jdbcType=REAL},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ixtech.management.repo.entity.Area">
        <!--@mbg.generated-->
        update jipinzuche_area
        set active = #{active,jdbcType=BIT},
        deleted = #{deleted,jdbcType=BIT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        code = #{code,jdbcType=VARCHAR},
        `name` = #{name,jdbcType=VARCHAR},
        name_en = #{nameEn,jdbcType=VARCHAR},
        category = #{category,jdbcType=INTEGER},
        parentid = #{parentid,jdbcType=INTEGER},
        first_letter = #{firstLetter,jdbcType=VARCHAR},
        time_zone_city = #{timeZoneCity,jdbcType=VARCHAR},
        time_zone = #{timeZone,jdbcType=REAL},
        `status` = #{status,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update jipinzuche_area
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.code,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="name_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.nameEn,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.category,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="parentid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.parentid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="first_letter = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.firstLetter,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="time_zone_city = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.timeZoneCity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="time_zone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.timeZone,jdbcType=REAL}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update jipinzuche_area
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.active != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deleted != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.code != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.code,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="name_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.nameEn != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.nameEn,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.category != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.category,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="parentid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.parentid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.parentid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="first_letter = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.firstLetter != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.firstLetter,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="time_zone_city = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.timeZoneCity != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.timeZoneCity,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="time_zone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.timeZone != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.timeZone,jdbcType=REAL}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_area
        (active, deleted, create_time, update_time, code, `name`, name_en, category, parentid,
        first_letter, time_zone_city, time_zone, `status`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.active,jdbcType=BIT}, #{item.deleted,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.nameEn,jdbcType=VARCHAR}, #{item.category,jdbcType=INTEGER}, #{item.parentid,jdbcType=INTEGER},
            #{item.firstLetter,jdbcType=VARCHAR}, #{item.timeZoneCity,jdbcType=VARCHAR}, #{item.timeZone,jdbcType=REAL},
            #{item.status,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="selectByPrimaryKeyList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM jipinzuche_area
        <where>
            id IN
            <foreach item="item" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND
            <include refid="Ignore_Deleted"/>
        </where>
    </select>

    <select id="queryAllStoreArea" resultMap="BaseResultMap">
        SELECT
        DISTINCT a.id, a.name, a.parentid, a.category
        FROM jipinzuche_area a
        INNER JOIN jipinzuche_store s ON s.countryid = a.id OR s.provinceid = a.id OR s.cityid = a.id
        <where>
            a.
            <include refid="Ignore_Deleted"/>
            AND s.
            <include refid="Ignore_Deleted"/>
        </where>
    </select>

</mapper>