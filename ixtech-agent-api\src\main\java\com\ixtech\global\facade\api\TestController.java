package com.ixtech.global.facade.api;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.domain.service.ITestService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.ixtech.global.common.dto.ApiResponse.fail;
import static com.ixtech.global.common.dto.ApiResponse.success;

/**
 * 测试api
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {
    @Resource
    private ITestService testService;

    @GetMapping("/testConnect")
    public ApiResponse<Boolean> testConnect() {
        try {
            return success(testService.testQueryConnect());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return fail("连接异常");
        }
    }
}
