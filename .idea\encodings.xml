<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/ixtech-agent-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-agent-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-parent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-parent/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/ixtech-base-feign-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/ixtech-base-feign-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/ixtech-base-i18n-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/ixtech-base-i18n-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/ixtech-base-mysql-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/ixtech-base-mysql-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/ixtech-base-redis-starter/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/ixtech-base-redis-starter/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/ixtech-base-starters/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/ixtech-module-admin/ixtech-module-admin-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/ixtech-module-admin/ixtech-module-admin-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/ixtech-module-admin/ixtech-module-admin-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/ixtech-module-admin/ixtech-module-admin-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/ixtech-module-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/ixtech-module-admin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-biz-data-permission/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-biz-data-permission/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-biz-ip/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-biz-ip/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-biz-tenant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-biz-tenant/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-excel/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-excel/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-job/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-job/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-monitor/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-monitor/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-mq/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-mq/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-mybatis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-mybatis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-protection/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-protection/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-web/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-websocket/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-framework/yudao-spring-boot-starter-websocket/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-infra/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-infra/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-infra/yudao-module-infra-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-infra/yudao-module-infra-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-infra/yudao-module-infra-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-infra/yudao-module-infra-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-system/yudao-module-system-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-system/yudao-module-system-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-system/yudao-module-system-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-module-system/yudao-module-system-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-management/yudao-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/ixtech-module-merchant/ixtech-module-merchant-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/ixtech-module-merchant/ixtech-module-merchant-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/ixtech-module-merchant/ixtech-module-merchant-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/ixtech-module-merchant/ixtech-module-merchant-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/ixtech-module-merchant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/ixtech-module-merchant/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-module-infra/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-module-infra/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-module-infra/yudao-module-infra-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-module-infra/yudao-module-infra-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-module-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-module-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-module-system/yudao-module-system-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-module-system/yudao-module-system-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-merchants/yudao-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-order/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-order/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-product/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-product/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-translation/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-translation/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-vendor-basic/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-vendor-basic/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-vendor-management/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-vendor-management/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-biz-data-permission/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-biz-data-permission/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-biz-ip/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-biz-ip/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-biz-tenant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-biz-tenant/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-excel/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-excel/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-job/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-job/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-monitor/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-monitor/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-mq/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-mq/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-mybatis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-mybatis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-protection/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-protection/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-web/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-websocket/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-framework/yudao-spring-boot-starter-websocket/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-module-infra/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-module-infra/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-module-infra/yudao-module-infra-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-module-infra/yudao-module-infra-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-module-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-module-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-module-system/yudao-module-system-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/ixtech-yudao-base/yudao-module-system/yudao-module-system-api/src/main/resources" charset="UTF-8" />
  </component>
</project>