package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChannelCooperationStatusEnum implements DictInf {

    UNBOUND("0", "未绑定合作"),
    BOUND("1", "绑定合作");

    /**
     * 状态值
     */
    private final String value;

    /**
     * 状态名称
     */
    private final String label;
}
