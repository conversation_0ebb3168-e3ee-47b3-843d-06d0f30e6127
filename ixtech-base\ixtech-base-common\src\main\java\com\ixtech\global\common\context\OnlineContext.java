package com.ixtech.global.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


/**
 * 上下文
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Slf4j
public final class OnlineContext {

    private static final ThreadLocal<Subject> SUBJECT = new TransmittableThreadLocal<>();


    public static Subject getSubject() {
        return SUBJECT.get();
    }

    public static void setSubject(Subject subject) {
        SUBJECT.set(subject);
    }

    public static Subject init() {
        clear();
        Subject subject = new Subject();
        SUBJECT.set(subject);
        return subject;
    }

    public static void clear() {
        SUBJECT.remove();
    }


    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class Subject {

        // 用户id，默认值-1
        private Long userId = -1L;

        // 用户渠道
        private String userSource;

        // 访问平台
        private Integer platform;

        // 渠道id
        private Integer sourceId;

    }

}
