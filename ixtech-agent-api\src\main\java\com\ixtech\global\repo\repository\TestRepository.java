package com.ixtech.global.repo.repository;

import com.ixtech.global.repo.entity.User;
import com.ixtech.global.repo.mapper.TestMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户查询测试Repository
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Repository
public class TestRepository {

    @Resource
    private TestMapper testMapper;

    /**
     * 根据参数查询用户信息
     * @param first 起点
     * @param limit 限制数量
     * @return 用户信息
     */
    public List<User> queryUserByParams(int first, int limit) {
        return testMapper.selectUsersByParams(first, limit);
    }
}
