package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.JipinzucheCarModel;
import com.ixtech.management.repo.mapper.JipinzucheCarModelMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class JipinzucheCarModelRepository {

    @Resource
    private JipinzucheCarModelMapper jipinzucheCarModelMapper;

    public JipinzucheCarModel selectById(Long id){
        return jipinzucheCarModelMapper.selectById(id);
    }

    public List<JipinzucheCarModel> selectAll() {
        return jipinzucheCarModelMapper.selectAll();
    }
}
