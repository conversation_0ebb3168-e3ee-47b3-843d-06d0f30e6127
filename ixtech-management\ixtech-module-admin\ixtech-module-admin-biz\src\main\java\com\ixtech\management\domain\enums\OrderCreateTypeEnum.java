package com.ixtech.management.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum OrderCreateTypeEnum {

    USER_CREATE(0, "手动创建"),

    API_CREATE(1,"系统创建"),

    IMPORT_CREATE(2, "系统创建");


    private final int name;
    private final String code;

//    private static final Map<String, OrderCreateTypeEnum> CODE_MAP = Arrays.stream(values())
//            .collect(Collectors.toMap(OrderCreateTypeEnum::getCode, Function.identity()));

    private static final Map<Integer, OrderCreateTypeEnum> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(OrderCreateTypeEnum::getName, Function.identity()));

    OrderCreateTypeEnum(int name, String code) {
        this.name = name;
        this.code = code;
    }

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 对应的枚举，找不到返回null
     */
//    public static OrderCreateTypeEnum getByCode(String code) {
//        return CODE_MAP.get(code);
//    }

    /**
     * 根据名称获取枚举
     * @param name 名称
     * @return 对应的枚举，找不到返回null
     */
    public static OrderCreateTypeEnum getByName(Integer name) {
        return NAME_MAP.get(name);
    }

    /**
     * 检查编码是否存在
     * @param code 编码
     * @return 是否存在
     */
//    public static boolean containsCode(String code) {
//        return CODE_MAP.containsKey(code);
//    }

    /**
     * 检查名称是否存在
     * @param name 名称
     * @return 是否存在
     */
    public static boolean containsName(Integer name) {
        return NAME_MAP.containsKey(name);
    }

    /**
     * 获取所有编码
     * @return 编码数组
     */
    public static String[] getAllCodes() {
        return Arrays.stream(values()).map(OrderCreateTypeEnum::getCode).toArray(String[]::new);
    }

    /**
     * 获取所有名称
     * @return 名称数组
     */
    public static Integer[] getAllNames() {
        return Arrays.stream(values()).map(OrderCreateTypeEnum::getName).toArray(Integer[]::new);
    }

    @Override
    public String toString() {
        return "OrderCreateTypeEnum{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
