package com.ixtech.mybatis.handler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ixtech.global.common.utils.JsonUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.util.List;
@MappedTypes(List.class)
public class GenericListJsonTypeHandler<T> extends BaseTypeHandler<List<T>> {
    private final Class<T> elementType;

    public GenericListJsonTypeHandler(Class<T> elementType) {
        this.elementType = elementType;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType) throws SQLException {
        String json = JsonUtils.stringify(parameter);
        ps.setString(i, json);
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<T> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<T> parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        return JsonUtils.parseArray(json, elementType);
    }
}