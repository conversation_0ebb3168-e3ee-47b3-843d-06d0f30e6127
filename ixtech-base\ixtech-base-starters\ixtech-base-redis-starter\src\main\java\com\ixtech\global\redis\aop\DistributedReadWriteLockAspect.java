package com.ixtech.global.redis.aop;

import com.ixtech.global.common.exception.ServerException;
import com.ixtech.global.redis.annotation.DistributedReadWriteLock;
import com.ixtech.global.redis.enums.LockType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

import static com.ixtech.global.common.exception.enums.CommonErrorCode.LOCK_FAILED;

@Aspect
@Component
@Slf4j
@ConditionalOnClass(Aspect.class)
@RequiredArgsConstructor
public class DistributedReadWriteLockAspect {

    private final RedissonClient redissonClient;

    private static final ExpressionParser PARSER = new SpelExpressionParser();
    private static final DefaultParameterNameDiscoverer DISCOVERER = new DefaultParameterNameDiscoverer();
    private String lockKeyPrefix = "lock:rw:"; // 读写锁使用不同的前缀

    @Around("@annotation(lockAnnotation)")
    public Object around(ProceedingJoinPoint joinPoint, DistributedReadWriteLock lockAnnotation) throws Throwable {
        // 1. 解析 SpEL 表达式，生成锁 key 的动态部分
        String dynamicKeyPart = parseDynamicKey(lockAnnotation.key(), joinPoint);
        if (!StringUtils.hasText(dynamicKeyPart)) {
            log.warn("DistributedReadWriteLock dynamic key part is empty, skipping lock. Method: {}", joinPoint.getSignature().toShortString());
            return joinPoint.proceed();
        }

        // 2. 获取读写锁实例
        String lockKey = lockKeyPrefix + dynamicKeyPart;
        RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(lockKey);

        // 3. 根据注解指定的类型获取对应的锁（读锁或写锁）
        RLock lock = (lockAnnotation.type() == LockType.READ)
                ? readWriteLock.readLock()
                : readWriteLock.writeLock();

        // 4. 尝试加锁
        boolean isLocked = false;
        try {
            if (lockAnnotation.waitTime() == -1) {
                isLocked = lock.tryLock();
            } else {
                isLocked = lock.tryLock(lockAnnotation.waitTime(), lockAnnotation.leaseTime(), lockAnnotation.unit());
            }

            if (!isLocked) {
                log.warn("Failed to acquire distributed read-write lock, key: {}, type: {}", lockKey, lockAnnotation.type());
                throw ServerException.of(LOCK_FAILED);
            }

            // 5. 加锁成功，执行业务方法
            return joinPoint.proceed();
        } finally {
            // 6. 释放锁
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private String parseDynamicKey(String keyExpression, ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        EvaluationContext context = new MethodBasedEvaluationContext(null, method, args, DISCOVERER);
        try {
            return PARSER.parseExpression(keyExpression).getValue(context, String.class);
        } catch (Exception e) {
            log.error("Error parsing SpEL for distributed read-write lock key: {}", keyExpression, e);
            throw new IllegalArgumentException("Failed to parse SpEL for key: " + keyExpression, e);
        }
    }
}
