package com.ixtech.management.facade.api;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.CarStockInsuranceService;
import com.ixtech.management.domain.service.ModelService;
import com.ixtech.management.integration.internal.req.CarStockListReq;
import com.ixtech.management.integration.internal.resp.ModelSelectOptionResponse;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 供应商管理
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:15
 */
@RestController
@RequestMapping("/v1/management/internal/car_stock_ins")
public class CarStockInsuranceController {

    @Resource
    private CarStockInsuranceService carStockInsuranceService;

    /**
     * 车型保险下拉列表
     *
     * @return
     */
    @PermitAll
    @PostMapping(value = "/dropdown_list")
    public ApiResponse<List<SelectOptionResponse<Long>>> dropdownList(@RequestBody CarStockListReq req) {

        List<SelectOptionResponse<Long>> result = carStockInsuranceService.dropdownList(req);
        return ApiResponse.success(result);
    }

}
