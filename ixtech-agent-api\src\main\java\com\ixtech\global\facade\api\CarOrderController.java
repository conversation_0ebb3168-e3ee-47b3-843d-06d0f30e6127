package com.ixtech.global.facade.api;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.util.ErrorRespUtils;
import com.ixtech.global.domain.service.CarOrderService;
import com.ixtech.global.integration.internal.req.OrderQueryReq;
import com.ixtech.global.integration.internal.req.RentalRequestReq;
import com.ixtech.global.integration.internal.resp.OrderCancelResp;
import com.ixtech.global.integration.internal.resp.RentalResp;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * agentapi 订单 controller
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@RestController
@RequestMapping("/agentopenapi/api")
@Slf4j
public class CarOrderController {

    //全局计数器-成功
    private static final Counter VEH_RES_SUCCESS_COUNTER = Counter
            .builder("agent_veh_res_success_count")  // 指标名
            .description("Total number of success agent veh_res")  // 描述
            .tag("result", "agent_veh_res_success")          // 自定义标签
            .register(Metrics.globalRegistry);  // 注册到全局Registry

    //全局计数器-失败
    private static final Counter VEH_RES_FAILED_COUNTER = Counter
            .builder("agent_veh_res_failed_count")  // 指标名
            .description("Total number of failed agent_veh_res")  // 描述
            .tag("result", "agent_veh_res_failed")          // 自定义标签
            .register(Metrics.globalRegistry);  // 注册到全局Registry


    @Resource(name = "orderServiceV2")
    private CarOrderService carOrderService;

    /**
     * 订单预订 接口
     *
     * @param req 请求
     * @return 响应
     */
    @PostMapping("/veh_res")
    @Timed(
            value = "agent_veh_res_request_seconds",
            description = "veh_res request latency",
            extraTags = {"api_url", "/agentopenapi/api/veh_res"},  // 与实际路径一致
            histogram = true
    )
    public ApiResponse<RentalResp> vehRes(@RequestBody @Valid RentalRequestReq req) throws InterruptedException {
        log.info("JipinzucheCarOrderController::vehRes:::{}", req);
        ApiResponse<RentalResp> resp = carOrderService.vehRes(req);
        if(resp.isSuccess()){
            VEH_RES_SUCCESS_COUNTER.increment();
        } else {
            VEH_RES_FAILED_COUNTER.increment();
        }
        return ErrorRespUtils.checkServiceResponse(resp, "订单预订失败");
    }

    /**
     * 订单取消 接口
     *
     * @param req 请求
     * @return 响应
     */
    @PostMapping("/veh_cancel")
    public ApiResponse<OrderCancelResp> vehCancel(@RequestBody @Valid OrderQueryReq req) throws InterruptedException {
        log.info("JipinzucheCarOrderController::vehCancel:::{}", req);
        ApiResponse<OrderCancelResp> resp = carOrderService.vehCancel(req);
        return ErrorRespUtils.checkServiceResponse(resp, "订单取消失败");
    }

    /**
     * 订单查询 接口
     *
     * @param req 请求
     * @return 响应
     */
    @PostMapping("/veh_res_status_search")
    public ApiResponse<RentalResp> vehResStatusSearch(@RequestBody @Valid OrderQueryReq req) {
        log.info("JipinzucheCarOrderController::vehResStatusSearch:::{}", req);
        ApiResponse<RentalResp> resp = carOrderService.vehResStatusSearch(req);
        return ErrorRespUtils.checkServiceResponse(resp, "订单查询失败");
    }

}
