package com.ixtech.global.feign.client;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.config.TranslationFeignClientConfiguration;
import com.ixtech.global.feign.req.TranslationReq;
import com.ixtech.global.feign.resp.TranslationResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用翻译服务的FeignClient
 *
 * <AUTHOR> hu
 * @date 2025/6/10 14:27
 */
@FeignClient(name = "translation", url = "${i18n.url}/v1/translation/internal/translate", configuration = TranslationFeignClientConfiguration.class)
public interface TranslationFeignClient {

    /**
     * 获取指定翻译资源key在指定语言下的翻译结果
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/lang_trans")
    ApiResponse<TranslationResp> langTrans(@RequestBody TranslationReq req);

}
