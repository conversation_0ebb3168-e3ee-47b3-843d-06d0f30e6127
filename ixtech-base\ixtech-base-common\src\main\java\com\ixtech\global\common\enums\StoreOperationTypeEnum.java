package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StoreOperationTypeEnum implements DictInf {

    SET_CHANNELS_OFFLINE("0","下线"),

    SET_CHANNELS_ONLINE("1","上线"),

    DISABLE_STORE("2","禁用");

    /**
     * 状态值
     */
    private final String value;

    /**
     * 状态名称
     */
    private final String label;
}
