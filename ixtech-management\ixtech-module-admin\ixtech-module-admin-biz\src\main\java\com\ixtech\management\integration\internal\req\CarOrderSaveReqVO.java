package com.ixtech.management.integration.internal.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "管理后台 - 车辆订单新增/修改 Request VO")
@Data
public class CarOrderSaveReqVO {
    private Integer id;
    private String ordercode;
    private Integer appointCarid;
    private Integer appointStockid;
    private Integer appointGetstoreid;
    private Integer appointReturnstoreid;
    private String appointGetstorename;
    private String appointReturnstorename;
    private String appointCarname;
    private String appointCarimg;
    private String appointPlatenumber;
    private Integer appointStarttime;
    private Integer appointEndtime;
    private Integer appointDays;
    private String ticketAddress;
    private Integer time;
    private String ip;
    private Integer source;
    private Integer ordertype;
    private String sourceOrdercode;
    private String note;
    private Integer mid;
    private Integer insurance;
    private Integer stockInsId;
    private String stockInsTitle;
    private Integer sex;
    private String surname;
    private String username;
    private String mobile;
    private String email;
    private String passportnum;
    private String appointCarcolor;
    private String appointCarcode;
    private Double price;
    private Double insuranceprice;
    private Double totalprice;
    private Double payprice;
    private Double localPayprice;
    private Integer paytime;
    private Integer paytype;
    private Double deposit;
    private Integer depositPaytype;
    private Integer confirmtime;
    private String confirmip;
    private Integer confirmmid;
    private String contactline;
    private String confirmcode;
    private Integer status;
    private Integer getcartime;
    private Integer realGetcartime;
    private Integer getcarmid;
    private Integer carid;
    private Integer stockid;
    private String carname;
    private String carimg;
    private String carcode;
    private String carcolor;
    private String platenumber;
    private Integer returntime;
    private Integer realReturntime;
    private Integer returnmid;
    private Integer returnstoreid;
    private String returnstorename;
    private Integer orderEndtime;
    private Integer orderEndmid;
    private Double mileage;
    private Double returnDeposit;
    private Integer returnDepositStatus;
    private Integer returnDepositTime;
    private Integer returnDepositMid;
    private Integer canceltime;
    private Integer cancelmid;
    private Double cancelprice;
    private Integer edittime;
    private Integer editmid;
    private Integer deltime;
    private Integer delmid;
    private Double extraprice;
    private String extranote;
    private Integer sesame;
    private Integer grade;
    private Double otherprice;
    private String othernote;
    private String licensePic;
    private Integer canceledstatus;
    private Integer canceledmid;
    private Double discountprice;
    private Double daymileage;
    private String morenote;
    private Integer refusetime;
    private Integer refusemid;
    private String childseat;
    private Double childseatprice;
    private Boolean childseatPrepay;
    private Double overtimecost;
    private Boolean overtimePrepay;
    private Double carreturnPrice;
    private Double ctripprice;
    private Integer ordertypes;
    private String discountCode;
    private String cardnumber;
    private String termofvalidity;
    private Integer cvvnumbber;
    private Double exchangeRate;
    private Double discountDayprice;
    private String unit;
    private Boolean isRate;
    private String rateCode;
    private Boolean active;
    private Long orderRangeId;

}