package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 车辆分配方式枚举
 *
 * <AUTHOR> hu
 * @date 2025/7/23
 */
@Getter
public enum TransferDispatchTypeEnum {

    MANUAL_ASSIGN(1, "手动分配"),
    AUTO_ASSIGN(2, "自动分配"),
    ;

    TransferDispatchTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 分配方式编码
     */
    private final Integer code;

    /**
     * 分配方式描述
     */
    private final String value;
}
    