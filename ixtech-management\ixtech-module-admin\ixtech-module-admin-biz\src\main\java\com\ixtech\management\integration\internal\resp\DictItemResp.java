package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字典项响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DictItemResp {
    private String label;
    private String value;

//    public static DictItemResp from(DictInf dictInf) {
//        return new DictItemResp(dictInf.getLabel(), dictInf.getValue());
//    }
}