package com.ixtech.global.common.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @description:
 * @author: JP
 * @date： 2025/3/20 9:26
 */
public class Md5Utils {
 private static final char[] HEX_DIGITS = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

 public Md5Utils() {
 }

 public static String getMD5String(String str) {
  MessageDigest messageDigest = null;

  try {
   messageDigest = MessageDigest.getInstance("MD5");
  } catch (NoSuchAlgorithmException var3) {
   var3.printStackTrace();
   return null;
  }

  messageDigest.update(str.getBytes());
  return byteArray2HexString(messageDigest.digest());
 }

 private static String byteArray2HexString(byte[] bytes) {
  StringBuilder sb = new StringBuilder();
  byte[] var2 = bytes;
  int var3 = bytes.length;

  for(int var4 = 0; var4 < var3; ++var4) {
   byte b = var2[var4];
   sb.append(HEX_DIGITS[(b & 240) >> 4]).append(HEX_DIGITS[b & 15]);
  }

  return sb.toString();
 }

}
