package com.ixtech.management.integration.internal.req;

import lombok.Data;

import java.util.List;

@Data
public class CarOrderQuery {

    private String createTimeStart;

    private String createTimeEnd;

    private String sourceOrdercode;

    private List<Integer> createTypes;

    private Integer source;

    private List<Long> appointGetstoreids;

    private List<Integer> orderStatus;

    private Integer isOrderLaterTime;

    private Integer createTimeOrderBy;

    private Integer appointStartTimeOrderBy;

    private Integer appointEndTieOrderBy;

    private Integer idOrderBy;

    // 0:asc, 1:desc
    private Integer ascOrDesc;

    // 分页
    private Long limitStart;

    private Long limitSize;

}
