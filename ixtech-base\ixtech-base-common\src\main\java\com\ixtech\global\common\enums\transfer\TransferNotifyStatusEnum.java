package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 回调通知状态枚举
 *
 * <AUTHOR> hu
 * @date 2025/7/23
 */
@Getter
public enum TransferNotifyStatusEnum {

    PENDING(1, "待通知"),
    NOTIFIED(2, "已通知"),
    NOTIFY_FAILED(3, "通知失败"),
    ;

    TransferNotifyStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 状态编码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String value;
}
    