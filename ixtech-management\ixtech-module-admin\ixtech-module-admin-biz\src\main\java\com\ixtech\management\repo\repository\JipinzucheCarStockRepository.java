package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.JipinzucheCarStock;
import com.ixtech.management.repo.mapper.JipinzucheCarStockMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class JipinzucheCarStockRepository {

    @Resource
    private JipinzucheCarStockMapper jipinzucheCarStockMapper;

    public JipinzucheCarStock selectById(Long id){
        return jipinzucheCarStockMapper.selectById(id);
    }

    public List<JipinzucheCarStock> selectByModelId(Long id) {
        return jipinzucheCarStockMapper.selectByModelId(id);
    }

    public List<JipinzucheCarStock> selectByModelIdAndStoreId(Long modelid, Long storeid) {
        return jipinzucheCarStockMapper.selectByModelIdAndStoreId(modelid, storeid);
    }
}
