package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.JipinzucheCarList;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【jipinzuche_car_list(车辆列表)】的数据库操作Mapper
* @createDate 2025-04-20 23:08:12
* @Entity com.ixtech.management.repo.entity.JipinzucheCarList
*/
@DS("ix")
@Mapper
public interface JipinzucheCarListMapper {

    JipinzucheCarList selectById(Long id);

    List<JipinzucheCarList> selectByStockId(Integer id);
}




