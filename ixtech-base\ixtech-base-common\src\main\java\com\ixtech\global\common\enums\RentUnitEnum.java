package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 租期单位枚举
 */
@Getter
@AllArgsConstructor
public enum RentUnitEnum implements DictInf {

    DAY(1, "天"),
    HOUR(2, "小时");

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }
}
