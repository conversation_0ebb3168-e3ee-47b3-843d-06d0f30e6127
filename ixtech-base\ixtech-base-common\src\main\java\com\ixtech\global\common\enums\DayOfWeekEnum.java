package com.ixtech.global.common.enums;


import com.ixtech.global.common.enums.inf.DictInf;
import lombok.Getter;

import java.time.DayOfWeek;

@Getter
public enum DayOfWeekEnum implements DictInf {
    MON("MON", "周一"),
    TUE("TUE", "周二"),
    WED("WED", "周三"),
    THU("THU", "周四"),
    FRI("FRI", "周五"),
    SAT("SAT", "周六"),
    SUN("SUN", "周日");

    private final String value;
    private final String label;

    DayOfWeekEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    // 映射 java.time.DayOfWeek 到 DayOfWeekEnum
    public static DayOfWeekEnum mapToDayOfWeekEnum(DayOfWeek dayOfWeek) {
        return switch (dayOfWeek) {
            case MONDAY -> DayOfWeekEnum.MON;
            case TUESDAY -> DayOfWeekEnum.TUE;
            case WEDNESDAY -> DayOfWeekEnum.WED;
            case THURSDAY -> DayOfWeekEnum.THU;
            case FRIDAY -> DayOfWeekEnum.FRI;
            case SATURDAY -> DayOfWeekEnum.SAT;
            case SUNDAY -> DayOfWeekEnum.SUN;
        };
    }
}
