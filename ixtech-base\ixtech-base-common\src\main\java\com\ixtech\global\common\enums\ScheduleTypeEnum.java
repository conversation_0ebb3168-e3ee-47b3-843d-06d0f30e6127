package com.ixtech.global.common.enums;

import lombok.Getter;

/**
 * 营业计划类型枚举
 */
@Getter
public enum ScheduleTypeEnum {

    SPECIAL_OPERATING_TIME(1, "特殊营业时间"),
    SUSPENDED_OPERATING_TIME(2, "暂停营业时间");

    /**
     * 值
     */
    private final Integer value;

    /**
     * 标签
     */
    private final String label;

    ScheduleTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }
}
