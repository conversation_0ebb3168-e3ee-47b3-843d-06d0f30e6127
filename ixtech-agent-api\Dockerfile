FROM maven:3.9.9-eclipse-temurin-21-jammy AS builder

# 复制自定义 settings.xml 文件到容器
ADD ./settings.xml settings.xml

# 添加 pom.xml 和源码
ADD ./pom.xml pom.xml
ADD ./src src/

# 使用自定义 settings.xml 打包
RUN mvn clean -DskipTests=true package --settings ./settings.xml

# Second stage: minimal runtime environment
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/eclipse-temurin:21-jdk-jammy

# 复制 JAR 文件和配置文件
COPY --from=builder ./target/*.jar ixtech-agent-api.jar

# 暴露端口 (根据实际应用修改)
EXPOSE 8080

# 启动应用 (添加 -XX:+EnableDynamicAgentLoading 以支持 JDK 21 的动态代理)
ENTRYPOINT ["java", "-XX:+EnableDynamicAgentLoading", "-jar", "/ixtech-agent-api.jar", "--spring.profiles.active=prod"]
