package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.JipinzucheCarList;
import com.ixtech.management.repo.entity.JipinzucheCarStock;
import com.ixtech.management.repo.mapper.JipinzucheCarListMapper;
import com.ixtech.management.repo.mapper.JipinzucheCarStockMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class JipinzucheCarListRepository {

    @Resource
    private JipinzucheCarListMapper jipinzucheCarListMapper;

    public JipinzucheCarList selectById(Long id){
        return jipinzucheCarListMapper.selectById(id);
    }

    public List<JipinzucheCarList> selectByStockId(Integer id) {
        return jipinzucheCarListMapper.selectByStockId(id);
    }
}
