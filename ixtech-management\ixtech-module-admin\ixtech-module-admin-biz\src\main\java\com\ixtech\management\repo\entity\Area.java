package com.ixtech.management.repo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 区域实体
 *
 * <AUTHOR> hu
 * @date 2025/4/12 17:38
 */
@Data
@NoArgsConstructor
public class Area extends BaseEntity {

    /**
     * 地区代码
     */
    private String code;

    /**
     * 国家名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String nameEn;

    /**
     * 1->国家；2->省；3->市；3->区
     */
    private Integer category;

    /**
     * 父id
     */
    private Long parentid;

    /**
     * 首字母
     */
    private String firstLetter;

    /**
     * 时区代表城市
     */
    private String timeZoneCity;

    /**
     * 时区
     */
    private Float timeZone;

    /**
     * 1->有效；-1->删除
     */
    private Integer status;

}