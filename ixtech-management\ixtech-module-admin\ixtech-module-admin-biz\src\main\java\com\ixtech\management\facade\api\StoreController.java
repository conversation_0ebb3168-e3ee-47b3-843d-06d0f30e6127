package com.ixtech.management.facade.api;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.StoreService;
import com.ixtech.management.integration.internal.req.StoreDropdownReq;
import com.ixtech.management.integration.internal.req.StoreListQueryReq;
import com.ixtech.management.integration.internal.resp.AreaSelectOptionResp;
import com.ixtech.management.integration.internal.resp.StoreInfoResp;
import com.ixtech.management.integration.internal.resp.StoreListInfoResp;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 门店管理
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:16
 */
@RestController
@RequestMapping("/v1/management/internal/store")
public class StoreController {

    @Resource
    private StoreService storeService;

    /**
     * 查询门店列表
     *
     * @param storeListQueryReq 条件筛选参数
     * @return
     */
    @PermitAll
    @PostMapping(value = "/list")
    public ApiResponse<PageResponse<StoreListInfoResp>> list(@RequestBody StoreListQueryReq storeListQueryReq) {
        PageResponse<StoreListInfoResp> result = storeService.list(storeListQueryReq);
        return ApiResponse.success(result);
    }

    /**
     * 查询门店信息
     *
     * @param id 门店id
     * @return
     */
    @PermitAll
    @GetMapping(value = "/info")
    public ApiResponse<StoreInfoResp> info(@RequestParam(value = "id") Long id) {
        StoreInfoResp info = storeService.info(id);
        return ApiResponse.success(info);
    }

    /**
     * 获取门店下拉列表
     *
     * @param storeDropdownReq
     * @return
     */
    @PermitAll
    @PostMapping(value = "/dropdown_list")
    public ApiResponse<List<SelectOptionResponse<Long>>> dropdownList(@RequestBody StoreDropdownReq storeDropdownReq) {
        List<SelectOptionResponse<Long>> result = storeService.dropdownList(storeDropdownReq);
        return ApiResponse.success(result);
    }

    /**
     * 获取门店地区下拉列表
     *
     * @return
     */
    @PermitAll
    @GetMapping(value = "/area_dropdown_list")
    public ApiResponse<List<AreaSelectOptionResp>> areaDropdownList() {
        List<AreaSelectOptionResp> result = storeService.areaDropdownList();
        return ApiResponse.success(result);
    }

}
