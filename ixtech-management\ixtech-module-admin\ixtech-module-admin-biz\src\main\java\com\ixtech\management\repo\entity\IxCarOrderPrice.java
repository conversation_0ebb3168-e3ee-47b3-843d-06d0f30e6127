package com.ixtech.management.repo.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单-金额信息表
 * @TableName ix_car_order_price
 */
@Data
public class IxCarOrderPrice implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * 订单id
     */
    private Long carOrderId;

    /**
     * 预付金额（含加价）
     */
    private BigDecimal prepaidAmount;

    /**
     * 到付金额（含加价）
     */
    private BigDecimal cashOnDeliveryAmount;

    /**
     * 单程费
     */
    private BigDecimal oneWayFee;

    /**
     * 特殊取车时间费
     */
    private BigDecimal specialPickupTimeFee;

    /**
     * 特殊还车时间费
     */
    private BigDecimal specialReturnTimeFee;

    /**
     * 平台险购买状态 1：是 0：否
     */
    private Integer buyInsurance;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 当地货币代码
     */
    private String localCurrency;

    private static final long serialVersionUID = 1L;
}