package com.ixtech.global.config;

import com.ixtech.global.config.WhitelistConfig;
import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.utils.JsonUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 白名单过滤器，默认校验所有请求
 */
@Slf4j
@Component
@Order(2)
public class WhitelistFilter implements Filter {

    private final WhitelistConfig whitelistConfig;

    // 多次反向代理后会有多个ip值 的分割符
    private final static String IP_UTILS_FLAG = ",";

    public WhitelistFilter(WhitelistConfig whitelistConfig) {
        this.whitelistConfig = whitelistConfig;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("WhitelistFilter initialized");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 如果白名单未启用，直接通过
        if (!whitelistConfig.isEnabled()) {
            chain.doFilter(request, response);
            return;
        }

        // 检查是否为排除路径
        String requestUri = httpRequest.getRequestURI();
        List<String> excludePaths = whitelistConfig.getExcludePaths();
        if (excludePaths != null) {
            for (String path : excludePaths) {
                if (requestUri.startsWith(path.replace("/**", "")) || requestUri.equals(path)) {
                    log.info("Request URI {} matches exclude path {}, skipping whitelist check", requestUri, path);
                    chain.doFilter(request, response);
                    return;
                }
            }
        }

        // 获取客户端 IP
        String clientIp = getClientIp(httpRequest);
        log.info("Client IP: {}", clientIp);

        // 检查 IP 是否在白名单中
        boolean isAllowed = whitelistConfig.getIps() != null && whitelistConfig.getIps().contains(clientIp);
        if (!isAllowed) {
            log.warn("IP {} not in whitelist, access denied", clientIp);
            httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
            // 指定 UTF-8 编码
            httpResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
            httpResponse.setCharacterEncoding(StandardCharsets.UTF_8.name()); // 显式设置 UTF-8
            httpResponse.getWriter().write(JsonUtils.stringify(ApiResponse.fail("您的ip不在白名单内，请联系我们"
                    , HttpServletResponse.SC_FORBIDDEN)));
            return;
        }

        // 通过校验，继续处理请求
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        log.info("WhitelistFilter destroyed");
    }

    /**
     * 获取客户端真实 IP 地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("x-forwarded-for");
            if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
                // 多次反向代理后会有多个ip值，第一个ip才是真实ip
                if (ip.contains(IP_UTILS_FLAG)) {
                    ip = ip.split(IP_UTILS_FLAG)[0];
                }
            }
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 处理多 IP 情况
        if (ip != null && ip.contains(IP_UTILS_FLAG)) {
            ip = ip.split(IP_UTILS_FLAG)[0].trim();
        }
        return ip;
    }
}
