package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 租车请求对象，包含租车相关的所有信息。
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RentalRequestReq {

    /**
     * 客户信息，必填
     */
    @NotNull(message = "客户信息不能为空")
    @Valid
    private CustomerReq customer; // 客户信息对象

    /**
     * 预估总金额，必填
     */
    @NotNull(message = "预估总金额不能为空")
    @Valid
    private EstimatedTotalAmountReq estimatedTotalAmount; // 预估总金额对象

    /**
     * 费率限定信息，可选
     */
    @Valid
    private RateQualifierReq rateQualifier; // 费率限定对象

    /**
     * 取车时间，格式：yyyy-MM-dd HH:mm，必填
     */
    @NotNull(message = "取车时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime pickUpDateTime; // 取车时间

    /**
     * 还车时间，格式：yyyy-MM-dd HH:mm，必填
     */
    @NotNull(message = "还车时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime returnDateTime; // 还车时间

    /**
     * 取车地点信息，必填
     */
    @NotNull(message = "取车地点信息不能为空")
    @Valid
    private LocationReq pickupLocation; // 取车地点对象

    /**
     * 还车地点信息，必填
     */
    @NotNull(message = "还车地点信息不能为空")
    @Valid
    private LocationReq returnLocation; // 还车地点对象

    /**
     * 供应商代码（如 "ZI"），必填
     */
    @NotBlank(message = "供应商代码不能为空")
    private String vendorPrefCode; // 供应商偏好代码

    /**
     * 供应商名称（如 "xxxx"），必填
     */
    @NotBlank(message = "供应商名称不能为空")
    private String vendorPrefName; // 供应商偏好名称

    /**
     * 车辆代码（如 "ICAR"），必填
     */
    @NotBlank(message = "车辆代码不能为空")
    private String vehPrefCode; // 车辆偏好代码

    // /**
    //  * 车辆具体型号代码，必填
    //  */
    // @NotBlank(message = "车辆具体型号代码 不能为空")
    // @JsonProperty("vehicle_model_name")
    // private String vehicleModelName; // 车辆型号名称

    /**
     * 特殊设备偏好列表，可选
     */
    @Valid
    private List<SpecialEquipPrefReq> specialEquipPref; // 特殊设备偏好列表

    /**
     * 到达详情，可选
     */
    @Valid
    private ArrivalDetailReq arrivalDetail; // 到达详情对象

    /**
     * 唯一标识符（如 "8EGUKD04SW29853-6303"），可选
     */
    @NotBlank(message = "唯一标识符 不能为空")
    private String referenceId; // 唯一标识符

    /**
     * 订单来源（1=门店, 2=网站, 3=微信, 4=淘宝, 5=携程, 6=租租车, 7=惠租车, 8=租租车ERC, 9=易途8），必填
     */
    // @NotNull(message = "订单来源不能为空")
    private Integer source; // 订单来源

    /**
     * 来源订单 ID（如 "111321321311"），必填
     */
    @NotBlank(message = "合作伙伴订单编号 不能为空")
    @JsonAlias("partner_order_no")
    private String sourceOrderId; // 来源订单ID
}
