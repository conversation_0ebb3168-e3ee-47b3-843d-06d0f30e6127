package com.ixtech.global.constant;

/**
 * <AUTHOR> hu
 * @date 2025/6/16 19:41
 */
public class I18nConstants {

    public static final String DOT = ",";

    public static final String LANG_SEPARATOR = ";q=";

    public static final String DEFAULT_LANG = "zh-CN";
    public static final double DEFAULT_LANG_QUALITY = 1.0d;

    /**
     * 空的翻译资源key
     */
    public static final String EMPTY = "";

    /**
     * 每批次处理的翻译key数量
     */
    public static final int BATCH_KEY_SIZE = 300;

    /**
     * 最大递归搜索深度，减少数据层级太深造成的性能影响，同时避免死循环
     */
    public static final int MAX_RECURSIVE_SEARCH_DEPTH = 5;

    /**
     * 芋道的响应结果
     */
    public static final String REQUEST_ATTRIBUTE_COMMON_RESULT = "common_result";

}
