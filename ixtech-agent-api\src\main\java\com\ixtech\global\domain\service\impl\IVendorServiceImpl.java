package com.ixtech.global.domain.service.impl;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.domain.service.IVendorService;
import com.ixtech.global.integration.internal.client.VendorFeignClient;
import com.ixtech.global.integration.internal.req.RuleSearchReq;
import com.ixtech.global.integration.internal.req.VehicleSearchReq;
import com.ixtech.global.integration.internal.req.VendorSearchReq;
import com.ixtech.global.integration.internal.resp.LocationSearchResp;
import com.ixtech.global.integration.internal.resp.RuleSearchResp;
import com.ixtech.global.integration.internal.resp.VehicleSearchResp;
import com.ixtech.global.integration.internal.resp.VendorSearchResp;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("vendorService")
public class IVendorServiceImpl implements IVendorService {

    @Resource
    private VendorFeignClient vendorFeignClient;

    @Override
    public ApiResponse<List<VendorSearchResp>> vendorSearch(VendorSearchReq request) {
        return vendorFeignClient.vendorSearch(request);
    }

    @Override
    public ApiResponse<List<LocationSearchResp>> locationSearch(VendorSearchReq request) {
        return vendorFeignClient.locationSearch(request);
    }

    @Override
    public ApiResponse<List<VehicleSearchResp>> vehicleSearch(VehicleSearchReq request) {
        return vendorFeignClient.vehicleSearch(request);
    }

    @Override
    public ApiResponse<List<RuleSearchResp>> ruleSearch(RuleSearchReq request) {
        return vendorFeignClient.ruleSearch(request);
    }
}
