package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.IxCarOrderCustomer;
import com.ixtech.management.repo.entity.JipinzucheCarList;
import com.ixtech.management.repo.mapper.IxCarOrderCustomerMapper;
import com.ixtech.management.repo.mapper.JipinzucheCarListMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class IxCarOrderCustomerRepository {

    @Resource
    private IxCarOrderCustomerMapper ixCarOrderCustomerMapper;

    public Integer insert(IxCarOrderCustomer ixCarOrderCustomer) {
        return ixCarOrderCustomerMapper.insert(ixCarOrderCustomer);
    }
}
