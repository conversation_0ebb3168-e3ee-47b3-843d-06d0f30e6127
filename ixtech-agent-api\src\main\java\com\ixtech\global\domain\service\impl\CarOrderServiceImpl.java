package com.ixtech.global.domain.service.impl;

import com.ixtech.global.common.RedisConstant;
import com.ixtech.global.common.context.OnlineContext;
import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.domain.service.CarOrderService;
import com.ixtech.global.integration.internal.client.CarOrderFeignClient;
import com.ixtech.global.integration.internal.req.OrderQueryReq;
import com.ixtech.global.integration.internal.req.RentalRequestReq;
import com.ixtech.global.integration.internal.resp.OrderCancelResp;
import com.ixtech.global.integration.internal.resp.RentalResp;
import com.ixtech.global.redis.RedisService;
import com.ixtech.global.repo.entity.IxChannelPO;
import com.ixtech.global.repo.repository.IxChannelRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service("carOrderService")
public class CarOrderServiceImpl implements CarOrderService {

    @Resource
    private CarOrderFeignClient carOrderFeignClient;
    @Resource
    private IxChannelRepository ixChannelRepository;
    @Resource
    private RedisService redisService;


    @Override
    public ApiResponse<RentalResp> vehRes(RentalRequestReq rentalRequestReq) {
        // 订单来源赋值
        rentalRequestReq.setSource(fetchOrderSource());
        return carOrderFeignClient.vehRes(rentalRequestReq);
    }

    @Override
    public ApiResponse<OrderCancelResp> vehCancel(OrderQueryReq req) {
        // 订单来源赋值
        req.setSource(fetchOrderSource());
        return carOrderFeignClient.vehCancel(req);
    }

    @Override
    public ApiResponse<RentalResp> vehResStatusSearch(OrderQueryReq req) {
        // 订单来源赋值
        req.setSource(fetchOrderSource());
        return carOrderFeignClient.vehResStatusSearch(req);
    }


    /**
     * 获取订单来源
     */
    private Integer fetchOrderSource() {
        // 从上下文获取用户来源，即订单来源
        Integer sourceId = OnlineContext.getSubject().getSourceId();
        if (sourceId != null) {
            return sourceId;
        }
        // 获取所有渠道
        List<IxChannelPO> sourceList = getSourceList();
        Map<String, IxChannelPO> sourceMap = sourceList.stream()
                .collect(Collectors.toMap(IxChannelPO::getStage, a -> a, (k1, k2) -> k1));
        // 从上下文获取用户来源，即订单来源
        String userSource = OnlineContext.getSubject().getUserSource();
        if (StringUtils.isBlank(userSource)) {
            log.error("CarOrderServiceImpl fetchOrderSource userSource is null");
            return null;
        }
        IxChannelPO source = sourceMap.get(userSource);
        if (source == null) {
            log.error("CarOrderServiceImpl fetchOrderSource userSource is not exist");
            return null;
        }
        return source.getId().intValue();
    }

    @Override
    public List<IxChannelPO> getSourceList() {
        List<IxChannelPO> sourceList = null;
        try {
            // 从缓存中获取订单来源集合
            sourceList = redisService.getFromList(RedisConstant.ORDER_SOURCE_LIST_KEY);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (CollectionUtils.isEmpty(sourceList)) {
            // 缓存中没有，从数据库中获取
            sourceList = ixChannelRepository.queryAllCarOrderSources();
            redisService.addToList(RedisConstant.ORDER_SOURCE_LIST_KEY, sourceList, RedisConstant.SIX_HOURS_SECONDS, ChronoUnit.SECONDS);
        }
        return CollectionUtils.emptyIfNull(sourceList).stream()
                .filter(p -> Objects.nonNull(p.getStage()))
                .collect(Collectors.toList());
    }

}
