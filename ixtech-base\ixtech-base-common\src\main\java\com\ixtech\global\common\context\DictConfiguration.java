package com.ixtech.global.common.context;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.ixtech.global.common.enums.*;
import com.ixtech.global.common.enums.inf.DictInf;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Configuration
public class DictConfiguration {

    // 字典键名常量
    public static final String KEY_STORE_STATUS = "store_status";
    public static final String KEY_TRANSPORTATION_METHOD = "transportation_method";
    public static final String KEY_STORE_LOCATION_TYPE = "store_location_type";
    public static final String KEY_SHUTTLE_TIME_UNIT = "shuttle_time_unit";
    public static final String KEY_PAYMENT_METHOD = "payment_method";
    public static final String KEY_PRICING_METHOD = "pricing_method";
    public static final String KEY_SUPPORT_STATUS = "support_status";
    public static final String KEY_FUEL_POLICY = "fuel_policy";
    public static final String KEY_STORE_TYPE = "store_type";
    public static final String KEY_COUNTRY = "country";
    public static final String KEY_TEL_CODE = "tel_code";
    public static final String KEY_WEEK = "week";
    public static final String KEY_SITE_TYPE = "site_type";
    public static final String CURRENCY = "currency";
    public static final String KEY_VEHICLE_ONLINE_STATUS = "vehicle_online_status";
    public static final String KEY_VEHICLE_DESIGN_MODEL = "vehicle_design_model";
    public static final String KEY_VEHICLE_INSTANT_CONFIRM = "vehicle_instant_confirm";
    public static final String KEY_ENABLE_STATUS = "enable_status";
    public static final String KEY_DOCUMENT_STATUS = "document_status";
    public static final String KEY_RENTAL_ORDER_SRATUS = "rental_order_status";
    public static final String KEY_COUNTRY_ABB = "country_abbreviation";
    public static final String KEY_TASK_STA = "task_status";
    public static final String KEY_LANGUAGE = "language";
    public static final String KEY_DATA_SCOPE = "data_scope";

    /**
     * 字典类型 -> 字典值集合
     *
     * @return
     */
    @Lazy
    @Bean
    public Map<String, List<DictInf>> dictMap() {

        //之所以采用ImmutableMap，ImmutableList，是为了防止被修改
        ImmutableMap.Builder<String, List<DictInf>> res = ImmutableMap.builder();
        // 添加所有字典枚举
        res.put(KEY_STORE_STATUS, ImmutableList.copyOf(StoreStatusEnum.values()));
        res.put(KEY_TRANSPORTATION_METHOD, ImmutableList.copyOf(TransportationMethodEnum.values()));
        res.put(KEY_STORE_LOCATION_TYPE, ImmutableList.copyOf(StoreLocationTypeEnum.values()));
        res.put(KEY_SHUTTLE_TIME_UNIT, ImmutableList.copyOf(ShuttleTimeUnitEnum.values()));
        res.put(KEY_PAYMENT_METHOD, ImmutableList.copyOf(PayMethodEnum.values()));
        res.put(KEY_PRICING_METHOD, ImmutableList.copyOf(SpecialEquipPricingMethodEnum.values()));
        res.put(KEY_SUPPORT_STATUS, ImmutableList.copyOf(SupportStatusEnum.values()));
        res.put(KEY_FUEL_POLICY, ImmutableList.copyOf(FuelPolicyEnum.values()));
        res.put(KEY_STORE_TYPE, ImmutableList.copyOf(StoreTypeEnum.values()));
        res.put(KEY_COUNTRY, ImmutableList.copyOf(CountryEnum.values()));
        res.put(KEY_WEEK, ImmutableList.copyOf(DayOfWeekEnum.values()));
        res.put(KEY_SITE_TYPE, ImmutableList.copyOf(SiteTypeEnum.values()));
        res.put(CURRENCY, ImmutableList.copyOf(CurrencyEnum.values()));
        // 处理 KEY_COUNTRY_CODE，映射到 CountryEnum 的电话代码
        res.put(KEY_TEL_CODE, ImmutableList.copyOf(
                Arrays.stream(CountryEnum.values())
                        .map(country -> new DictInf() {
                            @Override
                            public String getValue() {
                                return country.getTelCode();
                            }

                            @Override
                            public String getLabel() {
                                return country.getChineseName() + "(+" + country.getTelCode() + ")";
                            }
                        })
                        .collect(Collectors.toList())
        ));
        res.put(KEY_COUNTRY_ABB, ImmutableList.copyOf(
                Arrays.stream(CountryEnum.values())
                        .map(country -> new DictInf() {
                            @Override
                            public String getValue() {
                                return country.getAbbreviation();
                            }

                            @Override
                            public String getLabel() {
                                return country.getChineseName() + "-" + country.getAbbreviation();
                            }
                        })
                        .collect(Collectors.toList())
        ));
        res.put(KEY_VEHICLE_ONLINE_STATUS, ImmutableList.copyOf(VehicleOnlineStatusEnum.values()));
        res.put(KEY_VEHICLE_DESIGN_MODEL, ImmutableList.copyOf(VehicleDesignModelEnum.values()));
        res.put(KEY_VEHICLE_INSTANT_CONFIRM, ImmutableList.copyOf(VehicleInstantConfirmEnum.values()));
        res.put(KEY_ENABLE_STATUS, ImmutableList.copyOf(EnabledStatusEnum.values()));
        res.put(KEY_DOCUMENT_STATUS, ImmutableList.copyOf(DocumentStatusEnum.values()));
        res.put(KEY_RENTAL_ORDER_SRATUS, ImmutableList.copyOf(RentalOrderStatusEnum.values()));
        res.put(KEY_TASK_STA, ImmutableList.copyOf(FileTaskStatusEnum.values()));
        res.put(KEY_LANGUAGE, ImmutableList.copyOf(LanguageEnum.values()));
        res.put(KEY_DATA_SCOPE, ImmutableList.copyOf(DataScopeEnum.values()));
        return res.build();
    }

}
