package com.ixtech.management.repo.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> hu
 * @date 2025/4/4 15:59
 */
@Data
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * active 是否在线 false：否 true：是
     */
    private Boolean active;

    /**
     * deleted 是否删除 0：否 1：是
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private LocalDateTime createTime;

    /**
     * update_time in UTC
     */
    private LocalDateTime updateTime;

}
