package com.ixtech.management.repo.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 租车订单的付款方式
 * @TableName jipinzuche_car_order_paytype
 */
@Data
public class JipinzucheCarOrderPaytype implements Serializable {
    /**
     * 
     */
    private Integer id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * 名称
     */
    private String title;

    /**
     * api 的stage
     */
    private String stage;

    /**
     * 0->未删除；1->删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;
}