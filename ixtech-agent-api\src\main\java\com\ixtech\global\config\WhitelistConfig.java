package com.ixtech.global.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 白名单配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "whitelist")
public class WhitelistConfig {
    /**
     * 是否启用白名单
     */
    private boolean enabled;

    /**
     * IP 白名单列表
     */
    private List<String> ips;

    /**
     * 排除校验的路径列表
     */
    private List<String> excludePaths;
}
