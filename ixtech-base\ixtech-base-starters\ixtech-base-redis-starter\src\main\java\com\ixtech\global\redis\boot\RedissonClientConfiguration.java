package com.ixtech.global.redis.boot;


import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;



/**
 * RedissonClient Bean定义
 *
 * <AUTHOR>
 * @date 2022-10-19
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(RedisConfigProperties.class)
public class RedissonClientConfiguration {

    private static final String REDIS_TAG = "redis://";

    @Resource
    private RedisConfigProperties redisConfigProperties;


    /**
     * 默认实例
     *
     * @return
     */
    @Bean("redissonClient")
    public RedissonClient redissonClientList() {
        Assert.notNull(redisConfigProperties, "redis RedisConfigProperties empty.");
        Config config = new Config();
        config.useSingleServer().setAddress(REDIS_TAG.concat(redisConfigProperties.getHost()).concat(":").concat(String.valueOf(redisConfigProperties.getPort())))
                .setPassword(redisConfigProperties.getPassword())
                .setConnectionMinimumIdleSize(redisConfigProperties.getMinIdle())
                .setDatabase(redisConfigProperties.getIndex())
                .setConnectionPoolSize(redisConfigProperties.getMaxTotal())
                .setRetryAttempts(redisConfigProperties.getRetryAttempts())
                .setRetryInterval(redisConfigProperties.getRetryInterval())
                .setIdleConnectionTimeout(redisConfigProperties.getMaxWaitMillis());

        config.setThreads(redisConfigProperties.getThreads())
                .setNettyThreads(redisConfigProperties.getNettyThreads());
        return Redisson.create(config);

    }

}
