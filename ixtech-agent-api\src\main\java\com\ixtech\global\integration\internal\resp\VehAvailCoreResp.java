package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 车辆可用核心信息 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehAvailCoreResp implements Serializable {

 private static final long serialVersionUID = 1L;


 /**
  * 租赁费率列表
  */
 private List<RentalRateResp> rentalRates;

 /**
  * 车辆信息
  */
 private VehicleResp vehicle;
}
