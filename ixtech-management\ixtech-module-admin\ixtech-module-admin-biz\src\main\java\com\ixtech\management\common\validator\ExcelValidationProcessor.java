package com.ixtech.management.common.validator;

import com.ixtech.management.common.exception.BizException;
import com.ixtech.management.config.ExcelHeaderCheckException;
import com.ixtech.management.integration.internal.resp.CarOrderImportRespVO;
import com.ixtech.management.repo.entity.CarOrderSource;
import com.ixtech.management.repo.repository.CarOrderSourceRepository;
import jakarta.annotation.Resource;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class ExcelValidationProcessor {
    @Resource
    private ExcelValidatorFactory excelValidatorFactory;

    @Resource
    private CarOrderSourceRepository jipinzucheCarOrderSourceRepository;

    public void validateExcel(Long fileVersionId, String fileType, MultipartFile file
            , Map<String, List<CarOrderImportRespVO.CarOrderCheckOrImportResult>> checkResultMap) {
        Workbook workbook;
        Sheet sheet;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            sheet = workbook.getSheetAt(0);
        } catch (IOException e) {
            throw new BizException("excel文件解析失败");
        }
        if (sheet.getLastRowNum() > 1000) {
            throw new BizException("文件数据超过1000行！");
        }

        CarOrderSource source = jipinzucheCarOrderSourceRepository.selectByTitle(fileType);
        if (source == null) {
            throw new ExcelHeaderCheckException("渠道不存在");
        }

        // 获取校验器
        ExcelHeaderValidator validator = excelValidatorFactory.getValidator(fileType);

        // 1. 校验表头
        validator.validateHeaders(getHeaders(sheet));

        // 2. 校验行数据
        validator.validateAllRows(Math.toIntExact(source.getId()), fileVersionId, sheet, checkResultMap);
    }

    private List<String> getHeaders(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        List<String> headers = new ArrayList<>();

        if (headerRow == null) {
            throw new ExcelHeaderCheckException("表头行为空，请补充表头");
        }
        // 获取最大列数（包括空白列）
        int lastCellNum = headerRow.getLastCellNum();

        for (int i = 0; i < lastCellNum; i++) {
            Cell cell = headerRow.getCell(i, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            headers.add(cell == null ? "" : cell.getStringCellValue());
        }
        return headers;
//        return StreamSupport.stream(
//                        sheet.getRow(0).spliterator(), false)
//                .map(Cell::getStringCellValue)
//                .collect(Collectors.toList());
    }
}
