package com.ixtech.management.facade.api;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.ixtech.management.config.ExcelHeaderCheckException;
import com.ixtech.management.integration.internal.req.CarOrderAddReq;
import com.ixtech.management.integration.internal.req.CarOrderImportReqVO;
import com.ixtech.management.integration.internal.req.CarOrderListQueryReq;
import com.ixtech.management.integration.internal.resp.*;
import com.ixtech.management.domain.service.CarOrderService;
import com.ixtech.management.repo.entity.CarOrderSource;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 车辆订单")
@RestController
@RequestMapping("/ixtech/carorder")
@Validated
public class CarOrderController {

    @Resource
    private CarOrderService carOrderService;

    @PostMapping(value="/check", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "页面租车车辆订单导入")
    @PreAuthorize("@ss.hasPermission('ixtech:carorder:check')")
    public PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> createCarOrder(
            @RequestParam("pageNo") String pageNo,
            @RequestParam("pageSize") String pageSize,
            @RequestParam("distribution") String distribution
            , @RequestParam("file") MultipartFile file
            , @RequestParam("fileVersionId") Long fileVersionId) {
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(Integer.valueOf(pageNo));
        pageParam.setPageSize(Integer.valueOf(pageSize));
        return carOrderService.fileCheck(distribution, file, fileVersionId, pageParam);
    }

    @GetMapping("/getImportPage")
    @Operation(summary = "获得导入订单列表分页数据")
//    @PreAuthorize("@ss.hasPermission('ixtech:carorder:queryImportResult')")
    public PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> getCarOrder(
            CarOrderImportReqVO pageReqVO) throws ExcelHeaderCheckException {
        return carOrderService.getImportCarOrder(pageReqVO);
    }

    @PostMapping("/getImportResult")
    @Operation(summary = "获得导入订单列表分页数据")
//    @PreAuthorize("@ss.hasPermission('ixtech:carorder:getImportResult')")
    public UploadResultResp getImportResult(
            @RequestBody CarOrderImportReqVO pageReqVO) throws ExcelHeaderCheckException {
        return carOrderService.getImportResult(pageReqVO);
    }

    @PostMapping("/import")
    @Operation(summary = "校验后的合法订单的导入")
    @PreAuthorize("@ss.hasPermission('ixtech:carorder:import')")
    public PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> carOrderExcelImport(
            @RequestParam("pageNo") String pageNo,
            @RequestParam("pageSize") String pageSize,
            @RequestParam("distribution") String distribution
            , @RequestParam("file") MultipartFile file
            , @RequestParam("fileVersionId") Long fileVersionId) {
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(Integer.valueOf(pageNo));
        pageParam.setPageSize(Integer.valueOf(pageSize));
        return carOrderService.orderExcelImport(Integer.valueOf(distribution), file, fileVersionId, pageParam);
    }

    /**
     * 订单列表 接口
     *
     * @param req 请求
     * @return 响应
     */
    @PostMapping("/order_list")
    @Operation(summary = "订单查询")
    @PreAuthorize("@ss.hasPermission('ixtech:carorder:list')")
    public CommonResult<PageResult<CarOrderListQueryResp>> vehOrderList(CarOrderListQueryReq req) {

        PageResult<CarOrderListQueryResp> resp = carOrderService.vehOrderList(req);
        return success(new PageResult<>(resp.getList(),
                resp.getTotal()));
    }

    /**
     * 订单来源列表 接口
     *
     * @return 响应
     */
    @PostMapping("/order_source_list")
    @Operation(summary = "订单来源列表")
    @PreAuthorize("@ss.hasPermission('ixtech:carordersource:list')")
    public CommonResult<PageResult<CarOrderSource>> vehOrderSourceList() {
        PageResult<CarOrderSource> resp = carOrderService.vehAllOrderSourceList();
        return success(new PageResult<>(resp.getList(),
                resp.getTotal()));
    }

    @GetMapping("/info")
    @Operation(summary = "订单详情")
//    @PreAuthorize("@ss.hasPermission('ixtech:carorder:details')")
    public CommonResult<CarOrderInfoResp> vehOrderInfo(@RequestParam(value = "id") Long id) {
        CarOrderInfoResp resp = carOrderService.vehOrderInfo(id);
        return success(resp);
    }

    @GetMapping("/export")
    @Operation(summary = "导出订单 Excel")
    @PreAuthorize("@ss.hasPermission('ixtech:carorder:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantExcel(@Valid CarOrderListQueryReq exportReqVO,
                                  HttpServletResponse response) throws IOException {
        exportReqVO.setLimitSize(0L);
        exportReqVO.setLimitStart(0L);
        List<CarOrderListExportResp> list = carOrderService.vehOrderListExport(exportReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "订单.xlsx", "数据", CarOrderListExportResp.class,
                list);

    }

    /**
     * 订单手动创建 接口
     *
     * @return 响应
     */
    @PostMapping("/order_add")
    @Operation(summary = "订单手动创建")
    @PreAuthorize("@ss.hasPermission('ixtech:carorder:add')")
    public CommonResult<Integer> vehOrderAdd(@RequestBody @Valid CarOrderAddReq req) {
        int row = carOrderService.vehOrderAdd(req);
        return success(row);
    }

    @GetMapping("/check_unique")
    @Operation(summary = "订单重复检查")
//    @PreAuthorize("@ss.hasPermission('ixtech:carorder:checkunique')")
    public CommonResult<Boolean> vehOrderCheckUnique(@RequestParam(value = "code") String sourceOrdercode) {
        Long row = carOrderService.vehOrderCheckUnique(sourceOrdercode);
        return success(row > 0);
    }

}