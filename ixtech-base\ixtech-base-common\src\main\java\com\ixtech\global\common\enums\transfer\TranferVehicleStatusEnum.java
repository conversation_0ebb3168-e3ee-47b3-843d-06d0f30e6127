package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

@Getter
public enum TranferVehicleStatusEnum {
    /**
     * 正常状态
     */
    NORMAL(1, "正常"),

    /**
     * 保养状态
     */
    MAINTENANCE(2, "保养"),

    /**
     * 维修状态
     */
    REPAIR(3, "维修");

    TranferVehicleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 状态编码
     */
    private  Integer code;

    /**
     * 状态描述
     */
    private  String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 状态编码
     * @return 对应的枚举实例，若未匹配则返回null
     */
    public static TranferVehicleStatusEnum getByCode(Integer code) {
        for (TranferVehicleStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 状态编码
     * @return 对应的描述，若未匹配则返回"未知状态"
     */
    public static String getDescByCode(Integer code) {
        TranferVehicleStatusEnum status = getByCode(code);
        return status != null ? status.desc : "未知状态";
    }

}
