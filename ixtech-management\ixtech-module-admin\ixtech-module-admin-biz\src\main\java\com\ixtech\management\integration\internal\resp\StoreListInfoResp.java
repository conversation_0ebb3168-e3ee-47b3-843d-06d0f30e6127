package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 门店列表响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:55
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StoreListInfoResp {

    /**
     * 门店id
     */
    private Long id;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 门店类型 1：城市 2：机场 3：酒店
     */
    private Integer type;

    /**
     * 门店类型（字符串描述）
     */
    private String typeStr;

    /**
     * 录入车辆数
     */
    private Integer carCount;

    /**
     * 在线车辆数
     */
    private Integer activeCarCount;

    /**
     * 上下线状态 true：上线 false：下线
     */
    private Boolean active;

}
