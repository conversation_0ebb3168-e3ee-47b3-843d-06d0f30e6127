package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计价方式枚举
 * (1-按次, 2-按小时)
 */
@Getter
@AllArgsConstructor
public enum StoreChargeMethodEnum implements DictInf {

    PER_TIME(1, "按次"),
    PER_HOUR(2, "按小时"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static StoreChargeMethodEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
