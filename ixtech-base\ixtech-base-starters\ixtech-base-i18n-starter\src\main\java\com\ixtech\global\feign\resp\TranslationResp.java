package com.ixtech.global.feign.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> hu
 * @date 2025/6/10 14:30
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TranslationResp {

    /**
     * 翻译结果（key：资源key，value：翻译结果）
     */
    private Map<String, String> translationResult;

}
