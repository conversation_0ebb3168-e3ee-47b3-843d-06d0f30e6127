package com.ixtech.global.domain.service.impl;

import com.ixtech.global.domain.service.ITestService;
import com.ixtech.global.repo.entity.User;
import com.ixtech.global.repo.repository.TestRepository;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 测试service实现类
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
public class TestServiceImpl implements ITestService {

    @Resource
    private TestRepository testRepository;

    /**
     * 测试数据库连接-查询用户
     * 有未查到没关系，没有异常就OK
     */
    @Override
    public Boolean testQueryConnect() {
        List<User> userList = testRepository.queryUserByParams(NumberUtils.INTEGER_ZERO, NumberUtils.INTEGER_ONE);
        return true;
    }
}
