package com.ixtech.management.domain.service;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.ixtech.management.integration.internal.req.CarOrderAddReq;
import com.ixtech.management.integration.internal.req.CarOrderImportReqVO;
import com.ixtech.management.integration.internal.req.CarOrderListQueryReq;
import com.ixtech.management.integration.internal.req.OrderQueryReq;
import com.ixtech.management.integration.internal.resp.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.ixtech.management.repo.entity.CarOrderSource;
import com.ixtech.management.repo.entity.JipinzucheCarOrderPO;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 车辆订单 Service 接口
 *
 * <AUTHOR>
 */
public interface CarOrderService {

    /**
     * 订单excel导入
     *
     * @param distribution
     * @param file
     * @param fileVersionId
     * @return
     */
    PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> fileCheck(String distribution, MultipartFile file, Long fileVersionId, PageParam pageParam);

    /**
     * 获取导入结果分页信息
     *
     * @param pageReqVO 分页查询
     * @return 导入结果分页信息
     */
    PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> getImportCarOrder(@Valid CarOrderImportReqVO pageReqVO);

    PageResult<CarOrderImportRespVO.CarOrderCheckOrImportResult> orderExcelImport(Integer distribution, MultipartFile file, Long fileVersionId, PageParam pageParam);

    UploadResultResp getImportResult(CarOrderImportReqVO pageReqVO);

    PageResult<CarOrderListQueryResp> vehOrderList(@Valid CarOrderListQueryReq req);

    PageResult<CarOrderSource> vehAllOrderSourceList();

    CarOrderInfoResp vehOrderInfo(Long id);

    Integer vehOrderAdd(CarOrderAddReq req);

    List<CarOrderListExportResp> vehOrderListExport(@Valid CarOrderListQueryReq exportReqVO);

    Long vehOrderCheckUnique(String sourceOrdercode);

    JipinzucheCarOrderPO selectBySourceAndSourceCode(OrderQueryReq orderQueryReq);
}