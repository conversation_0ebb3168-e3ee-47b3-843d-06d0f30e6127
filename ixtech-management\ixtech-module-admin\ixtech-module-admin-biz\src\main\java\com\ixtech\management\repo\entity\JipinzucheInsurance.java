package com.ixtech.management.repo.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName jipinzuche_insurance
 */
@Data
public class JipinzucheInsurance implements Serializable {
    /**
     * 
     */
    private Integer id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * 保险名称
     */
    private String title;

    /**
     * 保险code
     */
    private String code;

    /**
     * 创建人
     */
    private Integer mid;

    /**
     * 
     */
    private String ip;

    /**
     * 0->未删除；1->删除
     */
    private Integer isDelete;

    /**
     * 简介
     */
    private String info;

    private static final long serialVersionUID = 1L;
}