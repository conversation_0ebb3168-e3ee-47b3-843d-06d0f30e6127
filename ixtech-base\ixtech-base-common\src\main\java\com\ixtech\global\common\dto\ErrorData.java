package com.ixtech.global.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据传输error结果
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ErrorData {

    private String code;
    private String message;

    /**
     * message错误文案对应的翻译资源key
     */
    @JsonIgnore
    private String resourceKey;

}
