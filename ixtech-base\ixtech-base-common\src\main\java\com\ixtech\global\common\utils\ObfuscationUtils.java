package com.ixtech.global.common.utils;

import java.util.Base64;

public final class ObfuscationUtils {
    // 工具类，禁止实例化
    private ObfuscationUtils() {
        throw new AssertionError("Utility class cannot be instantiated");
    }

    /**
     * 混淆字符串并返回Base64编码结果
     *
     * @param input 原始字符串
     * @param seed  混淆种子
     * @return Base64编码的混淆结果
     */
    public static String obfuscate(String input, long seed) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        seed = seed != 0 ? seed : 1; // 防止种子为0
        byte[] bytes = process(input.getBytes(), seed);
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 反混淆Base64编码的字符串
     *
     * @param input Base64编码的混淆字符串
     * @param seed  反混淆种子（必须与混淆时相同）
     * @return 原始字符串
     */
    public static String deobfuscate(String input, long seed) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        seed = seed != 0 ? seed : 1; // 防止种子为0
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(input);
            byte[] processedBytes = process(decodedBytes, seed);
            return new String(processedBytes);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid Base64 encoded input", e);
        }
    }

    // 核心处理方法
    private static byte[] process(byte[] input, long seed) {
        byte[] result = new byte[input.length];
        long state = seed;

        for (int i = 0; i < input.length; i++) {
            state = xorshift(state);
            result[i] = (byte) (input[i] ^ (state & 0xFF));
        }
        return result;
    }

    // Xorshift 随机数生成
    private static long xorshift(long state) {
        state ^= (state << 13);
        state ^= (state >>> 7);
        state ^= (state << 17);
        return state;
    }

    public static void main(String[] args) {
        long seed = 2826242220L;

        Long modelId = 6L;
        String obfuscate = obfuscate(String.join(":", "IX RENTAL", "BKK01", "EDAR", modelId.toString(), "xxx"), seed);
        System.out.println(obfuscate);
    }
}
