package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 门店地点resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LocationResp implements Serializable {

 private static final long serialVersionUID = 1L;


 /**
  * 地点代码上下文
  */
 private String locationCodeContext;

 /**
  * 地点代码
  */
 private String locationCode;
}
