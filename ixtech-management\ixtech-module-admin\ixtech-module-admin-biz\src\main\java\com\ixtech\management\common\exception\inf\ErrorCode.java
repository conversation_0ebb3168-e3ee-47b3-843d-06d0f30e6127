package com.ixtech.management.common.exception.inf;


import com.ixtech.management.common.component.SpringContextHolder;

/**
 * 错误码接口
 */
public interface ErrorCode {
    // 错误类型静态变量
    String ERROR_TYPE_SYSTEM = "S";   // 系统错误
    String ERROR_TYPE_PARAM = "P";    // 参数错误
    String ERROR_TYPE_BUSINESS = "B"; // 业务错误

    String SERVICE_CODE = SpringContextHolder.getProperty("app.code", "0000");

    /**
     * 获取服务码
     *
     * @return 4位服务码
     */
    default String getServiceCode() {
        return SERVICE_CODE;
    }

    /**
     * 获取错误类型
     *
     * @return S, P 或 B
     */
    String getErrorType();

    /**
     * 获取具体错误码
     *
     * @return 4位具体错误码
     */
    String getSpecificCode();

    /**
     * 获取错误消息
     *
     * @return 错误描述
     */
    String getMessage();

    /**
     * 获取完整错误码
     *
     * @return 例如 "0001S0000"
     */
    String getFullCode();

    /**
     * 根据完整错误码解析
     *
     * @param fullCode 完整错误码，例如 "0001S0000"
     * @return 对应的错误码实例，未找到返回 null
     */
    ErrorCode fromFullCode(String fullCode);
}
