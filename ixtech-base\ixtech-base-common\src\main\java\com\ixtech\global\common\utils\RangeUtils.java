package com.ixtech.global.common.utils;

import com.google.common.collect.ImmutableMap;
import com.ixtech.global.common.dto.RangeResult;
import com.ixtech.global.common.dto.TimeInterval;
import com.ixtech.global.common.dto.inf.RangeInterval;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 排班工具类
 */
public class RangeUtils {

    /**
     * 计算最小需要的车辆数量，忽略零长度时间段
     *
     * @param ranged   已分配车辆。key 车辆id，value 时间排班，无序，保证不重叠
     * @param unRanged 未分配的车辆排班
     * @param <T>      继承自 TimeInterval 的类型
     * @return 最小车辆数量
     */
    public static <T extends TimeInterval> Integer minCars(Map<Long, List<T>> ranged, List<T> unRanged) {
        // 收集所有非零长度时间段，忽略零长度时间段
        List<T> nonZeroIntervals = new ArrayList<>();

        // 从 ranged 中提取时间段
        if (ranged != null) {
            for (List<T> intervals : ranged.values()) {
                if (intervals != null) {
                    for (T interval : intervals) {
                        if (!interval.getStartTime().equals(interval.getEndTime())) {
                            nonZeroIntervals.add(interval);
                        }
                    }
                }
            }
        }

        // 添加 unRanged 时间段
        if (unRanged != null) {
            for (T interval : unRanged) {
                if (!interval.getStartTime().equals(interval.getEndTime())) {
                    nonZeroIntervals.add(interval);
                }
            }
        }

        // 如果没有非零长度时间段，返回 0
        if (nonZeroIntervals.isEmpty()) {
            return 0;
        }

        // 按开始时间排序，若开始时间相同，按结束时间排序
        nonZeroIntervals.sort(Comparator.comparing((T a) -> a.getStartTime()).thenComparing(TimeInterval::getEndTime));

        // 使用优先队列维护每辆车的最后一个结束时间
        PriorityQueue<LocalDateTime> endTimes = new PriorityQueue<>();

        // 遍历所有非零长度时间段
        for (T interval : nonZeroIntervals) {
            // 如果队列不为空且当前时间段可以复用最早结束的车辆
            if (!endTimes.isEmpty() && !interval.getStartTime().isBefore(endTimes.peek())) {
                endTimes.poll(); // 移除最早结束时间
            }
            // 加入当前时间段的结束时间
            endTimes.add(interval.getEndTime());
        }

        // 队列大小即为所需最小车辆数
        return endTimes.size();
    }

    /**
     * 根据ranged和unranged排班，判断currentRange，可以排进几辆车
     *
     * @param ranged   已分配车辆。key 车辆id，value 时间排班，无序，保证不重叠
     * @param unRanged 未分配的车辆排班
     * @param <T>      继承自 TimeInterval 的类型
     * @return 最小车辆数量
     */
    public static <T extends RangeInterval> Integer minAmount(Map<Long, List<T>> ranged, List<T> unRanged) {
        // 收集所有非零长度时间段，忽略零长度时间段
        List<T> nonZeroIntervals = new ArrayList<>();

        // 从 ranged 中提取时间段
        if (ranged != null) {
            for (List<T> intervals : ranged.values()) {
                if (intervals != null) {
                    for (T interval : intervals) {
                        if (!interval.startTime().equals(interval.endTime())) {
                            nonZeroIntervals.add(interval);
                        }
                    }
                }
            }
        }

        // 添加 unRanged 时间段
        if (unRanged != null) {
            for (T interval : unRanged) {
                if (!interval.startTime().equals(interval.endTime())) {
                    nonZeroIntervals.add(interval);
                }
            }
        }

        // 如果没有非零长度时间段，返回 0
        if (nonZeroIntervals.isEmpty()) {
            return 0;
        }

        // 按开始时间排序，若开始时间相同，按结束时间排序
        nonZeroIntervals.sort(Comparator.comparing((T a) -> a.startTime()).thenComparing(RangeInterval::endTime));

        // 使用优先队列维护每辆车的最后一个结束时间
        PriorityQueue<LocalDateTime> endTimes = new PriorityQueue<>();

        // 遍历所有非零长度时间段
        for (T interval : nonZeroIntervals) {
            // 如果队列不为空且当前时间段可以复用最早结束的车辆
            if (!endTimes.isEmpty() && !interval.startTime().isBefore(endTimes.peek())) {
                endTimes.poll(); // 移除最早结束时间
            }
            // 加入当前时间段的结束时间
            endTimes.add(interval.endTime());
        }

        // 队列大小即为所需最小车辆数
        return endTimes.size();
    }

    /**
     * 根据ranged和unranged排班，为 unRanged 分配车辆，并计算总共需要的最小车辆数
     *
     * @param ranged     已分配车辆。key 车辆id，value 时间排班，无序，可能会重叠
     * @param unRanged   未分配的车辆排班，将被作为一个整体分配给同一辆车
     * @param vehicleIds 所有的车辆id
     * @param <T>        继承自 RangeInterval 的类型
     * @return RangeResult 包含最小车辆总数和为 unRanged 分配的车辆ID。
     * 如果能排进 ranged 中的现有车辆，则返回该车辆ID。
     * 如果需要新车辆，则从 vehicleIds 中返回一个未使用过的ID。
     * 如果 unRanged 内部有冲突，或所有车辆都无法满足排班，则车辆ID返回null。
     */
    public static <T extends RangeInterval> RangeResult doRange(Map<Long, List<T>> ranged, List<T> unRanged, List<Long> vehicleIds) {
        RangeResult result = new RangeResult();

        // 1. 计算总共需要的最小车辆数 (minAvailableCount)
        List<T> allIntervals = new ArrayList<>();
        if (ranged != null) {
            ranged.values().forEach(allIntervals::addAll);
        }
        if (unRanged != null) {
            allIntervals.addAll(unRanged);
        }

        // 过滤掉零长度的时间段
        List<T> nonZeroIntervals = allIntervals.stream()
                .filter(interval -> !interval.startTime().equals(interval.endTime()))
                .collect(Collectors.toList());

        if (nonZeroIntervals.isEmpty()) {
            result.setMinAvailableCount(0);
            result.setVehicleId(null); // 没有任务，不需要车辆
            return result;
        }

        // 按开始时间排序
        nonZeroIntervals.sort(Comparator.comparing(RangeInterval::startTime));

        // 使用优先队列计算最小车辆数
        PriorityQueue<LocalDateTime> endTimes = new PriorityQueue<>();
        for (T interval : nonZeroIntervals) {
            if (!endTimes.isEmpty() && !interval.startTime().isBefore(endTimes.peek())) {
                endTimes.poll();
            }
            endTimes.add(interval.endTime());
        }
        result.setMinAvailableCount(endTimes.size());

        // 2. 为 unRanged 分配车辆 (vehicleId)
        if (unRanged == null || unRanged.isEmpty()) {
            result.setVehicleId(null); // 没有需要新分配的任务
            return result;
        }

        // 检查 unRanged 内部是否有时间冲突，如果有，则无法分配给单辆车
        if (hasOverlap(unRanged)) {
            result.setVehicleId(null); // 新任务内部冲突，无法分配
            return result;
        }

        // 尝试将 unRanged 分配给已存在的车辆
        if (ranged != null) {
            for (Map.Entry<Long, List<T>> entry : ranged.entrySet()) {
                Long vehicleId = entry.getKey();
                List<T> existingIntervals = entry.getValue();

                // 将现有排班和新排班合并
                List<T> combinedIntervals = Stream.concat(existingIntervals.stream(), unRanged.stream())
                        .collect(Collectors.toList());

                if (!hasOverlap(combinedIntervals)) {
                    // 找到可以复用的车辆
                    result.setVehicleId(vehicleId);
                    return result;
                }
            }
        }

        // 如果没有现有车辆可用，则寻找一辆新车
        Set<Long> usedVehicleIds = (ranged != null) ? ranged.keySet() : Collections.emptySet();
        Optional<Long> availableNewVehicle = vehicleIds.stream()
                .filter(id -> !usedVehicleIds.contains(id))
                .findFirst();

        if (availableNewVehicle.isPresent()) {
            // 找到一辆未使用的车辆
            result.setVehicleId(availableNewVehicle.get());
        } else {
            // 所有车辆都已在用且都无法满足新排班
            result.setVehicleId(null);
        }

        return result;
    }

    /**
     * 根据ranged和unranged排班，为 unRanged 分配车辆
     *
     * @param ranged     已分配车辆。key 车辆id，value 时间排班，无序，可能会重叠
     * @param unRanged   未分配的车辆排班
     * @param vehicleIds 所有的车辆id
     * @param <T>        继承自 RangeInterval 的类型
     * @return unRanged 分配的车辆ID。
     * 如果能排进 ranged 中的现有车辆，则返回该车辆ID。
     * 如果需要新车辆，则从 vehicleIds 中返回一个未使用过的ID。
     * 如果 unRanged 内部有冲突，或所有车辆都无法满足排班，则车辆ID返回null。
     */
    public static <T extends RangeInterval> Long rangeByVehicle(Map<Long, List<T>> ranged, T unRanged, List<Long> vehicleIds) {
        // 如果待分配的任务为空，则无法分配
        if (unRanged == null) {
            return null;
        }

        // 1. 优先尝试分配给已存在的车辆
        if (ranged != null) {
            for (Map.Entry<Long, List<T>> entry : ranged.entrySet()) {
                Long vehicleId = entry.getKey();

                // 将现有排班和新排班合并到一个临时列表
                List<T> combinedIntervals = Stream.concat(entry.getValue().stream(), Stream.of(unRanged))
                                .collect(Collectors.toList());

                // 检查合并后是否存在时间冲突
                if (!hasOverlap(combinedIntervals)) {
                    // 如果没有冲突，则该车辆可用，返回其ID
                    return vehicleId;
                }
            }
        }

        // 2. 如果没有现有车辆可用，则寻找一辆新车
        // 获取所有已使用的车辆ID
        Set<Long> usedVehicleIds = (ranged != null) ? ranged.keySet() : Collections.emptySet();

        if (vehicleIds != null) {
            // 遍历所有可用车辆ID
            for (Long newVehicleId : vehicleIds) {
                // 如果该ID未被使用
                if (!usedVehicleIds.contains(newVehicleId)) {
                    // 分配这辆新车，返回其ID
                    return newVehicleId;
                }
            }
        }

        // 3. 如果所有现有车辆都有冲突，并且没有可用的新车，则返回null
        return null;
    }

    /**
     * 检查时间间隔列表内部是否存在重叠。按 半开区间 [startTime, endTime) 的方式来处理
     *
     * @param intervals 时间间隔列表
     * @param <T>       继承自 RangeInterval 的类型
     * @return 如果存在重叠则返回 true，否则返回 false
     */
    private static <T extends RangeInterval> boolean hasOverlap(List<T> intervals) {
        if (intervals == null || intervals.size() <= 1) {
            return false;
        }

        // 创建副本以防修改原始列表
        List<T> sortedIntervals = new ArrayList<>(intervals);
        // 按开始时间排序
        sortedIntervals.sort(Comparator.comparing(RangeInterval::startTime));

        for (int i = 1; i < sortedIntervals.size(); i++) {
            // 如果当前区间的开始时间早于前一个区间的结束时间，则存在重叠
            if (sortedIntervals.get(i).startTime().isBefore(sortedIntervals.get(i - 1).endTime())) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param stock2vehicleCount 库存拥有的车辆数量。key 库存id，value 库存车辆数量
     * @param stock2ranges       库存的排班记录。key 库存id，value 时间排班，无序，可能会重叠
     * @param forRange           未分配的车辆排班，需要分配库存id
     * @param <T>                继承自 RangeInterval 的类型
     * @return forRange 分配的库存ID。
     * 遍历库存id。 如果能根据 stock2ranges 和 forRange 计算出来的最小车辆数量 <= stock2vehicleCount 库存车辆数量，返回库存id
     * 如果 所有库存都无法满足排班，则库存ID返回null。
     */
    public static <T extends RangeInterval> Long rangeByStock(Map<Long, Integer> stock2vehicleCount
            , Map<Long, List<T>> stock2ranges, T forRange) {

        // 边界情况：没有要分配的任务
        if (forRange == null) {
            return null;
        }
        // 边界情况：没有可用的库存
        if (stock2vehicleCount == null || stock2vehicleCount.isEmpty()) {
            return null;
        }

        // 为了在有多个库存都满足条件时能有确定的返回结果，对库存ID进行排序
        List<Long> sortedStockIds = new ArrayList<>(stock2vehicleCount.keySet());
        Collections.sort(sortedStockIds);

        for (Long stockId : sortedStockIds) {
            Integer vehicleCount = stock2vehicleCount.get(stockId);

            // 如果库存没有车辆或配置错误，则跳过
            if (vehicleCount == null || vehicleCount <= 0) {
                continue;
            }

            // 获取当前库存的已有排班，如果不存在则视为空列表
            List<T> existingRanges = (stock2ranges != null)
                    ? stock2ranges.getOrDefault(stockId, Collections.emptyList())
                    : Collections.emptyList();

            // 将新任务与已有排班合并
            List<T> combinedRanges = new ArrayList<>(existingRanges);
            combinedRanges.add(forRange);

            // 计算合并后的排班所需要的最小车辆数
            int minRequiredVehicles = calculateMinRequiredVehicles(combinedRanges);

            // 如果所需车辆数小于或等于库存拥有的车辆数，则分配成功
            if (minRequiredVehicles <= vehicleCount) {
                return stockId;
            }
        }

        // 遍历完所有库存后，仍未找到合适的，返回null
        return null;
    }

    /**
     * 为单个库存分配排班任务。这是一个方便方法，调用主 rangeByStock 方法。
     *
     * @param stockId      库存ID
     * @param vehicleCount 库存拥有的车辆数量
     * @param ranges       该库存已有的排班记录
     * @param forRange     需要分配的排班任务
     * @param <T>          继承自 RangeInterval 的类型
     * @return 如果能将 forRange 分配给该库存，则返回库存ID；否则返回 null。
     */
    public static <T extends RangeInterval> Long rangeByStock(Long stockId, Integer vehicleCount
            , List<T> ranges, T forRange) {
        if (stockId == null) {
            return null;
        }
        return rangeByStock(ImmutableMap.of(stockId, vehicleCount), ImmutableMap.of(stockId, ranges), forRange);
    }

    /**
     * 计算处理给定时间段列表所需的最少车辆数（资源数）。
     * 该算法通过计算最大并发任务数来实现。
     *
     * @param allIntervals 所有时间段的列表
     * @return 所需的最小车辆数
     */
    private static <T extends RangeInterval> int calculateMinRequiredVehicles(List<T> allIntervals) {
        if (allIntervals == null || allIntervals.isEmpty()) {
            return 0;
        }

        // 过滤掉零时长的任务，因为它们不占用车辆资源时长
        List<T> nonZeroIntervals = new ArrayList<>();
        for (T interval : allIntervals) {
            if (!interval.startTime().equals(interval.endTime())) {
                nonZeroIntervals.add(interval);
            }
        }

        if (nonZeroIntervals.isEmpty()) {
            return 0;
        }

        // 按任务开始时间对所有任务进行排序
        nonZeroIntervals.sort(Comparator.comparing(RangeInterval::startTime));

        // 使用一个最小堆（优先队列）来存放当前已分配车辆的任务的“结束时间”
        // 堆顶始终是所有已分配任务中最早的结束时间
        PriorityQueue<LocalDateTime> endTimes = new PriorityQueue<>();

        for (T interval : nonZeroIntervals) {
            // 检查最早结束的车辆是否在当前任务开始前已经空闲
            if (!endTimes.isEmpty() && !interval.startTime().isBefore(endTimes.peek())) {
                // 如果是，则可以复用这辆车。将其从堆中移除（代表该车的旧任务已结束）
                endTimes.poll();
            }
            // 将当前任务的结束时间加入堆中（代表分配了一辆车给这个任务）
            // 这辆车可能是刚被复用的，也可能是一辆新车（如果堆的大小增加了）
            endTimes.add(interval.endTime());
        }

        // 循环结束后，堆的大小就是整个时间线上同时在执行任务的最大数量，
        // 即为满足所有排班所需的最少车辆数。
        return endTimes.size();
    }

}
