package com.ixtech.management.repo.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 门店表
 * @TableName jipinzuche_store
 */
@Data
public class Store implements Serializable {
    /**
     *
     */
    private Long id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * 国家id
     */
    private Integer countryid;

    /**
     * 省id
     */
    private Integer provinceid;

    /**
     * 城市id
     */
    private Integer cityid;

    /**
     * 区id
     */
    private Integer countyid;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 经度
     */
    private Double lon;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 门店图片
     */
    private String litpic;

    /**
     * 地理位置-小图
     */
    private String mapImgSmall;

    /**
     * 地理位置-大图
     */
    private String mapImgLarge;

    /**
     * 简介
     */
    private String intro;

    /**
     * 负责人
     */
    private String principal;

    /**
     * 负责人电话
     */
    private String principalMobile;

    /**
     * 电话区号
     */
    private String mobileAreaCode;

    /**
     * 货币单位
     */
    private String currencyUnit;

    /**
     * 单日里程限制km(ps:0->不限制)
     */
    private Double dayMileage;

    /**
     * 门店代码(唯一)
     */
    private String code;

    /**
     * 营业时间
     */
    private String opentime;

    /**
     * 1->城市；2->机场；3->酒店
     */
    private Integer type;

    /**
     * 机场代码
     */
    private String iata;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 取车指引
     */
    private String howToGet;

    /**
     * 还车指引
     */
    private String howToGo;

    /**
     * 汇率(人民币：当地货币)
     */
    private Double exchangeRate;

    /**
     * 最短租期
     */
    private Integer minRentDays;

    /**
     * 租车订单前缀
     */
    private String ordercodePre;

    /**
     * 添加时间
     */
    private Integer time;

    /**
     * ip
     */
    private String ip;

    /**
     * 添加人
     */
    private Integer mid;

    /**
     * 1->有效；-1->删除
     */
    private Integer status;

    /**
     * 超出部分多少钱1公里
     */
    private Double everyPrice;

    /**
     * 货币单位
     */
    private String monetaryUnit;

    /**
     * 公里单位
     */
    private String kilometers;

    /**
     * 英文门店名
     */
    private String enname;

    /**
     * 英文门店地址
     */
    private String enaddress;

    /**
     * 工作日可加班时间晚上
     */
    private String weekdayevening;

    /**
     * 工作日可加班时间早上
     */
    private String weekdaymoring;

    /**
     * 非营业可加班时间收费标准多少钱一次
     */
    private Double feescale;

    /**
     * 非工作日可加班时间
     */
    private String weekendevening;

    /**
     * 非工作日可加班时间晚上
     */
    private String weekendmoring;

    /**
     * 携程超时费用标准化
     */
    private String standardcharge;

    /**
     * 1->在接口中显示门店信息 -1->在接口中不显示门店信息
     */
    private Integer displaystatus;

    /**
     * 儿童座椅费用
     */
    private Double childseat;

    /**
     * 还车时间延迟
     */
    private Double timeDelay;

    /**
     * pos机支持的卡种
     */
    private String cardType;

    /**
     * 该门店是否支持vcc订单 0->不支持 1->支持
     */
    private Integer isVcc;

    /**
     * 刷信誉权的额度区间
     */
    private String quotaInterval;

    /**
     * 财务BI链接
     */
    private String url;

    /**
     * 订单条款
     */
    private String orderTerms;

    /**
     * 注意事项
     */
    private String orderPrecautions;

    /**
     * 是否立即确认 0->否；1->是
     */
    private String confirmNow;

    /**
     * 最短提前预定时间
     */
    private Integer minBookHour;

    /**
     * 系统专用货币单位
     */
    private String localcurrencyUnit;

    /**
     * 到达方式：1.Within walking distance ,2.Free Shuttle Service,3.Meet and Greet ,4.AirTrain/LRT ,5.Shuttle Service ,6.Free Air Train ,7.Call for collection on arrival
     */
    private Integer arrivalWay;

    /**
     * 所属供应商id
     */
    private Long vendorId;

    private static final long serialVersionUID = 1L;
}