package com.ixtech.global.interceptor;

/**
 * <AUTHOR> hu
 * @date 2025/6/16 19:07
 */

import com.ixtech.global.util.RequestUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;

/**
 * 语言拦截器：从请求中提取语言信息并存储到上下文中
 *
 * <AUTHOR> hu
 * @date 2025/6/16 19:11
 */
@Slf4j
public class AcceptLanguageFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hai<PERSON> filterChain) throws ServletException, IOException {
        try {
            // 获取请求中的语言列表（按优先级排序）
            List<RequestUtils.LanguagePriority> languages = extractLanguagesFromRequest(request);

            // 将语言列表存储到上下文持有者中
            if (!CollectionUtils.isEmpty(languages)) {
                LanguageContextHolder.setLanguages(languages);
            }

            // 继续处理请求
            filterChain.doFilter(request, response);
        } finally {
            // 请求完成后，清除上下文，防止内存泄漏
            LanguageContextHolder.clear();
        }
    }

    /**
     * 从请求中提取语言列表
     */
    private List<RequestUtils.LanguagePriority> extractLanguagesFromRequest(HttpServletRequest request) {
        return RequestUtils.getRequestLanguage(request);
    }

}


