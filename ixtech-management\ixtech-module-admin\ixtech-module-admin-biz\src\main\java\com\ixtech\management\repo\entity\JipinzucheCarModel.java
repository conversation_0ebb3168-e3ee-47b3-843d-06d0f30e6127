package com.ixtech.management.repo.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 车辆表
 * @TableName jipinzuche_car_model
 */
@Data
public class JipinzucheCarModel implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * car_brand的id
     */
    private Integer brandid;

    /**
     * 车辆图片
     */
    private String litpic;

    /**
     * 车型名称
     */
    private String name;

    /**
     * 车辆四字码
     */
    private String carcode;

    /**
     * 座位数
     */
    private Integer seat;

    /**
     * 车门数
     */
    private Integer door;

    /**
     * 行李数
     */
    private Integer luggage;

    /**
     * 1->自动挡；2->手动挡
     */
    private Integer transmission;

    /**
     * 添加时间
     */
    private Integer time;

    /**
     * 添加ip
     */
    private String ip;

    /**
     * 添加人
     */
    private Integer mid;

    /**
     * 1->有效；-1->删除
     */
    private Integer status;

    /**
     * 车辆成本价
     */
    private Integer dayprice;

    private static final long serialVersionUID = 1L;
}