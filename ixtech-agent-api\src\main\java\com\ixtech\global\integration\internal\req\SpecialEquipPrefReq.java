package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 特殊设备偏好对象，包含设备类型和数量。
 */
@Data
public class SpecialEquipPrefReq {

    /**
     * 设备类型，必填
     */
    @NotBlank(message = "设备类型不能为空")
    @JsonProperty("equip_type")
    private String equipType;

    /**
     * 数量，必填
     */
    @NotNull(message = "数量不能为空")
    @JsonProperty("quantity")
    private Integer quantity;
}
