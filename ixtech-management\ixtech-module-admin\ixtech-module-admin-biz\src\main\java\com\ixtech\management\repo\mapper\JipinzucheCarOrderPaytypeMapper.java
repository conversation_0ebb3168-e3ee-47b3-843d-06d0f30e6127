package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.JipinzucheCarOrderPaytype;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【jipinzuche_car_order_paytype(租车订单的付款方式)】的数据库操作Mapper
* @createDate 2025-04-21 00:01:41
* @Entity com.ixtech.management.repo.entity.JipinzucheCarOrderPaytype
*/
@DS("ix")
@Mapper
public interface JipinzucheCarOrderPaytypeMapper {

    JipinzucheCarOrderPaytype selectById(Long id);

    List<JipinzucheCarOrderPaytype> selectAll();
}




