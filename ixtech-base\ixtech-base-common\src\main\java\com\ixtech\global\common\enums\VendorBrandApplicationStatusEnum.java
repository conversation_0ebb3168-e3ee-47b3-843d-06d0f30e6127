package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商品牌申请状态枚举
 * 对应表: rental_vendor_brand_application, 字段: status
 * (1-待审批, 2-已受理, 3-已驳回, 4-已取消, 5-完成配置)
 */
@Getter
@AllArgsConstructor
public enum VendorBrandApplicationStatusEnum implements DictInf {

    PENDING_APPROVAL(1, "待审批"),
    ACCEPTED(2, "已受理"),
    REJECTED(3, "已驳回"),
    CANCELLED(4, "已取消"),
    CONFIG_COMPLETED(5, "完成配置"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }
}
