package com.ixtech.management.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum DepositPaytypeEnum {

    ALIPAY("支付宝", 1),
    WECHAT("微信", 2),
    CREDIT_CARD("信用卡", 3),
    CASH("现金", 4),
    FLASH_RENT("闪租", 5),
    WORRY_FREE_RENT("无忧租", 6),
    SESAME_CREDIT("芝麻信誉", 7);


    private final String name;
    private final int code;

    private static final Map<Integer, DepositPaytypeEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DepositPaytypeEnum::getCode, Function.identity()));

    private static final Map<String, DepositPaytypeEnum> NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DepositPaytypeEnum::getName, Function.identity()));

    DepositPaytypeEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 对应的枚举，找不到返回null
     */
    public static DepositPaytypeEnum getByCode(Integer code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据名称获取枚举
     * @param name 名称
     * @return 对应的枚举，找不到返回null
     */
    public static DepositPaytypeEnum getByName(String name) {
        return NAME_MAP.get(name);
    }

    /**
     * 检查编码是否存在
     * @param code 编码
     * @return 是否存在
     */
    public static boolean containsCode(Integer code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 检查名称是否存在
     * @param name 名称
     * @return 是否存在
     */
    public static boolean containsName(String name) {
        return NAME_MAP.containsKey(name);
    }

    /**
     * 获取所有编码
     * @return 编码数组
     */
    public static Integer[] getAllCodes() {
        return Arrays.stream(values()).map(DepositPaytypeEnum::getCode).toArray(Integer[]::new);
    }

    /**
     * 获取所有名称
     * @return 名称数组
     */
    public static String[] getAllNames() {
        return Arrays.stream(values()).map(DepositPaytypeEnum::getName).toArray(String[]::new);
    }

    @Override
    public String toString() {
        return "InsuranceEnum{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
