package com.ixtech.global.common.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;

import java.util.Arrays;
import java.util.Map;


@Slf4j
public class RequestLogInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        long startTime = System.currentTimeMillis();
        //设置请求开始时间
        request.setAttribute("startTime", startTime);
        return true;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            this.afterCompletion(request, response, handler, ex);
            long startTime = (Long) request.getAttribute("startTime");
            long endTime = System.currentTimeMillis();
            long executeTime = endTime - startTime;
            StringBuilder sb = new StringBuilder();
            if (handler instanceof HandlerMethod) {
                sb.append("请求耗时:").append(executeTime).append("\n");
                sb.append("方法:").append(request.getServletPath()).append("\n");
                if (("POST".equals(request.getMethod()) || "PUT".equals(request.getMethod()))
                        && request instanceof ContentCachingRequestWrapper) {
                    ContentCachingRequestWrapper requestWrapper = (ContentCachingRequestWrapper) request;
                    sb.append("请求param参数:").append(getParamString(request.getParameterMap())).append("\n");
                    sb.append("请求body参数:").append(new String(requestWrapper.getContentAsByteArray())).append("\n");
                } else {
                    sb.append("请求param参数:").append(getParamString(request.getParameterMap())).append("\n");
                }
                log.info(sb.toString());
            }
        } catch (Exception e) {
            log.error("afterCompletion异常:", e);
        } finally {
            // 日志清楚操作
        }
    }

    /**
     * 拼装请求参数
     */
    private String getParamString(Map<String, String[]> map) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String[]> e : map.entrySet()) {
            sb.append(e.getKey()).append("=");
            String[] value = e.getValue();
            if (value != null && value.length == 1) {
                sb.append(value[0]).append("\t");
            } else {
                sb.append(Arrays.toString(value)).append("\t");
            }
        }
        return sb.toString();
    }
}
