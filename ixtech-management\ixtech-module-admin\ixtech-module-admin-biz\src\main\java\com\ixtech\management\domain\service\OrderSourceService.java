package com.ixtech.management.domain.service;

import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.VendorAddResp;
import com.ixtech.management.integration.internal.resp.VendorListInfoResp;

import java.util.List;

/**
 * 渠道service
 *
 * @date 2025/4/4 13:15
 */
public interface OrderSourceService {
    /**
     * 供应商下拉列表
     *
     * @param vendorDropdownReq
     * @return
     */
    List<SelectOptionResponse<Long>> dropdownList(VendorDropdownReq vendorDropdownReq);

}
