package com.ixtech.global.common.enums;

import lombok.Getter;

/**
 * 渠道枚举
 */
@Getter
public enum SourceEnum {
    STORE(1, "门店"),         // 实体门店
    WEBSITE(2, "网站"),       // 官方网站
    WECHAT(3, "微信"),        // 微信平台
    TAOBAO(4, "淘宝"),        // 淘宝平台
    CTRIP(5, "携程"),         // 携程平台
    ZUZUCHE(6, "租租车"),     // 租租车平台
    HUIZUCHE(7, "惠租车"),    // 惠租车平台
    ZUZUCHE_ERC(8, "租租车ERC"), // 租租车ERC平台
    YITU8(9, "易途8"),        // 易途8平台
    B2B(10, "B2B"),           // B2B业务
    BOOKING(11, "Booking"),   // Booking平台
    HUIZUCHE_INTL(12, "惠租车INTL"); // 惠租车国际版

    private final Integer code; // 来源编码
    private final String name;  // 来源名称

    SourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
