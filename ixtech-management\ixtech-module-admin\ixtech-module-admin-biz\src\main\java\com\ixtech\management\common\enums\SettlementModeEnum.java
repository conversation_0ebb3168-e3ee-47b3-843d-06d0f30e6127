package com.ixtech.management.common.enums;

import lombok.Getter;

/**
 * 结算模式枚举
 *
 * <AUTHOR> hu
 * @date 2025/4/4 11:23
 */
@Getter
public enum SettlementModeEnum implements BaseEnum<Byte> {

    BASE_PRICE((byte) 1, "底价模式"),
    COMMISSION((byte) 2, "抽佣模式"),
    ;

    private final Byte code;
    private final String name;

    SettlementModeEnum(Byte code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return asString();
    }

}
