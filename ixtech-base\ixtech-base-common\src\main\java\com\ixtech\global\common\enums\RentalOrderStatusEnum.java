package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * 租车订单状态枚举
 * 对应表: rental_order, 字段: status
 * (-1-已取消，0-待确认，1-已确认，2-已取车，3-已还车，4-已完成)
 */
@Getter
@AllArgsConstructor
public enum RentalOrderStatusEnum implements DictInf {

    CANCELLED(-1, "已取消"),
    PENDING_CONFIRMATION(0, "待确认"),
    CONFIRMED(1, "已确认"),
    PICKED_UP(2, "已取车"),
    RETURNED(3, "已还车"),
    COMPLETED(4, "已完成"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }


    /**
     * 订单状态集合 可取消订单
     */
    public static final Set<Integer> CANCELABLE_STATUS = Set.of(PENDING_CONFIRMATION.getCode(), CONFIRMED.getCode());


    /**
     * 订单状态集合 已确认
     */
    public static final Set<Integer> AFTER_CONFIRM_STATUS = Set.of(CONFIRMED.getCode(), PICKED_UP.getCode(), RETURNED.getCode(), COMPLETED.getCode());

    public static String getNameByCode(Integer code) {
        for (RentalOrderStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getLabel();
            }
        }
        return "";
    }

}
