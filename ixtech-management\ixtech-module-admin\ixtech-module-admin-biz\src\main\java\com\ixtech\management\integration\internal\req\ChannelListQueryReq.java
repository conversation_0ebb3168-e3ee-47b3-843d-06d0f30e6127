package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.ixtech.management.common.dto.PageRequest;
import lombok.Data;

/**
 * 渠道列表请求Req
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:33
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChannelListQueryReq extends PageRequest {

    /**
     * 渠道id
     */
    private Long id;

    /**
     * 渠道简称
     */
    private String channelName;

    /**
     * 公司全称
     */
    private String companyFullName;

}
