package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.Getter;
import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 国家/地区枚举类
 * 包含中文名称、英文名称、简称、电话代码、货币代码和货币数字代码
 */
@Getter
@AllArgsConstructor
public enum CountryEnum implements DictInf {

    HONG_KONG ("中国香港", "Hong Kong (China)", "HK", "852", "HKD", "344"),
    MACAO ("中国澳门", "Macao (China)", "MO", "853", "MOP", "446"),
    TAIWAN ("中国台湾", "Taiwan (China)", "TW", "886", "TWD", "901"),

    AFGHANISTAN("阿富汗伊斯兰共和国", "the Islamic Republic of Afghanistan", "AF", "93", "AFN", "971"),
    ALBANIA("阿尔巴尼亚共和国", "the Republic of Albania", "AL", "355", "ALL", "8"),
    ALGERIA("阿尔及利亚民主人民共和国", "the People's Democratic Republic of Algeria", "DZ", "213", "DZD", "12"),
    ANDORRA("安道尔公国", "the Principality of Andorra", "AD", "376", "EUR", "978"),
    ANGOLA("安哥拉共和国", "the Republic of Angola", "AO", "244", "AOA", "973"),
    ANTIGUA_BARBUDA("安提瓜和巴布达", "Antigua and Barbuda", "AG", "1-268", "XCD", "951"),
    ARGENTINA("阿根廷共和国", "the Argentine Republic", "AR", "54", "ARS", "32"),
    ARMENIA("亚美尼亚共和国", "the Republic of Armenia", "AM", "374", "AMD", "51"),
    AUSTRALIA("澳大利亚", "Australia", "AU", "61", "AUD", "36"),
    AUSTRIA("奥地利共和国", "the Republic of Austria", "AT", "43", "EUR", "978"),
    AZERBAIJAN("阿塞拜疆共和国", "the Republic of Azerbaijan", "AZ", "994", "AZN", "944"),
    BAHAMAS("巴哈马国", "the Commonwealth of the Bahamas", "BS", "1-242", "BSD", "44"),
    BAHRAIN("巴林王国", "the Kingdom of Bahrain", "BH", "973", "BHD", "48"),
    BANGLADESH("孟加拉人民共和国", "the People's Republic of Bangladesh", "BD", "880", "BDT", "50"),
    BARBADOS("巴巴多斯", "Barbados", "BB", "1-246", "BBD", "52"),
    BELARUS("白俄罗斯共和国", "the Republic of Belarus", "BY", "375", "BYN", "933"),
    BELGIUM("比利时王国", "the Kingdom of Belgium", "BE", "32", "EUR", "978"),
    BELIZE("伯利兹", "Belize", "BZ", "501", "BZD", "84"),
    BENIN("贝宁共和国", "the Republic of Benin", "BJ", "229", "XOF", "952"),
    BHUTAN("不丹王国", "the Kingdom of Bhutan", "BT", "975", "INR,BTN", "356,064"),
    BOLIVIA("多民族玻利维亚国", "the Plurinational State of Bolivia", "BO", "591", "BOB", "68"),
    BOSNIA("波斯尼亚和黑塞哥维那", "Bosnia and Herzegovina", "BA", "387", "BAM", "977"),
    BOTSWANA("博茨瓦纳共和国", "the Republic of Botswana", "BW", "267", "BWP", "72"),
    BRAZIL("巴西联邦共和国", "the Federative Republic of Brazil", "BR", "55", "BRL", "986"),
    BRUNEI("文莱达鲁萨兰国", "Brunei Darussalam", "BN", "673", "BND", "96"),
    BULGARIA("保加利亚共和国", "the Republic of Bulgaria", "BG", "359", "BGN", "975"),
    BURKINA_FASO("布基纳法索", "Burkina Faso", "BF", "226", "XOF", "952"),
    BURUNDI("布隆迪共和国", "the Republic of Burundi", "BI", "257", "BIF", "108"),
    CABO_VERDE("佛得角共和国", "the Republic of Cabo Verde", "CV", "238", "CVE", "132"),
    CAMBODIA("柬埔寨王国", "the Kingdom of Cambodia", "KH", "855", "KHR", "116"),
    CAMEROON("喀麦隆共和国", "the Republic of Cameroon", "CM", "237", "XAF", "950"),
    CANADA("加拿大", "Canada", "CA", "1", "CAD", "124"),
    CENTRAL_AFRICAN_REPUBLIC("中非共和国", "the Central African Republic", "CF", "236", "XAF", "950"),
    CHAD("乍得共和国", "the Republic of Chad", "TD", "235", "XAF", "950"),
    CHILE("智利共和国", "the Republic of Chile", "CL", "56", "CLP", "152"),
    CHINA("中华人民共和国", "the People's Republic of China", "CN", "86", "CNY", "156"),
    COLOMBIA("哥伦比亚共和国", "the Republic of Colombia", "CO", "57", "COP", "170"),
    COMOROS("科摩罗联盟", "the Union of the Comoros", "KM", "269", "KMF", "174"),
    CONGO("刚果共和国", "the Republic of the Congo", "CG", "242", "XAF", "950"),
    COOK_ISLANDS("库克群岛", "the Cook Islands", "CK", "682", "NZD", "554"),
    COSTA_RICA("哥斯达黎加共和国", "the Republic of Costa Rica", "CR", "506", "CRC", "188"),
    CROATIA("克罗地亚共和国", "the Republic of Croatia", "HR", "385", "EUR", "978"),
    CUBA("古巴共和国", "the Republic of Cuba", "CU", "53", "CUP", "192"),
    CYPRUS("塞浦路斯共和国", "the Republic of Cyprus", "CY", "357", "EUR", "978"),
    CZECH_REPUBLIC("捷克共和国", "the Czech Republic", "CZ", "420", "CZK", "203"),
    NORTH_KOREA("朝鲜民主主义人民共和国", "the Democratic People's Republic of Korea", "KP", "850", "KPW", "408"),
    DEMOCRATIC_REPUBLIC_CONGO("刚果民主共和国", "the Democratic Republic of the Congo", "CD", "243", "CDF", "976"),
    DENMARK("丹麦王国", "the Kingdom of Denmark", "DK", "45", "DKK", "208"),
    DJIBOUTI("吉布提共和国", "the Republic of Djibouti", "DJ", "253", "DJF", "262"),
    DOMINICA("多米尼克国", "the Commonwealth of Dominica", "DM", "1-767", "XCD", "951"),
    DOMINICAN_REPUBLIC("多米尼加共和国", "the Dominican Republic", "DO", "1-809,1-829,1-849", "DOP", "214"),
    ECUADOR("厄瓜多尔共和国", "the Republic of Ecuador", "EC", "593", "USD", "840"),
    EGYPT("阿拉伯埃及共和国", "the Arab Republic of Egypt", "EG", "20", "EGP", "818"),
    EL_SALVADOR("萨尔瓦多共和国", "the Republic of El Salvador", "SV", "503", "SVC,USD", "222,840"),
    EQUATORIAL_GUINEA("赤道几内亚共和国", "the Republic of Equatorial Guinea", "GQ", "240", "XAF", "950"),
    ERITREA("厄立特里亚国", "the State of Eritrea", "ER", "291", "ERN", "232"),
    ESTONIA("爱沙尼亚共和国", "the Republic of Estonia", "EE", "372", "EUR", "978"),
    ESWATINI("斯威士兰王国", "the Kingdom of Eswatini", "SZ", "268", "SZL", "748"),
    ETHIOPIA("埃塞俄比亚联邦民主共和国", "the Federal Democratic Republic of Ethiopia", "ET", "251", "ETB", "230"),
    FIJI("斐济共和国", "the Republic of Fiji", "FJ", "679", "FJD", "242"),
    FINLAND("芬兰共和国", "the Republic of Finland", "FI", "358", "EUR", "978"),
    FRANCE("法兰西共和国", "the French Republic", "FR", "33", "EUR", "978"),
    GABON("加蓬共和国", "the Gabonese Republic", "GA", "241", "XAF", "950"),
    GAMBIA("冈比亚共和国", "the Republic of the Gambia", "GM", "220", "GMD", "270"),
    GEORGIA("格鲁吉亚", "Georgia", "GE", "995", "GEL", "981"),
    GERMANY("德意志联邦共和国", "the Federal Republic of Germany", "DE", "49", "EUR", "978"),
    GHANA("加纳共和国", "the Republic of Ghana", "GH", "233", "GHS", "936"),
    GREECE("希腊共和国", "the Hellenic Republic", "GR", "30", "EUR", "978"),
    GRENADA("格林纳达", "Grenada", "GD", "1-473", "XCD", "951"),
    GUATEMALA("危地马拉共和国", "the Republic of Guatemala", "GT", "502", "GTQ", "320"),
    GUINEA("几内亚共和国", "the Republic of Guinea", "GN", "224", "GNF", "324"),
    GUINEA_BISSAU("几内亚比绍共和国", "the Republic of Guinea-Bissau", "GW", "245", "XOF", "952"),
    GUYANA("圭亚那合作共和国", "the Co-operative Republic of Guyana", "GY", "592", "GYD", "328"),
    HAITI("海地共和国", "the Republic of Haiti", "HT", "509", "HTG,USD", "332,840"),
    HOLY_SEE("罗马教廷", "the Holy See", "VA", "39", "EUR", "978"),
    HONDURAS("洪都拉斯共和国", "the Republic of Honduras", "HN", "504", "HNL", "340"),
    HUNGARY("匈牙利", "Hungary", "HU", "36", "HUF", "348"),
    ICELAND("冰岛共和国", "the Republic of Iceland", "IS", "354", "ISK", "352"),
    INDIA("印度共和国", "the Republic of India", "IN", "91", "INR", "356"),
    INDONESIA("印度尼西亚共和国", "the Republic of Indonesia", "ID", "62", "IDR", "360"),
    IRAN("伊朗伊斯兰共和国", "the Islamic Republic of Iran", "IR", "98", "IRR", "364"),
    IRAQ("伊拉克共和国", "the Republic of Iraq", "IQ", "964", "IQD", "368"),
    IRELAND("爱尔兰", "Ireland", "IE", "353", "EUR", "978"),
    ISRAEL("以色列国", "the State of Israel", "IL", "972", "ILS", "376"),
    ITALY("意大利共和国", "the Republic of Italy", "IT", "39", "EUR", "978"),
    JAMAICA("牙买加", "Jamaica", "JM", "1-876", "JMD", "388"),
    JAPAN("日本国", "Japan", "JP", "81", "JPY", "392"),
    JORDAN("约旦哈希姆王国", "the Hashemite Kingdom of Jordan", "JO", "962", "JOD", "400"),
    KAZAKHSTAN("哈萨克斯坦共和国", "the Republic of Kazakhstan", "KZ", "7", "KZT", "398"),
    KENYA("肯尼亚共和国", "the Republic of Kenya", "KE", "254", "KES", "404"),
    KIRIBATI("基里巴斯共和国", "the Republic of Kiribati", "KI", "686", "AUD", "36"),
    KUWAIT("科威特国", "the State of Kuwait", "KW", "965", "KWD", "414"),
    KYRGYZSTAN("吉尔吉斯共和国", "the Kyrgyz Republic", "KG", "996", "KGS", "417"),
    LAOS("老挝人民民主共和国", "the Lao People's Democratic Republic", "LA", "856", "LAK", "418"),
    LATVIA("拉脱维亚共和国", "the Republic of Latvia", "LV", "371", "EUR", "978"),
    LEBANON("黎巴嫩共和国", "the Lebanese Republic", "LB", "961", "LBP", "422"),
    LESOTHO("莱索托王国", "the Kingdom of Lesotho", "LS", "266", "LSL,ZAR", "426,710"),
    LIBERIA("利比里亚共和国", "the Republic of Liberia", "LR", "231", "LRD", "430"),
    LIBYA("利比亚国", "the State of Libya", "LY", "218", "LYD", "434"),
    LIECHTENSTEIN("列支敦士登公国", "the Principality of Liechtenstein", "LI", "423", "CHF", "756"),
    LITHUANIA("立陶宛共和国", "the Republic of Lithuania", "LT", "370", "EUR", "978"),
    LUXEMBOURG("卢森堡大公国", "the Grand Duchy of Luxembourg", "LU", "352", "EUR", "978"),
    MADAGASCAR("马达加斯加共和国", "the Republic of Madagascar", "MG", "261", "MGA", "969"),
    MALAWI("马拉维共和国", "the Republic of Malawi", "MW", "265", "MWK", "454"),
    MALAYSIA("马来西亚", "Malaysia", "MY", "60", "MYR", "458"),
    MALDIVES("马尔代夫共和国", "the Republic of Maldives", "MV", "960", "MVR", "462"),
    MALI("马里共和国", "the Republic of Mali", "ML", "223", "XOF", "952"),
    MALTA("马耳他共和国", "the Republic of Malta", "MT", "356", "EUR", "978"),
    MARSHALL_ISLANDS("马绍尔群岛共和国", "the Republic of the Marshall Islands", "MH", "692", "USD", "840"),
    MAURITANIA("毛里塔尼亚伊斯兰共和国", "the Islamic Republic of Mauritania", "MR", "222", "MRU", "929"),
    MAURITIUS("毛里求斯共和国", "the Republic of Mauritius", "MU", "230", "MUR", "480"),
    MEXICO("墨西哥合众国", "the United Mexican States", "MX", "52", "MXN", "484"),
    MICRONESIA("密克罗尼西亚联邦", "the Federated States of Micronesia", "FM", "691", "USD", "840"),
    MONACO("摩纳哥公国", "the Principality of Monaco", "MC", "377", "EUR", "978"),
    MONGOLIA("蒙古国", "Mongolia", "MN", "976", "MNT", "496"),
    MONTENEGRO("黑山", "Montenegro", "ME", "382", "EUR", "978"),
    MOROCCO("摩洛哥王国", "the Kingdom of Morocco", "MA", "212", "MAD", "504"),
    MOZAMBIQUE("莫桑比克共和国", "the Republic of Mozambique", "MZ", "258", "MZN", "943"),
    MYANMAR("缅甸联邦共和国", "the Republic of the Union of Myanmar", "MM", "95", "MMK", "104"),
    NAMIBIA("纳米比亚共和国", "the Republic of Namibia", "NA", "264", "NAD,ZAR", "516,710"),
    NAURU("瑙鲁共和国", "the Republic of Nauru", "NR", "674", "AUD", "36"),
    NEPAL("尼泊尔", "Nepal", "NP", "977", "NPR", "524"),
    NETHERLANDS("荷兰王国", "the Kingdom of the Netherlands", "NL", "31", "EUR", "978"),
    NEW_ZEALAND("新西兰", "New Zealand", "NZ", "64", "NZD", "554"),
    NICARAGUA("尼加拉瓜共和国", "the Republic of Nicaragua", "NI", "505", "NIO", "558"),
    NIGER("尼日尔共和国", "the Republic of the Niger", "NE", "227", "XOF", "952"),
    NIGERIA("尼日利亚联邦共和国", "the Federal Republic of Nigeria", "NG", "234", "NGN", "566"),
    NIUE("纽埃", "Niue", "NU", "683", "NZD", "554"),
    NORTH_MACEDONIA("北马其顿共和国", "the Republic of North Macedonia", "MK", "389", "MKD", "807"),
    NORWAY("挪威王国", "the Kingdom of Norway", "NO", "47", "NOK", "578"),
    OMAN("阿曼苏丹国", "the Sultanate of Oman", "OM", "968", "OMR", "512"),
    PAKISTAN("巴基斯坦伊斯兰共和国", "the Islamic Republic of Pakistan", "PK", "92", "PKR", "586"),
    PALAU("帕劳共和国", "the Republic of Palau", "PW", "680", "USD", "840"),
    PANAMA("巴拿马共和国", "the Republic of Panama", "PA", "507", "PAB,USD", "590,840"),
    PAPUA_NEW_GUINEA("巴布亚新几内亚独立国", "the Independent State of Papua New Guinea", "PG", "675", "PGK", "598"),
    PARAGUAY("巴拉圭共和国", "the Republic of Paraguay", "PY", "595", "PYG", "600"),
    PERU("秘鲁共和国", "the Republic of Peru", "PE", "51", "PEN", "604"),
    PHILIPPINES("菲律宾共和国", "the Republic of the Philippines", "PH", "63", "PHP", "608"),
    POLAND("波兰共和国", "the Republic of Poland", "PL", "48", "PLN", "985"),
    PORTUGAL("葡萄牙共和国", "the Portuguese Republic", "PT", "351", "EUR", "978"),
    QATAR("卡塔尔国", "the State of Qatar", "QA", "974", "QAR", "634"),
    SOUTH_KOREA("大韩民国", "the Republic of Korea", "KR", "82", "KRW", "410"),
    MOLDOVA("摩尔多瓦共和国", "the Republic of Moldova", "MD", "373", "MDL", "498"),
    ROMANIA("罗马尼亚", "Romania", "RO", "40", "RON", "946"),
    RUSSIA("俄罗斯联邦", "the Russian Federation", "RU", "7", "RUB", "643"),
    RWANDA("卢旺达共和国", "the Republic of Rwanda", "RW", "250", "RWF", "646"),
    SAINT_KITTS_NEVIS("圣基茨和尼维斯", "Saint Kitts and Nevis", "KN", "1-869", "XCD", "951"),
    SAINT_LUCIA("圣卢西亚", "Saint Lucia", "LC", "1-758", "XCD", "951"),
    SAINT_VINCENT_GRENADINES("圣文森特和格林纳丁斯", "Saint Vincent and the Grenadines", "VC", "1-784", "XCD", "951"),
    SAMOA("萨摩亚独立国", "the Independent State of Samoa", "WS", "685", "WST", "882"),
    SAN_MARINO("圣马力诺共和国", "the Republic of San Marino", "SM", "378", "EUR", "978"),
    SAO_TOME_PRINCIPE("圣多美和普林西比民主共和国", "the Democratic Republic of Sao Tome and Principe", "ST", "239", "STN", "930"),
    SAUDI_ARABIA("沙特阿拉伯王国", "the Kingdom of Saudi Arabia", "SA", "966", "SAR", "682"),
    SENEGAL("塞内加尔共和国", "the Republic of Senegal", "SN", "221", "XOF", "952"),
    SERBIA("塞尔维亚共和国", "the Republic of Serbia", "RS", "381", "RSD", "941"),
    SEYCHELLES("塞舌尔共和国", "the Republic of Seychelles", "SC", "248", "SCR", "690"),
    SIERRA_LEONE("塞拉利昂共和国", "the Republic of Sierra Leone", "SL", "232", "SLE", "925"),
    SINGAPORE("新加坡共和国", "the Republic of Singapore", "SG", "65", "SGD", "702"),
    SLOVAKIA("斯洛伐克共和国", "the Slovak Republic", "SK", "421", "EUR", "978"),
    SLOVENIA("斯洛文尼亚共和国", "the Republic of Slovenia", "SI", "386", "EUR", "978"),
    SOLOMON_ISLANDS("所罗门群岛", "Solomon Islands", "SB", "677", "SBD", "90"),
    SOMALIA("索马里联邦共和国", "the Federal Republic of Somalia", "SO", "252", "SOS", "706"),
    SOUTH_AFRICA("南非共和国", "the Republic of South Africa", "ZA", "27", "ZAR", "710"),
    SOUTH_SUDAN("南苏丹共和国", "the Republic of South Sudan", "SS", "211", "SSP", "728"),
    SPAIN("西班牙王国", "the Kingdom of Spain", "ES", "34", "EUR", "978"),
    SRI_LANKA("斯里兰卡民主社会主义共和国", "the Democratic Socialist Republic of Sri Lanka", "LK", "94", "LKR", "144"),
    PALESTINE("巴勒斯坦国", "the State of Palestine", "PS", "970", "", ""),
    SUDAN("苏丹共和国", "the Republic of the Sudan", "SD", "249", "SDG", "938"),
    SURINAME("苏里南共和国", "the Republic of Suriname", "SR", "597", "SRD", "968"),
    SWEDEN("瑞典王国", "the Kingdom of Sweden", "SE", "46", "SEK", "752"),
    SWITZERLAND("瑞士联邦", "the Swiss Confederation", "CH", "41", "CHF", "756"),
    SYRIA("阿拉伯叙利亚共和国", "the Syrian Arab Republic", "SY", "963", "SYP", "760"),
    TAJIKISTAN("塔吉克斯坦共和国", "the Republic of Tajikistan", "TJ", "992", "TJS", "972"),
    THAILAND("泰王国", "the Kingdom of Thailand", "TH", "66", "THB", "764"),
    TIMOR_LESTE("东帝汶民主共和国", "the Democratic Republic of Timor-Leste", "TL", "670", "USD", "840"),
    TOGO("多哥共和国", "the Togolese Republic", "TG", "228", "XOF", "952"),
    TONGA("汤加王国", "the Kingdom of Tonga", "TO", "676", "TOP", "776"),
    TRINIDAD_TOBAGO("特立尼达和多巴哥共和国", "the Republic of Trinidad and Tobago", "TT", "1-868", "TTD", "780"),
    TUNISIA("突尼斯共和国", "the Republic of Tunisia", "TN", "216", "TND", "788"),
    TURKEY("土耳其共和国", "the Republic of Turkey", "TR", "90", "TRY", "949"),
    TURKMENISTAN("土库曼斯坦", "Turkmenistan", "TM", "993", "TMT", "934"),
    TUVALU("图瓦卢", "Tuvalu", "TV", "688", "AUD", "36"),
    UGANDA("乌干达共和国", "the Republic of Uganda", "UG", "256", "UGX", "800"),
    UKRAINE("乌克兰", "Ukraine", "UA", "380", "UAH", "980"),
    UNITED_ARAB_EMIRATES("阿拉伯联合酋长国", "the United Arab Emirates", "AE", "971", "AED", "784"),
    UNITED_KINGDOM("大不列颠及北爱尔兰联合王国", "the United Kingdom of Great Britain and Northern Ireland", "GB", "44", "GBP", "826"),
    TANZANIA("坦桑尼亚联合共和国", "the United Republic of Tanzania", "TZ", "255", "TZS", "834"),
    UNITED_STATES("美利坚合众国", "the United States of America", "US", "1", "USD", "840"),
    URUGUAY("乌拉圭东岸共和国", "the Eastern Republic of Uruguay", "UY", "598", "UYU,UYW", "858,927"),
    UZBEKISTAN("乌兹别克斯坦共和国", "the Republic of Uzbekistan", "UZ", "998", "UZS", "860"),
    VANUATU("瓦努阿图共和国", "the Republic of Vanuatu", "VU", "678", "VUV", "548"),
    VENEZUELA("委内瑞拉玻利瓦尔共和国", "the Bolivarian Republic of Venezuela", "VE", "58", "VES,VED", "928,926"),
    VIETNAM("越南社会主义共和国", "the Socialist Republic of Viet Nam", "VN", "84", "VND", "704"),
    YEMEN("也门共和国", "the Republic of Yemen", "YE", "967", "YER", "886"),
    ZAMBIA("赞比亚共和国", "the Republic of Zambia", "ZM", "260", "ZMW", "967"),
    ZIMBABWE("津巴布韦共和国", "the Republic of Zimbabwe", "ZW", "263", "ZWG", "924");

    /**
     * 中文名称
     */
    private final String chineseName;

    /**
     * 英文名称
     */
    private final String englishName;

    /**
     * 国家/地区简称 (ISO 3166-1 alpha-2)
     */
    private final String abbreviation;

    /**
     * 电话代码
     */
    private final String telCode;

    /**
     * 货币代码 (ISO 4217)
     */
    private final String currencyCode;

    /**
     * 货币数字代码 (ISO 4217)
     */
    private final String currencyNumericCode;

    /**
     * 实现DictInf接口的方法 - 使用中文名称作为标签
     */
    @Override
    public String getLabel() {
        return chineseName+"(+"+telCode+")";
    }

    /**
     * 实现DictInf接口的方法 - 使用简称作为值
     */
    @Override
    public String getValue() {
        return telCode;
    }

    /**
     * 根据电话区号获取国家枚举
     */
    public static CountryEnum fromTelCode(String telCode) {
        for (CountryEnum country : values()) {
            if (country.telCode.equalsIgnoreCase(telCode)) {
                return country;
            }
        }
        return null;
    }

    /**
     * 获取电话代码列表（处理多个电话代码的情况）
     */
    public List<String> getTelCodes() {
        return Arrays.asList(telCode.split(","));
    }

    /**
     * 获取货币代码列表（处理多个货币代码的情况）
     */
    public List<String> getCurrencyCodes() {
        return Arrays.asList(currencyCode.split(","));
    }

    /**
     * 获取货币数字代码列表（处理多个货币数字代码的情况）
     */
    public List<String> getCurrencyNumericCodes() {
        return Arrays.asList(currencyNumericCode.split(","));
    }

    /**
     * 获取所有国家选项（用于字典接口）
     */
    public static List<CountryOption> getAllCountryOptions() {
        return Arrays.stream(values())
                .map(country -> new CountryOption(
                        country.getChineseName(),
                        country.getEnglishName(),
                        country.getAbbreviation(),
                        country.getTelCode(),
                        country.getCurrencyCode(),
                        country.getCurrencyNumericCode()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 国家选项内部类（用于字典接口返回）
     */
    @Getter
    @AllArgsConstructor
    public static class CountryOption {
        private final String chineseName;
        private final String englishName;
        private final String abbreviation;
        private final String telCode;
        private final String currencyCode;
        private final String currencyNumericCode;
    }
}