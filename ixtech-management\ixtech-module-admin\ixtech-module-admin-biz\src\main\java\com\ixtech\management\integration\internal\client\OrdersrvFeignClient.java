package com.ixtech.management.integration.internal.client;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.*;
import com.ixtech.management.repo.entity.JipinzucheCarOrder;
import com.ixtech.management.repo.entity.JipinzucheCarStock;
import com.ixtech.management.repo.entity.JipinzucheCarStockInsurance;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 车辆相关服务 Feign 客户端接口
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Component
@FeignClient(name = "ordersrv")
public interface OrdersrvFeignClient {
    /**
     * 订单预定
     *
     * @param request request
     * @return resp
     */
    @PostMapping("/v1/ordersrv/internal/order/veh_res")
    ApiResponse<RentalResp> vehRes(@RequestBody @Valid RentalRequestReq request);

    /**
     * 订单查询
     *
     * @param req request
     * @return 结果
     */
    @PostMapping("/v1/ordersrv/internal/order/veh_res_status_search")
    ApiResponse<RentalResp> vehResStatusSearch(@RequestBody @Valid OrderQueryReq req);

    /**
     * 门店查询
     *
     * @param req request
     * @return 结果
     */
    @PostMapping("/management/v1/ordersrv/internal/store/store_search")
    ApiResponse<StoreResp> storeSearch(@RequestBody @Valid StoreQueryReq req);

    /**
     * 订单信息入库
     *
     * @param req request
     * @return 结果
     * @throws InterruptedException
     */
    @PostMapping("/v1/ordersrv/internal/order/veh_add")
    ApiResponse<Integer> vehAdd(@RequestBody @Valid CarOrderOrderSrvVehAddReq req);

    @PostMapping("/v1/ordersrv/internal/stock/veh_car_stock")
    ApiResponse<JipinzucheCarStockResp> vehCarStock(@RequestBody @Valid StockRangeReq req);

    @PostMapping("/management/v1/ordersrv/internal/carstockinsurance/store_search")
    ApiResponse<JipinzucheCarStockInsurance> stockInsuranceSearch(@RequestBody @Valid StockInsuranceQueryReq req);

    @PostMapping("/v1/ordersrv/internal/order/veh_order_list")
    ApiResponse<ManagementCarOrderListPageResp> vehOrderList(@RequestBody @Valid CarOrderListQueryReq req);

    @GetMapping("/v1/ordersrv/internal/order/veh_order_info")
    ApiResponse<CarOrderInfoResp> vehOrderInfo(@RequestParam(value = "id") Long id) ;

    @PostMapping("/v1/ordersrv/internal/order/clear_vendor_cache")
    ApiResponse clearVendorCache(@RequestBody VendorClearCacheReq req);

    @PostMapping("/v2/ordersrv/api/rental_order/list_by_page")
    ApiResponse<PageResult<OrderQueryResp>> listByPage(@RequestBody OrderPageQueryReq req);

    @PostMapping("/v2/ordersrv/api/rental_order/detail")
     ApiResponse<MerchantRentalOrderResp> getOrderDetail(@RequestBody MerchantRentalOrderDetailReq req);


    /**
     * 历史订单转移至新表
     * @param req 订单转移请求
     * @return 是否成功
     */
    @PostMapping("/v1/ordersrv/internal/order/history_order_transfer")
    ApiResponse<Boolean> orderTransfer(@RequestBody OrderTransferReq req);


}
