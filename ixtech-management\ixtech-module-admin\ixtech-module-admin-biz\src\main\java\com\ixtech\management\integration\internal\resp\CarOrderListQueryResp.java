package com.ixtech.management.integration.internal.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 车辆订单列表响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 21:20
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CarOrderListQueryResp {

    /**
     * 订单id
     */
    @ExcelProperty("订单id")
    private Long id;

    /**
     * 订单号
     */
    @ExcelProperty("订单来源编号")
    private String sourceOrderCode;

    /**
     * 订单创建方式（字符串描述）
     */
    @ExcelProperty("订单创建方式")
    private String createTypeStr;

    /**
     * 订单来源（字符串描述）
     */
    @ExcelProperty("订单来源")
    private String source;

    /**
     * 订单类型（字符串描述）
     */
    @ExcelProperty("订单类型")
    private String orderType;

    /**
     * 供应商id
     */
    @ExcelProperty("供应商id")
    private Long vendorId;

    /**
     * 供应商名称
     */
    @ExcelProperty("供应商名称")
    private String vendorName;

    /**
     * 门店id
     */
    @ExcelProperty("门店id")
    private String storeId;

    /**
     * 门店名称
     */
    @ExcelProperty("门店名称")
    private String storeName;

    /**
     * 预计取车时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private LocalDateTime appointStartTime;

    @ExcelProperty("租车开始时间")
    private String appointStartTimeStr;

    /**
     * 预计还车时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private LocalDateTime appointEndTime;

    @ExcelProperty("租车结束时间")
    private String appointEndTimeStr;

    /**
     * 车型名称
     */
    @ExcelProperty("车型名称")
    private String modelsName;

    /**
     * 总金额
     */
    @ExcelProperty("总金额")
    private String totalAmountStr;

    /**
     * 货币类型
     */
//    private String amountCurrency;

    /**
     * 订单状态（字符串描述）
     */
    @ExcelProperty("订单状态")
    private String statusStr;

    /**
     * 订单创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private LocalDateTime time;
    @ExcelProperty("订单创建时间")
    private String timeStr;

}
