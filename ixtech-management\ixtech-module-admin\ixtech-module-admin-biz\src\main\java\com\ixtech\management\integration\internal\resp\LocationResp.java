package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 地点信息对象，用于表示取车或还车地点。
 */
@Data
public class LocationResp {

    /**
     * 地点代码上下文，例如 "IATA"。
     */
    @JsonProperty("location_code_context")
    private String locationCodeContext;

    /**
     * 取车或还车地点代码，例如 "SFOC28"。
     */
    @JsonProperty("location_code")
    private String locationCode;
}
