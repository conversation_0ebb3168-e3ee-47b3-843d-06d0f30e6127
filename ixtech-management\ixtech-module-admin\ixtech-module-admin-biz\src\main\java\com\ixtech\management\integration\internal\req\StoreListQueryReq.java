package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.ixtech.management.common.dto.PageRequest;
import lombok.Data;

import java.util.List;

/**
 * 门店列表请求Req
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:55
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StoreListQueryReq extends PageRequest {

    /**
     * 门店名称
     */
    private String name;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 省份id
     */
    private Long provinceId;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 门店类型 1：城市 2：机场 3：酒店
     */
    private List<Integer> types;

    /**
     * 门店上下线状态 true：在线 false：下线 不传表示查询全部状态
     */
    private Boolean active;

}
