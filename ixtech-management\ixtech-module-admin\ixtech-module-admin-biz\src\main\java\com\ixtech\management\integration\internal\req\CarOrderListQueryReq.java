package com.ixtech.management.integration.internal.req;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import lombok.Data;

import java.util.List;

@Data
public class CarOrderListQueryReq extends PageParam {

    private List<String> timeRange;

    private String sourceOrdercode;

    private List<Integer> createTypes;

    private Integer source;

    private Long vendor;

    private Integer manageOrderStatus;

    private List<Integer> orderStatus;

    private Integer createTimeOrderBy;

    private Integer appointStartTimeOrderBy;

    private Integer appointEndTimeOrderBy;

    private Integer totalpriceOrderBy;

    private Long limitStart;

    private Long limitSize;

}
