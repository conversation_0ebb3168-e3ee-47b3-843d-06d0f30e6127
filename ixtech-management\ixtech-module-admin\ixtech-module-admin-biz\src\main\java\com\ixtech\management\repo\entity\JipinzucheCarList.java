package com.ixtech.management.repo.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 车辆列表
 * @TableName jipinzuche_car_list
 */
@Data
public class JipinzucheCarList implements Serializable {
    /**
     * 
     */
    private Integer id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * #__stock 的 id
     */
    private Integer stockid;

    /**
     * 车牌号码
     */
    private String platecode;

    /**
     * 车辆颜色
     */
    private String color;

    /**
     * 行驶总里程（km）
     */
    private Double mileage;

    /**
     * 每次保养里程
     */
    private Double maintain;

    /**
     * 发动机号码
     */
    private String enginecode;

    /**
     * 车架号码
     */
    private String framecode;

    /**
     * 燃油型号
     */
    private String fuelmodel;

    /**
     * 添加时间
     */
    private Integer time;

    /**
     * 添加ip
     */
    private String ip;

    /**
     * 添加人
     */
    private Integer mid;

    /**
     * 使用次数
     */
    private Integer ordertimes;

    /**
     * -1->删除；1->有效；2->维修中；3->保养中
     */
    private Integer status;

    /**
     * 车辆gps的imei
     */
    private String gpsImei;

    /**
     * 起赔额（以车辆所属国家所在货币为准）
     */
    private Double accident;

    /**
     * 车辆日均价
     */
    private Double dayprice;

    private static final long serialVersionUID = 1L;
}