package com.ixtech.management.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据分页结果集response
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
public class PageResponse<T> implements Serializable {

    private static final long serialVersionUID = 8656597559014685635L;
    /**
     * 总记录数
     */
    private long total;
    /**
     * 当前页
     */
    private long current;

    /**
     * 结果集
     */
    private List<T> list = new ArrayList<>();


    public static <T> PageResponse<T> success(List<T> list, long total, long current) {
        PageResponse<T> res = new PageResponse<>();
        res.setList(list);
        res.setTotal(total);
        res.setCurrent(current);
        return res;
    }

}