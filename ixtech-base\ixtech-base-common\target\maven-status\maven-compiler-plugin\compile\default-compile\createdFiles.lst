com\ixtech\global\common\serialize\JacksonHttpMessageConverter$MyBeanSerializerModifier.class
com\ixtech\global\common\enums\FreeDelayReturnTimeUnitEnum.class
com\ixtech\global\common\enums\transfer\TranferVehicleStatusEnum.class
com\ixtech\global\common\enums\FuelTypeEnum.class
com\ixtech\global\common\enums\transfer\TransferSourceCodeEnum.class
com\ixtech\global\common\valid\EnumValueValidator.class
com\ixtech\global\common\biz\rental\utils\ReferenceIdUtil.class
com\ixtech\global\common\enums\VehicleDesignModelEnum.class
com\ixtech\global\common\dto\PageRequest.class
com\ixtech\global\common\biz\rental\constant\NormalConstant.class
com\ixtech\global\common\context\SpringContextHolder.class
com\ixtech\global\common\serialize\JacksonHttpMessageConverter$NullNumberJsonSerializer.class
com\ixtech\global\common\enums\EnumFillType.class
com\ixtech\global\common\dto\ErrorData.class
com\ixtech\global\common\enums\ChannelBrandEnum.class
com\ixtech\global\common\enums\OrderChargeTypeEnum.class
com\ixtech\global\common\utils\RangeUtils.class
com\ixtech\global\common\valid\Update.class
com\ixtech\global\common\dto\TimeInterval.class
com\ixtech\global\common\exception\ServerException.class
com\ixtech\global\common\serialize\TimestampToLocalDateTimeDeserializer.class
com\ixtech\global\common\annotation\JacksonFill.class
com\ixtech\global\common\utils\BeanUtils.class
com\ixtech\global\common\enums\transfer\TransferOrderTypeEnum.class
com\ixtech\global\common\context\DictConfiguration$1.class
com\ixtech\global\common\filter\ChannelContextWebFilter.class
com\ixtech\global\common\enums\transfer\TransferOperatorTerminalEnum.class
com\ixtech\global\common\enums\MileageUnitEnum.class
com\ixtech\global\common\enums\transfer\TransferPaymentStatusEnum.class
com\ixtech\global\common\enums\OrderTypeEnum.class
com\ixtech\global\common\utils\ObfuscationUtils.class
com\ixtech\global\common\enums\DrivingLicenseTypeEnum.class
com\ixtech\global\common\enums\StoreChargeMethodEnum.class
com\ixtech\global\common\utils\LocalDateTimeUtils.class
com\ixtech\global\common\listener\event\ApplicationStartCheckEvent.class
com\ixtech\global\common\enums\CurrencyEnum.class
com\ixtech\global\common\enums\RentUnitEnum.class
com\ixtech\global\common\listener\validator\ApplicationStartValidator.class
com\ixtech\global\common\context\DictConfiguration.class
com\ixtech\global\common\enums\StoreStatusEnum.class
com\ixtech\global\common\enums\inf\DictInf.class
com\ixtech\global\common\serialize\JacksonHttpMessageConverter$NullBooleanJsonSerializer.class
com\ixtech\global\common\utils\ExcelUtils.class
com\ixtech\global\common\enums\PriceTypeEnum.class
com\ixtech\global\common\utils\DataDigestUtil.class
com\ixtech\global\common\enums\TransportationMethodEnum.class
com\ixtech\global\common\utils\JsonUtils.class
com\ixtech\global\common\serialize\JacksonHttpMessageConverter$BigDecimalDeserializer.class
com\ixtech\global\common\enums\DocumentTypeEnum.class
com\ixtech\global\common\valid\Create.class
com\ixtech\global\common\enums\ChargePriceTypeEnum.class
com\ixtech\global\common\enums\LanguageEnum.class
com\ixtech\global\common\exception\handler\ServerExceptionHandler.class
com\ixtech\global\common\enums\SaleStatusEnum.class
com\ixtech\global\common\dto\inf\RangeInterval.class
com\ixtech\global\common\enums\DocumentStatusEnum.class
com\ixtech\global\common\enums\EnabledStatusEnum.class
com\ixtech\global\common\enums\StoreOperationTypeEnum.class
com\ixtech\global\common\serialize\JacksonHttpMessageConverter.class
com\ixtech\global\common\enums\transfer\TransferDispatchStatusEnum.class
com\ixtech\global\common\enums\SnapshotVersionEnum.class
com\ixtech\global\common\enums\SpecialEquipPricingMethodEnum.class
com\ixtech\global\common\config\IdGeneratorProperties.class
com\ixtech\global\common\enums\CancellerTypeEnum.class
com\ixtech\global\common\enums\InsuranceChildTypeEnum.class
com\ixtech\global\common\enums\InsuranceTypeEnum.class
com\ixtech\global\common\enums\VehicleInstantConfirmEnum.class
com\ixtech\global\common\serialize\JacksonHttpMessageConverter$NullObjectJsonSerializer.class
com\ixtech\global\common\enums\FulfillmentStatusEnum.class
com\ixtech\global\common\utils\FeiShuBotUtils.class
com\ixtech\global\common\context\DictConfiguration$2.class
com\ixtech\global\common\dto\PageResponse.class
com\ixtech\global\common\enums\transfer\TransferAmenityServiceTypeEnum.class
com\ixtech\global\common\dto\IxChannelInfo.class
com\ixtech\global\common\enums\PayMethodEnum.class
com\ixtech\global\common\enums\transfer\TransferOrderChargeTypeEnum.class
com\ixtech\global\common\enums\RentalPaymentMethodEnum.class
com\ixtech\global\common\enums\transfer\TransferPaymentTypeEnum.class
com\ixtech\global\common\constant\IxtechConstants.class
com\ixtech\global\common\enums\ShuttleTimeUnitEnum.class
com\ixtech\global\common\utils\FileUtils$1.class
com\ixtech\global\common\enums\SupportStatusEnum.class
com\ixtech\global\common\biz\rental\constant\NormalConstant$Klook.class
com\ixtech\global\common\annotation\ValidEnum.class
com\ixtech\global\common\enums\transfer\TranferVehicleColorEnum.class
com\ixtech\global\common\enums\MarkTypeEnum.class
com\ixtech\global\common\enums\transfer\TransferOrderCreateTypeEnum.class
com\ixtech\global\common\utils\StrUtils.class
com\ixtech\global\common\enums\VehicleAttributionEnum.class
com\ixtech\global\common\enums\VehicleOnlineStatusEnum.class
com\ixtech\global\common\enums\ScheduleTypeEnum.class
com\ixtech\global\common\enums\StoreLocationTypeEnum.class
com\ixtech\global\common\enums\transfer\TransferVehicleOwnerEnum.class
com\ixtech\global\common\filter\VendorContextWebFilter.class
com\ixtech\global\common\enums\transfer\TransferOrderStatusEnum.class
com\ixtech\global\common\biz\rental\bo\ReferenceIdBO.class
com\ixtech\global\common\enums\RentalInsuranceTypeEnum.class
com\ixtech\global\common\enums\transfer\TransferServiceTypeEnum.class
com\ixtech\global\common\enums\FuelPolicyEnum.class
com\ixtech\global\common\context\ChannelContextHolder.class
com\ixtech\global\common\enums\OrderLogOperationTypeEnum.class
com\ixtech\global\common\exception\inf\ErrorCode.class
com\ixtech\global\common\interceptor\UserInitInterceptor.class
com\ixtech\global\common\serialize\JacksonHttpMessageConverter$NullArrayJsonSerializer.class
com\ixtech\global\common\enums\InsuranceStatusEnum.class
com\ixtech\global\common\enums\transfer\TransferOrderContactTypeEnum.class
com\ixtech\global\common\enums\transfer\TransferEstimatedMileageUnitEnum.class
com\ixtech\global\common\utils\FileUtils.class
com\ixtech\global\common\enums\DayOfWeekEnum$1.class
com\ixtech\global\common\dto\VendorInfo.class
com\ixtech\global\common\dto\ApiResponse.class
com\ixtech\global\common\utils\AES256Utils.class
com\ixtech\global\common\enums\transfer\TransferContactTargetTypeEnum.class
com\ixtech\global\common\utils\Md5Utils.class
com\ixtech\global\common\exception\enums\CommonErrorCode.class
com\ixtech\global\common\utils\CollUtils.class
com\ixtech\global\common\context\OnlineContext$Subject.class
com\ixtech\global\common\dto\inf\ScheduleInf.class
com\ixtech\global\common\exception\handler\GlobalExceptionHandler.class
com\ixtech\global\common\enums\DayOfWeekEnum.class
com\ixtech\global\common\enums\transfer\TransferMessageSendTypeEnum.class
com\ixtech\global\common\serialize\LocalDateTimeToTimestampSerializer.class
com\ixtech\global\common\enums\RentalOrderStatusEnum.class
com\ixtech\global\common\enums\transfer\TransferEstimatedTimeUnitEnum.class
com\ixtech\global\common\serialize\JacksonHttpMessageConverter$NullStringJsonSerializer.class
com\ixtech\global\common\context\VendorContextHolder.class
com\ixtech\global\common\enums\transfer\TransferDispatchTypeEnum.class
com\ixtech\global\common\enums\TransmissionEnum.class
com\ixtech\global\common\config\IdGeneratorConfig.class
com\ixtech\global\common\enums\StoreTypeEnum.class
com\ixtech\global\common\dto\RangeResult.class
com\ixtech\global\common\enums\OrderFulfillmentTypeEnum.class
com\ixtech\global\common\enums\VehicleConditionEnum.class
com\ixtech\global\common\enums\ChannelCooperationStatusEnum.class
com\ixtech\global\common\enums\transfer\TransferMessageSendStatusEnum.class
com\ixtech\global\common\enums\PlatformOrderStatusEnum.class
com\ixtech\global\common\enums\CountryEnum$CountryOption.class
com\ixtech\global\common\enums\FileTaskStatusEnum.class
com\ixtech\global\common\enums\SiteTypeEnum.class
com\ixtech\global\common\enums\RangeReserveStatusEnum.class
com\ixtech\global\common\enums\RentalPaymentStatusEnum.class
com\ixtech\global\common\exception\ApiException.class
com\ixtech\global\common\interceptor\RequestLogInterceptor.class
com\ixtech\global\common\utils\DateTimeUtils.class
com\ixtech\global\common\context\OnlineContext.class
com\ixtech\global\common\enums\CountryEnum.class
com\ixtech\global\common\enums\transfer\TransferNotifyStatusEnum.class
