package com.ixtech.management.repo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 租车订单的来源
 *
 * <AUTHOR> hu
 * @date 2025/4/18 14:22
 */
@Data
@NoArgsConstructor
public class CarOrderSource extends BaseEntity {

    /**
     * 名称
     */
    private String title;

    /**
     * api的 stage
     */
    private String stage;

    /**
     * 0->未删除；1->删除
     */
    private Boolean isDelete;

    /**
     * 公司全称
     */
    private String companyFullName;

    /**
     * 报价货币
     */
    private String quoteCurrency;

    /**
     * 结算货币
     */
    private String settlementCurrency;

    /**
     * 结算模式 1:底价模式 2:抽佣模式
     */
    private Byte settlementMode;

    /**
     * 比例x%
     */
    private BigDecimal rate;

    /**
     * api对接key
     */
    private String credentialKey;

}