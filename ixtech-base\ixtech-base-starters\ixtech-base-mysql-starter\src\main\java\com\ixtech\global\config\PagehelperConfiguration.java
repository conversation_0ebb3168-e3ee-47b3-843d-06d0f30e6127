package com.ixtech.global.config;

import com.github.pagehelper.PageInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

@Configuration
public class PagehelperConfiguration {
    /**
     * PageHelper 分页插件
     *
     * @return
     */
    @Bean
    @Order(0)
    public PageInterceptor pageInterceptor() {
        return new PageInterceptor();
    }
}
