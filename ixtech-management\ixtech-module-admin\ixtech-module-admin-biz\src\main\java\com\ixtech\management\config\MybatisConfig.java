package com.ixtech.management.config;

import com.github.pagehelper.PageInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR> hu
 * @date 2025/4/5 15:35
 */
@Configuration
public class MybatisConfig {

    /**
     * PageHelper 分页插件
     *
     * @return
     */
    @Bean
    @Order(0)
    public PageInterceptor pageInterceptor() {
        return new PageInterceptor();
    }

}
