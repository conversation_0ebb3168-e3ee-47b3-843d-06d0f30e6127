package com.ixtech.management.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.common.enums.AreaCategoryEnum;
import com.ixtech.management.common.enums.BaseEnum;
import com.ixtech.management.common.enums.StoreTypeEnum;
import com.ixtech.management.domain.service.StoreService;
import com.ixtech.management.integration.internal.req.StoreDropdownReq;
import com.ixtech.management.integration.internal.req.StoreListQueryReq;
import com.ixtech.management.integration.internal.resp.AreaSelectOptionResp;
import com.ixtech.management.integration.internal.resp.StoreInfoResp;
import com.ixtech.management.integration.internal.resp.StoreListInfoResp;
import com.ixtech.management.repo.entity.*;
import com.ixtech.management.repo.model.CountModel;
import com.ixtech.management.repo.repository.AreaRepository;
import com.ixtech.management.repo.repository.CarBrandRepository;
import com.ixtech.management.repo.repository.StoreRepository;
import com.ixtech.management.repo.repository.VendorRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 门店service
 *
 * <AUTHOR> hu
 * @date 2025/4/8 15:09
 */
@Slf4j
@Service
public class StoreServiceImpl implements StoreService {

    @Resource
    private StoreRepository storeRepository;
    @Resource
    private VendorRepository vendorRepository;
    @Resource
    private AreaRepository areaRepository;
    @Resource
    private CarBrandRepository carBrandRepository;

    @Override
    public PageResponse<StoreListInfoResp> list(StoreListQueryReq storeListQueryReq) {

        int pageNum = storeListQueryReq.getPageNum(), pageSize = storeListQueryReq.getPageSize();
        Page<Object> page = PageHelper.startPage(pageNum, pageSize, storeListQueryReq.isNeedTotalCount());

        // 按条件查询供应商列表
        Integer displayStatus = storeListQueryReq.getActive() != null
                ? storeListQueryReq.getActive() ? 1 : -1
                : null;
        List<Store> stores = storeRepository.searchByCondition(
                storeListQueryReq.getName(), storeListQueryReq.getCountryId(), storeListQueryReq.getProvinceId(), storeListQueryReq.getCityId(),
                storeListQueryReq.getVendorId(), storeListQueryReq.getTypes(), displayStatus
        );

        // 门店ID列表
        Set<Long> storeIds = new HashSet<>();
        // 供应商ID列表
        Set<Long> vendorIds = new HashSet<>();
        // 门店区域ID列表
        Set<Long> areaIds = new HashSet<>();
        List<StoreListInfoResp> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stores)) {
            stores.forEach(store -> {
                Optional.ofNullable(Long.valueOf(store.getId())).ifPresent(storeIds::add);
                Optional.ofNullable(store.getVendorId()).ifPresent(vendorIds::add);
                Optional.ofNullable(Long.valueOf(store.getCountryid())).ifPresent(areaIds::add);
                Optional.ofNullable(Long.valueOf(store.getProvinceid())).ifPresent(areaIds::add);
                Optional.ofNullable(Long.valueOf(store.getCityid())).ifPresent(areaIds::add);
                StoreListInfoResp storeListInfoResp = convert2StoreListInfoResp(store);
                list.add(storeListInfoResp);
            });
        }

        // 获取门店车辆数量
        Map<Long, CountModel> carCountMap = storeRepository.getStoreCarCountByStoreIds(storeIds);
        // 获取国家/城市信息
        Map<Long, Area> areaMap = areaRepository.selectByIds(areaIds);
        // 获取供应商信息
        Map<Long, Vendor> vendorMap = vendorRepository.selectByIds(vendorIds);

        // 合并数据
        enrichStoreListInfo(list, carCountMap, areaMap, vendorMap);

        return PageResponse.success(list, page.getTotal(), pageNum);
    }

    /**
     * 将store实体对象转换为StoreListInfoResp对象
     *
     * @param store
     * @return
     */
    private StoreListInfoResp convert2StoreListInfoResp(Store store) {
        if (store == null) {
            return null;
        }
        StoreListInfoResp storeListInfo = new StoreListInfoResp();
        BeanUtils.copyProperties(store, storeListInfo);
        // 门店上下线（在接口中显示门店信息：上线，在接口中不显示门店信息：下线）
        storeListInfo.setActive(Objects.equals(store.getDisplaystatus(), 1));
        // 填充国家/省份/城市ID
        storeListInfo.setCountryId(Long.valueOf(store.getCountryid()));
        storeListInfo.setProvinceId(Long.valueOf(store.getProvinceid()));
        storeListInfo.setCityId(Long.valueOf(store.getCityid()));
        // 门店类型
        StoreTypeEnum storeTypeEnum = BaseEnum.fromCode(StoreTypeEnum.class, store.getType());
        Optional.ofNullable(storeTypeEnum).ifPresent(t -> storeListInfo.setTypeStr(t.getName()));
        return storeListInfo;
    }

    /**
     * 门店列表数据填充
     *
     * @param storeList   门店列表
     * @param carCountMap 门店车辆数量
     * @param areaMap     区域信息
     * @param vendorMap   供应商信息
     */
    private void enrichStoreListInfo(List<StoreListInfoResp> storeList,
                                     Map<Long, CountModel> carCountMap,
                                     Map<Long, Area> areaMap,
                                     Map<Long, Vendor> vendorMap) {

        if (CollectionUtils.isEmpty(storeList)) {
            return;
        }

        storeList.forEach(store -> {
            // 填充车辆数量
            CountModel.setCountFields(
                    carCountMap.get(store.getId()),
                    store::setCarCount,
                    store::setActiveCarCount
            );
            // 填充国家名称
            Optional.ofNullable(areaMap.get(store.getCountryId())).map(Area::getName)
                    .ifPresent(store::setCountry);
            // 填充省份名称
            Area province = areaMap.get(store.getProvinceId());
            Optional.ofNullable(province).map(Area::getName)
                    .ifPresent(store::setProvince);
            // 填充城市名称（处理城市名称为"市辖区"的场景，替换为省份的名称）
            Optional.ofNullable(areaMap.get(store.getCityId())).map(city ->
                            "市辖区".equals(city.getName()) && province != null ? province.getName() : city.getName())
                    .ifPresent(store::setCity);
            // 填充供应商名称
            Optional.ofNullable(vendorMap.get(store.getVendorId())).map(Vendor::getName)
                    .ifPresent(store::setVendorName);
        });

    }

    @Override
    public StoreInfoResp info(Long id) {

        Store store = storeRepository.selectById(id);
        if (store == null) {
            return null;
        }

        StoreInfoResp storeInfoResp = new StoreInfoResp();
        BeanUtils.copyProperties(store, storeInfoResp);
        // 门店上下线状态
        storeInfoResp.setActive(Objects.equals(store.getDisplaystatus(), 1));
        // 填充国家/省份/城市ID
        storeInfoResp.setCountryId(Long.valueOf(store.getCountryid()));
        storeInfoResp.setProvinceId(Long.valueOf(store.getProvinceid()));
        storeInfoResp.setCityId(Long.valueOf(store.getCityid()));
        // 经纬度
        Optional.ofNullable(store.getLon()).ifPresent(k -> storeInfoResp.setLon(new BigDecimal(k.toString())));
        Optional.ofNullable(store.getLat()).ifPresent(k -> storeInfoResp.setLat(new BigDecimal(k.toString())));

        // 门店类型
        StoreTypeEnum storeTypeEnum = BaseEnum.fromCode(StoreTypeEnum.class, store.getType());
        Optional.ofNullable(storeTypeEnum).ifPresent(t -> storeInfoResp.setTypeStr(t.getName()));

        // 补充车辆信息
        fillCarInfo(storeInfoResp);
        // 补充国家/城市信息
        fillAreaInfo(storeInfoResp);
        // 补充供应商信息
        fillVendorInfo(storeInfoResp);

        return storeInfoResp;
    }

    /**
     * 补充车辆信息
     *
     * @param storeInfoResp
     */
    private void fillCarInfo(StoreInfoResp storeInfoResp) {
        Long id = storeInfoResp.getId();
        // 获取门店车辆数量
        Map<Long, CountModel> carCountMap = storeRepository.getStoreCarCountByStoreIds(Collections.singletonList(id));
        // 填充车辆数量
        CountModel.setCountFields(
                carCountMap.get(id),
                storeInfoResp::setCarCount,
                storeInfoResp::setActiveCarCount
        );
        // 覆盖车型
        List<CarModelPO> carModels = storeRepository.getCarModelsByStoreId(id);
        // 品牌列表
        Set<Long> brandIds = CollectionUtils.emptyIfNull(carModels).stream()
                .map(CarModelPO::getBrandid).collect(Collectors.toSet());
        Map<Long, CarBrand> carBrandMap = carBrandRepository.selectByIds(brandIds);
        // 拼接品牌名称-车型名称
        List<String> carModelsNames = CollectionUtils.emptyIfNull(carModels).stream()
                .map(m -> {
                    return Optional.ofNullable(carBrandMap.get(m.getBrandid()))
                            .map(brand -> brand.getName() + "-" + m.getName()).orElse(m.getName());
                }).distinct().collect(Collectors.toList());
        storeInfoResp.setCarModels(carModelsNames);
    }

    /**
     * 补充国家/城市信息
     *
     * @param storeInfoResp
     */
    private void fillAreaInfo(StoreInfoResp storeInfoResp) {

        // 获取地区信息
        Long countryId = storeInfoResp.getCountryId(), provinceId = storeInfoResp.getProvinceId(), cityId = storeInfoResp.getCityId();
        Set<Long> areaIds = Stream.of(countryId, provinceId, cityId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, Area> areaMap = areaRepository.selectByIds(areaIds);

        // 填充国家名称
        Optional.ofNullable(areaMap.get(countryId)).map(Area::getName)
                .ifPresent(storeInfoResp::setCountry);
        // 填充省份名称
        Area province = areaMap.get(provinceId);
        Optional.ofNullable(province).map(Area::getName)
                .ifPresent(storeInfoResp::setProvince);
        // 填充城市名称 (处理城市名称为"市辖区"的场景，替换为省份的名称)
        Optional.ofNullable(areaMap.get(cityId)).map(city ->
                        "市辖区".equals(city.getName()) && province != null ? province.getName() : city.getName())
                .ifPresent(storeInfoResp::setCity);

    }

    /**
     * 补充供应商信息
     *
     * @param storeInfoResp
     */
    private void fillVendorInfo(StoreInfoResp storeInfoResp) {
        Vendor vendor = vendorRepository.selectById(storeInfoResp.getVendorId());
        Optional.ofNullable(vendor).map(Vendor::getName).ifPresent(storeInfoResp::setVendorName);
    }

    @Override
    public List<SelectOptionResponse<Long>> dropdownList(StoreDropdownReq storeDropdownReq) {
        List<Store> list = storeRepository.searchByCondition(storeDropdownReq.getName(),
                null, null, null
                , storeDropdownReq.getVendorid() != null ? storeDropdownReq.getVendorid() : null
                , null, null);
        return CollectionUtils.emptyIfNull(list).stream()
                .map(s -> new SelectOptionResponse<>(s.getId().longValue(), s.getName()))
                .toList();
    }

    @Override
    public List<AreaSelectOptionResp> areaDropdownList() {

        // 获取门店的全部区域
        List<Area> areaList = areaRepository.queryAllStoreArea();
        List<AreaSelectOptionResp> areaSelectOptions = CollectionUtils.emptyIfNull(areaList).stream()
                .map(a -> {
                    AreaSelectOptionResp areaSelectOption = new AreaSelectOptionResp();
                    areaSelectOption.setParentId(a.getParentid());
                    areaSelectOption.setId(a.getId());
                    areaSelectOption.setName(a.getName());
                    areaSelectOption.setCategory(a.getCategory());
                    return areaSelectOption;
                }).toList();

        // 构建树形结构
        areaSelectOptions = convertToTreeOptimized(areaSelectOptions);
        // 去除“省份”，仅保留“国家”和“城市”
        areaSelectOptions = flattenProvinceLevelWithStream(areaSelectOptions);
        return areaSelectOptions;
    }

    /**
     * 构建地区下拉选项树形结构
     *
     * @param list
     * @return
     */
    public List<AreaSelectOptionResp> convertToTreeOptimized(List<AreaSelectOptionResp> list) {

        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        // 1.创建ID到节点的映射，并初始化childList
        Map<Long, AreaSelectOptionResp> nodeMap = list.stream()
                .peek(node -> {
                    if (node.getChildList() == null) {
                        node.setChildList(new ArrayList<>());
                    }
                }).collect(Collectors.toMap(AreaSelectOptionResp::getId, Function.identity()));

        // 2.构建父子关系
        list.forEach(node -> {
            Long parentId = node.getParentId();
            if (parentId != null && parentId != 0 && nodeMap.containsKey(parentId)) {
                AreaSelectOptionResp parent = nodeMap.get(parentId);
                // 处理城市名称为"市辖区"的场景，替换为省份的名称
                if (Objects.equals(node.getCategory(), AreaCategoryEnum.CITY.getCode()) && "市辖区".equals(node.getName())) {
                    node.setName(parent.getName());
                }
                parent.getChildList().add(node);
            }
        });

        // 3.返回所有根节点
        return list.stream()
                .filter(node -> {
                    Long parentId = node.getParentId();
                    return parentId == null || parentId == 0 || !nodeMap.containsKey(parentId);
                }).toList();
    }

    /**
     * 将三级区域结构（国家-省-市）转换为二级结构（国家-市）
     *
     * @param originalList
     * @return
     */
    public List<AreaSelectOptionResp> flattenProvinceLevelWithStream(List<AreaSelectOptionResp> originalList) {

        if (CollectionUtils.isEmpty(originalList)) {
            return originalList;
        }

        List<AreaSelectOptionResp> result = new ArrayList<>();
        for (AreaSelectOptionResp selectOption : originalList) {
            if (!Objects.equals(selectOption.getCategory(), AreaCategoryEnum.COUNTRY.getCode())) {
                continue;
            }
            List<AreaSelectOptionResp> cityList = new ArrayList<>();
            List<AreaSelectOptionResp> provinceList = selectOption.getChildList();
            if (CollectionUtils.isEmpty(provinceList)) {
                continue;
            }
            for (AreaSelectOptionResp province : provinceList) {
                List<AreaSelectOptionResp> childList = province.getChildList();
                if (CollectionUtils.isNotEmpty(childList)) {
                    cityList.addAll(childList);
                }
            }
            if (!cityList.isEmpty()) {
                AreaSelectOptionResp country = new AreaSelectOptionResp();
                country.setId(selectOption.getId());
                country.setName(selectOption.getName());
                country.setCategory(selectOption.getCategory());
                country.setChildList(cityList);
                result.add(country);
            }
        }
        return result;
    }

}
