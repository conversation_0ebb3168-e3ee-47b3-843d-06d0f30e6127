package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 指派指派指派消息发送状态枚举
 *
 * <AUTHOR> hu
 * @date 2025/7/23
 */
@Getter
public enum TransferMessageSendStatusEnum {

    UNSENT(1, "未发送"),
    SENT(2, "已发送"),
    SEND_FAILED(3, "发送失败"),
    ;

    TransferMessageSendStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 状态编码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String value;

    // 新增：根据编码获取状态描述
    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferMessageSendStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status.value;
            }
        }
        return null; // 编码不存在时返回null，也可返回"未知状态"等默认值
    }
}
    