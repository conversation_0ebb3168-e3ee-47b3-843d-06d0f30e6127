package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * 门店地点req
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CarStockInsuranceReq implements Serializable {

 /**
  * 地点代码上下文，如 IATA、ARC
  */
 private String title;

 /**
  * 地点代码
  */
 @NotBlank(message = "门店地址代码不能为空")
 private Long stockId;
}
