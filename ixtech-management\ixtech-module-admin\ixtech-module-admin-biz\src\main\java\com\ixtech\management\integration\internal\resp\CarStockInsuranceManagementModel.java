package com.ixtech.management.integration.internal.resp;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 车辆保险 model
 *
 * <AUTHOR>
 * @since 2025-03-24 09:44:00
 */
@Data
public class CarStockInsuranceManagementModel {
    
    private Long id;

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * car_model id
     */
    private Long modelId;

    /**
     * 保险 id
     */
    private Long insuranceId;

    /**
     * 保险名称
     */
    private String title;

    /**
     * 保险code
     */
    private String code;

    /**
     * 保险价格
     */
    private BigDecimal price;
}

