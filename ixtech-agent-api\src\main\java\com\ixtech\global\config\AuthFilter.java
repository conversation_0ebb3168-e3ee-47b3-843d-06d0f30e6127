package com.ixtech.global.config;

import com.ixtech.global.common.constant.IxtechConstants;
import com.ixtech.global.common.context.ChannelContextHolder;
import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.common.utils.JsonUtils;
import com.ixtech.global.domain.service.CarOrderService;
import com.ixtech.global.repo.entity.IxChannelPO;
import jakarta.annotation.Resource;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Setter
@Component
@ConfigurationProperties(prefix = "api.auth")
public class AuthFilter implements Filter {

    // Setter for credentials
    // 存储用户名和密钥的 Map
    //private Map<String, String> credentials;

    // 时间戳有效期（单位：分钟）
    private static final long TIMESTAMP_VALIDITY_MINUTES = 5;

    @Resource(name = "orderServiceV2")
    private CarOrderService carOrderService;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        try {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            if(httpRequest.getRequestURI().startsWith("/actuator/prometheus")){
                chain.doFilter(request, response);
            }

            // 获取请求头参数
            String username = httpRequest.getHeader("username");
            String timestampStr = httpRequest.getHeader("timestamp");
            String nonce = httpRequest.getHeader("nonce");
            String sign = httpRequest.getHeader("sign");

            // 基本参数校验
            if (isEmpty(username) || isEmpty(timestampStr) || isEmpty(nonce) || isEmpty(sign)) {
                sendError(httpResponse, ApiResponse.fail("缺少必要的请求头参数", 400), HttpStatus.BAD_REQUEST);
                return;
            }

            // 渠道获取
            List<IxChannelPO> sourceList = carOrderService.getSourceList();
            Map<String, IxChannelPO> sourceMap = sourceList.stream()
                    .collect(Collectors.toMap(IxChannelPO::getStage,
                            v -> v, (k1, k2) -> k1)
                    );
            IxChannelPO source = sourceMap.get(username);
            // 验证用户名
            if (source == null) {
                sendError(httpResponse, ApiResponse.fail("用户名无效", 401), HttpStatus.UNAUTHORIZED);
                return;
            }

            // 渠道有效性校验，当渠道下线后不再提供接口访问权限
            if (Boolean.FALSE.equals(source.getActive())) {
                sendError(httpResponse, ApiResponse.fail("渠道已下线", 401), HttpStatus.UNAUTHORIZED);
                return;
            }

            // 验证时间戳
            try {
                long timestamp = Long.parseLong(timestampStr);
                long currentTime = System.currentTimeMillis();
                if (Math.abs(currentTime - timestamp) > TimeUnit.MINUTES.toMillis(TIMESTAMP_VALIDITY_MINUTES)) {
                    sendError(httpResponse, ApiResponse.fail("时间戳已过期或无效", 401), HttpStatus.UNAUTHORIZED);
                    return;
                }
            } catch (NumberFormatException e) {
                sendError(httpResponse, ApiResponse.fail("时间戳格式错误", 400), HttpStatus.BAD_REQUEST);
                return;
            }

            // 验证签名
            String key = source.getCredentialKey();
            String headerParams = "username=" + username
                    + "&key=" + key
                    + "&timestamp=" + timestampStr  // 修复 typo: ×tamp -> timestamp
                    + "&nonce=" + nonce;
            String calculatedSign = getMD5String(headerParams);

            if (!sign.equalsIgnoreCase(calculatedSign)) {
                sendError(httpResponse, ApiResponse.fail("签名无效", 401), HttpStatus.UNAUTHORIZED);
                return;
            }

            // 将username设置到请求属性中，以便后续拦截器获取
            request.setAttribute(IxtechConstants.HEADER_USER_SOURCE, username);
            // 对于本agent服务，直接上下文赋值以便获取
            ChannelContextHolder.setChannelId(source.getId());
            ChannelContextHolder.setChannelCode(source.getStage());
            // 验证通过，继续处理请求
            chain.doFilter(request, response);
        } finally {
            ChannelContextHolder.clear();
        }
    }

    private void sendError(HttpServletResponse response, ApiResponse<?> result, HttpStatus status)
            throws IOException {
        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getWriter().write(JsonUtils.stringify(result));
    }

    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    private static String getMD5String(String str) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            return byteArray2HexString(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("未找到MD5算法", e);
        }
    }

    private static String byteArray2HexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        // 测试多用户签名生成
        String username = "klook";
        System.out.println("key:" + getMD5String(getMD5String(username)));
        String key = "9a2173c783f219028dab755a31e82c89";
        Long timestamp = System.currentTimeMillis();
        String nonce = RandomStringUtils.randomAlphanumeric(32);
        String headerParams = "username=" + username
                + "&key=" + key
                + "&timestamp=" + timestamp  // 修复 typo
                + "&nonce=" + nonce;
        String calculatedSign = getMD5String(headerParams);
        System.out.println("username: " + username);
        System.out.println("timestamp: " + timestamp);
        System.out.println("nonce: " + nonce);
        System.out.println("sign: " + calculatedSign);
    }
}
