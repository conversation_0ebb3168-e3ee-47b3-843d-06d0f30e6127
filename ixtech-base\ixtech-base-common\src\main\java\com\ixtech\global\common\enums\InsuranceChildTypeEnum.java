package com.ixtech.global.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 保险子项类型枚举
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Getter
public enum InsuranceChildTypeEnum {

    /*  JDM */
    /**
     * 1-基本险-车辆碰撞险
     */
    BASIC_COLLISION("IX RENTAL", 1, "CDW", "INSC0001", "车辆碰撞险", 1000, 1000, 1500, -1, -1, "正常驾驶租赁车辆期间发生意外事故时，您需要承担起赔额以内的费用，起赔额以上由保险公司承担（不含玻璃、底盘、轮胎、车顶损坏费用）"),

    /**
     * 2-基本险-第三者责任险
     */
    BASIC_THIRD_PARTY("IX RENTAL", 2, "TPL", "INSC0001", "第三者责任险", -1, -1, -1, 210000, 350000, "在正常驾驶租赁车辆期间因意外事故，致使第三方车辆或人员造成伤害，可获得保险公司给予起赔额以上保额以内的赔偿（* 实际赔付范围与标准以门店合同为准）"),

    /**
     * 3-基本险-营业损失赔偿险
     */
    BASIC_BUSINESS_LOSS("IX RENTAL", 3, "NOC", "INSC0001", "营业损失赔偿险", 2800, 2800, 7000, -1, -1, "如车辆因交通事故、失窃、故障、刮擦或其他原因需要维修或清洁时，顾客需支付租车公司相应费用，作为车辆停运损失赔偿（NOC）"),

    /**
     * 4-全额险-车辆碰撞险
     */
    FULL_COLLISION("IX RENTAL", 4, "CDW", "INSC0002", "车辆碰撞险", 0, 0, 0, -1, -1, "如正常驾驶租赁车辆期间发生事故，保险公司将承担全额费用（不含玻璃、底盘、轮胎、车顶损坏）"),

    /**
     * 5-全额险-第三者责任险
     */
    FULL_THIRD_PARTY("IX RENTAL", 5, "TPL", "INSC0002", "第三者责任险", -1, -1, -1, 210000, 350000, "在正常驾驶租赁车辆期间因意外事故，致使第三方车辆或人员造成伤害，可获得保险公司给予起赔额以上保额以内的赔偿（* 实际赔付范围与标准以门店合同为准）"),

    /**
     * 6-综合险-车辆碰撞险
     */
    COMPOSITE_COLLISION("IX RENTAL", 6, "CDW", "INSC0003", "车辆碰撞险", 0, 0, 0, -1, -1, "正常驾驶租赁车辆期间发生意外事故时，客人需要承担起赔额以内的费用，起赔额以上由保险公司承担（不含玻璃、底盘、轮胎、车顶损坏费用）"),

    /**
     * 7-综合险-第三者责任险
     */
    COMPOSITE_THIRD_PARTY("IX RENTAL", 7, "TPL", "INSC0003", "第三者责任险", -1, -1, -1, 210000, 350000, "在正常驾驶租赁车辆期间因意外事故，致使第三方车辆或人员造成伤害，可获得保险公司给予起赔额以上保额以内的赔偿（* 实际赔付范围与标准以门店合同为准）"),

    /**
     * 8-综合险-营业损失赔偿险
     */
    COMPOSITE_BUSINESS_LOSS("IX RENTAL", 8, "NOC", "INSC0003", "营业损失赔偿险", 0, 0, 0, -1, -1, "如车辆因交通事故、失窃、故障、刮擦或其他原因需要维修或清洁时，顾客需支付租车公司相应费用，作为车辆停运损失赔偿（NOC）"),

    /*  冲绳樱花 */
    /**
     * 1-基本险-车辆碰撞险
     */
    CS_BASIC_COLLISION("CHSH YINGHUA", 9, "CDW", "INSC0001", "车辆碰撞险", 2800, 2100, 2800, -1, -1, "正常驾驶租赁车辆期间发生意外事故时，客人需要承担起赔额以内的费用，起赔额以上由保险公司承担（不含玻璃、底盘、轮胎、车顶损坏费用）"),

    /**
     * 2-基本险-第三者责任险
     */
    CS_BASIC_THIRD_PARTY("CHSH YINGHUA", 10, "TPL", "INSC0001", "第三者责任险", -1, -1, -1, 690, 900, "在正常驾驶租赁车辆期间因意外事故，致使第三方车辆或人员造成伤害，可获得保险公司给予起赔额以上保额以内的赔偿（* 实际赔付范围与标准以门店合同为准）"),

    /**
     * 3-基本险-车辆盗窃险
     */
    CS_BASIC_THEFT("CHSH YINGHUA", 11, "TP", "INSC0001", "盗窃险", 350, 350, 350, -1, -1, "正常驾驶租赁车辆期间发生意外事故或盗窃时，您需要承担起赔额以内的费用，起赔额以上由保险公司承担（不含玻璃、底盘、轮胎、车顶损坏费用）。"),

    /**
     * 4-基本险-营业损失赔偿险
     */
    CS_BASIC_BUSINESS_LOSS("CHSH YINGHUA", 12, "NOC", "INSC0001", "营业损失赔偿险", 350, 350, 350, -1, -1, "如车辆因交通事故、失窃、故障、刮擦或其他原因需要维修或清洁时，顾客需支付租车公司相应费用，作为车辆停运损失赔偿（NOC）"),

    /**
     * 5-全额险-车辆碰撞险
     */
    CS_FULL_COLLISION("CHSH YINGHUA", 13, "CDW", "INSC0002", "车辆碰撞险", 0, 0, 0, -1, -1, "如正常驾驶租赁车辆期间发生事故，保险公司将承担全额费用（不含玻璃、底盘、轮胎、车顶损坏）"),

    /**
     * 6-全额险-第三者责任险
     */
    CS_FULL_THIRD_PARTY("CHSH YINGHUA", 14, "TPL", "INSC0002", "第三者责任险", -1, -1, -1, 690, 900, "在正常驾驶租赁车辆期间因意外事故，致使第三方车辆或人员造成伤害，可获得保险公司给予起赔额以上保额以内的赔偿（* 实际赔付范围与标准以门店合同为准）"),

    /**
     * 7-全额险-车辆盗窃险
     */
    CS_FULL_THEFT("CHSH YINGHUA", 15, "STP", "INSC0002", "超级盗窃险", 0, 0, 0, -1, -1, "正常驾驶租赁车辆期间发生盗窃时，所产生的费用将由保险公司全额负责"),

    /**
     * 8-全额险-营业损失赔偿险
     */
    CS_FULL_BUSINESS_LOSS("CHSH YINGHUA", 16, "NOC", "INSC0002", "营业损失赔偿险", 210, 210, 350, -1, -1, "如车辆因交通事故、失窃、故障、刮擦或其他原因需要维修或清洁时，顾客需支付租车公司相应费用，作为车辆停运损失赔偿（NOC）"),

    /**
     * 9-综合险-超级车辆碰撞险[SCDW]
     */
    CS_COMPOSITE_COLLISION("CHSH YINGHUA", 17, "SCDW", "INSC0003", "超级车辆碰撞险", 0, 0, 0, -1, -1, "如正常驾驶租赁车辆期间发生事故，保险公司将承担全额费用（不含玻璃、底盘、轮胎、车顶损坏）"),

    /**
     * 10-综合险-第三者责任险
     */
    CS_COMPOSITE_THIRD_PARTY("CHSH YINGHUA", 18, "TPL", "INSC0003", "第三者责任险", -1, -1, -1, 690, 900, "在正常驾驶租赁车辆期间因意外事故，致使第三方车辆或人员造成伤害，可获得保险公司给予起赔额以上保额以内的赔偿（* 实际赔付范围与标准以门店合同为准）"),

    /**
     * 10-综合险-超级窃盗险[STP]
     */
    CS_COMPOSITE_THEFT("CHSH YINGHUA", 19, "STP", "INSC0003", "超级窃盗险", 0, 0, 0, -1, -1, "正常驾驶租赁车辆期间发生盗窃时，所产生的费用将由保险公司全额负责"),

    /**
     * 11-综合险-营业损失赔偿险
     */
    CS_COMPOSITE_BUSINESS_LOSS("CHSH YINGHUA", 20, "NOC", "INSC0003", "营业损失赔偿险", 0, 0, 0, -1, -1, "如车辆因交通事故、失窃、故障、刮擦或其他原因需要维修或清洁时，顾客需支付租车公司相应费用，作为车辆停运损失赔偿（NOC）"),

    /* 台湾春天 */
    /**
     * 1-基本险-车辆碰撞险
     */
    TS_BASIC_COLLISION("TW HOLIDAY", 21, "CDW", "INSC0001", "车辆碰撞险", 1030, 1030, 3070, -1, -1, "正常驾驶租赁车辆期间发生意外事故时，您需要承担起赔额以内的费用，起赔额以上由保险公司承担（不含玻璃、底盘、轮胎、车顶损坏费用）。"),

    /**
     * 2-基本险-第三者责任险
     */
    TS_BASIC_THIRD_PARTY("TW HOLIDAY", 22, "TPL", "INSC0001", "第三者责任险", 1030, 1030, 3070, -1, -1, "在正常驾驶租赁车辆期间因意外事故，致使第三方车辆或人员造成伤害，可获得保险公司给予起赔额以上保额以内的赔偿（* 实际赔付范围与标准以门店合同为准）"),

    /**
     * 3-基本险-盗窃险
     */
    TS_BASIC_THEFT("TW HOLIDAY", 23, "TP", "INSC0001", "盗窃险", 1030, 1030, 3070, -1, -1, "正常驾驶租赁车辆期间发生意外事故或盗窃时，您需要承担起赔额以内的费用，起赔额以上由保险公司承担（不含玻璃、底盘、轮胎、车顶损坏费用）。"),

    /**
     * 4-基本险-营业损失赔偿险
     */
    TS_BASIC_BUSINESS_LOSS("TW HOLIDAY", 24, "NOC", "INSC0001", "营业损失赔偿险", 1030, 1030, 3070, -1, -1, "如车辆因交通事故、失窃、故障、刮擦或其他原因需要维修或清洁时，顾客需支付租车公司相应费用，作为车辆停运损失赔偿（NOC）"),

    ;


    private final String vendorCode;
    private final int code;
    private final String type;
    private final String insuranceCode;
    private final String name;
    /**
     * 保险起赔额
     */
    private final int franchiseAmount;
    /**
     * 保险起赔额-最小值
     */
    private final int minFranchiseAmount;
    /**
     * 保险起赔额-最大值
     */
    private final int maxFranchiseAmount;
    /**
     * 保额最小值
     */
    private final int minCoverageAmount;
    /**
     * 保额最大值
     */
    private final int maxCoverageAmount;
    private final String desc;


    InsuranceChildTypeEnum(String vendorCode, int code, String type, String insuranceCode, String name, int franchiseAmount, int minFranchiseAmount, int maxFranchiseAmount, int minCoverageAmount, int maxCoverageAmount, String desc) {
        this.vendorCode = vendorCode;
        this.code = code;
        this.type = type;
        this.insuranceCode = insuranceCode;
        this.name = name;
        this.franchiseAmount = franchiseAmount;
        this.minFranchiseAmount = minFranchiseAmount;
        this.maxFranchiseAmount = maxFranchiseAmount;
        this.minCoverageAmount = minCoverageAmount;
        this.maxCoverageAmount = maxCoverageAmount;
        this.desc = desc;
    }

    /**
     * 根据 vendorCode 和 insuranceCode 分组
     *
     * @return Map, 键为 Pair(vendorCode, insuranceCode), 值为对应的枚举列表
     */
    public static Map<Pair<String, String>, List<InsuranceChildTypeEnum>> getGroupedByVendorAndInsuranceCode() {
        return Arrays.stream(InsuranceChildTypeEnum.values())
                .collect(Collectors.groupingBy(
                        item -> Pair.of(item.getVendorCode(), item.getInsuranceCode())
                ));
    }

    public static List<InsuranceChildTypeEnum> getEnumsByInsuranceCode(String insuranceCode) {
        return Arrays.stream(InsuranceChildTypeEnum.values())
                .filter(p -> Objects.equals(insuranceCode, p.getInsuranceCode()))
                .collect(Collectors.toList());
    }

    public static List<InsuranceChildTypeEnum> getEnumsByInsuranceCodeAndVendorId(String vendorCode, String insuranceCode) {
        return Arrays.stream(InsuranceChildTypeEnum.values())
                .filter(p -> Objects.equals(insuranceCode, p.getInsuranceCode())
                        && (StringUtils.isNotBlank(vendorCode) && Objects.equals(vendorCode, p.getVendorCode())))
                .collect(Collectors.toList());
    }

}
