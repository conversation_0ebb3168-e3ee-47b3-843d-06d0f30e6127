package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 费率支付金额 resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RatePayAmountResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 佣金金额
  */
 private AmountResp commissionAmount;

 /**
  * 费用金额
  */
 private AmountResp feeAmount;

 /**
  * 费率计算佣金金额
  */
 private AmountResp rateCalCommissionAmount;

 /**
  * 税费金额
  */
 private AmountResp taxAmount;
}
