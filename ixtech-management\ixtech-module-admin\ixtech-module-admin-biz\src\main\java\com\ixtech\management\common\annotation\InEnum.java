package com.ixtech.management.common.annotation;

import com.ixtech.management.common.enums.BaseEnum;
import com.ixtech.management.common.validator.InEnumValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义参数校验，用于校验参数是否存在指定枚举中
 *
 * <AUTHOR> hu
 * @date 2025/4/4 18:02
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = InEnumValidator.class)
public @interface InEnum {

    /**
     * 错误提示语
     *
     * @return
     */
    String message() default "参数值不在允许范围内";

    /**
     * 目标枚举类
     */
    Class<? extends BaseEnum<?>> enumClass();

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
