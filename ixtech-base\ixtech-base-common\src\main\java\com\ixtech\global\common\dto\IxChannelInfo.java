package com.ixtech.global.common.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 渠道信息
 * @TableName ix_channel
 */
@Data
public class IxChannelInfo {
    /**
     *
     */
    private Long id;

    /**
     * 名称
     */
    private String title;

    /**
     * api的 stage
     */
    private String stage;

    /**
     * 公司全称
     */
    private String companyFullName;

    /**
     * 报价货币
     */
    private String quoteCurrency;

    /**
     * 结算货币
     */
    private String settlementCurrency;

    /**
     * 结算模式 1:底价模式 2:抽佣模式
     */
    private Integer settlementMode;

    /**
     * 比例x%
     */
    private BigDecimal rate;

    /**
     * 提供给渠道端端对接账号key
     */
    private String credentialKey;
}
