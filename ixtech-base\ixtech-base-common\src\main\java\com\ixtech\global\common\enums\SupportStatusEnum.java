package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SupportStatusEnum implements DictInf {

    /**
     * 支持
     */
    SUPPORTED("1", "支持"),

    /**
     * 不支持
     */
    NOT_SUPPORTED("0", "不支持");

    private final String value;
    private final String label;

    public static String getLabelByValue(String value) {
        for (SupportStatusEnum e : values()) {
            if (String.valueOf(e.value).equals(value)) {
                return e.label;
            }
        }
        return "";
    }
}