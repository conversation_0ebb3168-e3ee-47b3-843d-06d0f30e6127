package com.ixtech.management.domain.service;

import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.ChannelAddResp;
import com.ixtech.management.integration.internal.resp.ChannelInfoResp;
import com.ixtech.management.integration.internal.resp.ChannelListInfoResp;

import java.util.List;

/**
 * 渠道service
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:14
 */
public interface ChannelService {

    /**
     * 分页条件查询渠道列表
     *
     * @param channelListQueryReq
     * @return
     */
    PageResponse<ChannelListInfoResp> list(ChannelListQueryReq channelListQueryReq);

    /**
     * 查询指定渠道信息
     *
     * @param id 渠道id
     * @return
     */
    ChannelInfoResp info(Long id);

    /**
     * 添加渠道
     *
     * @param channelAddReq
     * @return
     */
    ChannelAddResp add(ChannelAddReq channelAddReq);

    /**
     * 更新渠道
     *
     * @param channelUpdateReq
     */
    void update(ChannelUpdateReq channelUpdateReq);

    /**
     * 上下线渠道
     *
     * @param channelSetActiveReq
     */
    void setActive(ChannelSetActiveReq channelSetActiveReq);

    /**
     * 渠道下拉列表
     *
     * @param channelDropdownReq
     * @return
     */
    List<SelectOptionResponse<Long>> dropdownList(ChannelDropdownReq channelDropdownReq);

}
