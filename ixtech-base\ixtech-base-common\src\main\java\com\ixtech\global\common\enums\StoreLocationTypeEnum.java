package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StoreLocationTypeEnum implements DictInf {

    AIRPORT_INTERNAL("1", "机场内"),
    AIRPORT_EXTERNAL("2", "机场外"),
    EXTERNAL_WITH_INTERNAL_PICKUP("3", "机场外，机场内取车");

    private final String value;
    private final String label;
}