package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.Area;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:38
 */
@DS("ix")
@Mapper
public interface AreaMapper {

    int insert(Area record);

    int insertSelective(Area record);

    Area selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Area record);

    int updateByPrimaryKey(Area record);

    int updateBatch(List<Area> list);

    int updateBatchSelective(List<Area> list);

    int batchInsert(@Param("list") List<Area> list);

    /**
     * 批量查询地区信息
     *
     * @param ids 地区id列表
     * @return
     */
    List<Area> selectByPrimaryKeyList(@Param(value = "ids") Collection<Long> ids);

    /**
     * 获取所有门店覆盖区域id
     *
     * @return
     */
    List<Area> queryAllStoreArea();

}