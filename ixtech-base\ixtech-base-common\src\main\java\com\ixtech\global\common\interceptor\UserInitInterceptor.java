package com.ixtech.global.common.interceptor;

import com.ixtech.global.common.context.OnlineContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 用户信息初始化拦截器
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Slf4j
public class UserInitInterceptor implements HandlerInterceptor {

    private static final String USER_SOURCE_HEADER = "X-User-Source";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        OnlineContext.init();
        String userSource = request.getHeader(USER_SOURCE_HEADER);
        log.info("UserInterceptor.preHandle userSource:{}", userSource);
        if (StringUtils.isNotBlank(userSource)) {
            OnlineContext.getSubject().setUserSource(userSource);
        }
        return true;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            OnlineContext.clear();
            log.info("UserInterceptor.clear:");
        } catch (Exception e) {
            log.error("UserInterceptor.afterCompletion异常:", e);
        } finally {
            OnlineContext.clear();
            log.info("UserInterceptor.clear:");
        }
    }


}
