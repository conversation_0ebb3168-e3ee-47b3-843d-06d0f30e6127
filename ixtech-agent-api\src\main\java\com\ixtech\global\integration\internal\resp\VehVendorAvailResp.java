package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 门店地点resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehVendorAvailResp implements Serializable {

 private static final long serialVersionUID = 1L;


 /**
  * 车辆可用核心信息列表
  */
 private List<VehAvailCoreResp> vehAvailCores;

 /**
  * 供应商信息
  */
 private VendorResp vendor;
}
