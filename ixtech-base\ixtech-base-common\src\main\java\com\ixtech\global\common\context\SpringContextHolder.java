package com.ixtech.global.common.context;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Spring 应用上下文持有器.
 * 设计目标：
 * 1. 在任何地方都能安全地获取 Spring 容器中的 Bean.
 * 2. 遵循 "Fail-Fast" 原则，在上下文未准备好时立即抛出异常.
 */
@Component
public class SpringContextHolder implements ApplicationContextAware {

    /**
     * 使用 volatile 保证多线程环境下的可见性.
     * Spring Boot 应用的启动过程是单线程的，但在某些复杂场景或测试中，volatile 能提供额外的安全保障。
     */
    private static volatile ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext context) throws BeansException {
        // @NonNull 注解由 Spring Framework 提供，用于静态检查，表明 context 不应为 null。
        // Spring 容器会保证在调用此方法时传入有效的 ApplicationContext。
        SpringContextHolder.applicationContext = context;
    }

    /**
     * 根据名称获取 Bean.
     *
     * @param name Bean 的名称
     * @return Object 一个以所给名字注册的 bean 的实例
     * @throws IllegalStateException 如果 ApplicationContext 未被初始化
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) {
        assertContextInitialized();
        return (T) applicationContext.getBean(name);
    }

    /**
     * 根据类型获取 Bean.
     *
     * @param clazz Bean 的 Class
     * @param <T>   Bean 的泛型
     * @return T 匹配给定对象类型的单个 bean 的实例
     * @throws IllegalStateException 如果 ApplicationContext 未被初始化
     */
    public static <T> T getBean(Class<T> clazz) {
        assertContextInitialized();
        return applicationContext.getBean(clazz);
    }

    /**
     * 根据名称和类型获取 Bean.
     *
     * @param name  Bean 的名称
     * @param clazz Bean 的 Class
     * @param <T>   Bean 的泛型
     * @return T 一个以所给名字注册的 bean 的实例
     * @throws IllegalStateException 如果 ApplicationContext 未被初始化
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        assertContextInitialized();
        return applicationContext.getBean(name, clazz);
    }

    /**
     * 获取配置文件中的值.
     *
     * @param key 属性键
     * @return 属性值，如果 key 不存在则返回 null
     * @throws IllegalStateException 如果 ApplicationContext 未被初始化
     */
    public static String getProperty(String key) {
        assertContextInitialized();
        return applicationContext.getEnvironment().getProperty(key);
    }

    /**
     * 获取配置文件中的值.
     *
     * @param key          属性键
     * @param defaultValue 默认值
     * @return 属性值
     * @throws IllegalStateException 如果 ApplicationContext 未被初始化
     */
    public static String getProperty(String key, String defaultValue) {
        assertContextInitialized();
        return applicationContext.getEnvironment().getProperty(key, defaultValue);
    }

    /**
     * 检查 ApplicationContext 是否已被初始化.
     */
    private static void assertContextInitialized() {
        if (applicationContext == null) {
            throw new IllegalStateException("ApplicationContext 未被初始化");
        }
    }
}
