package com.ixtech.global.integration.internal.client;

import com.ixtech.global.common.dto.ApiResponse;
import com.ixtech.global.integration.internal.req.ReferenceQueryReq;
import com.ixtech.global.integration.internal.req.VehicleAvailListQueryReq;
import com.ixtech.global.integration.internal.req.VehicleDetailQueryReq;
import com.ixtech.global.integration.internal.resp.ReferenceQueryResp;
import com.ixtech.global.integration.internal.resp.VehicleAvailQueryResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;

/**
 * Vehicle 服务 Feign 客户端接口
 */
@FeignClient(name = "productsrv", path = "/v1/productsrv/internal/vehicle")
public interface ProductsrvFeignClient {

    /**
     * 车辆搜索
     *
     * @param request 车辆可用性列表查询请求
     * @return 车辆可用性查询响应
     */
    @PostMapping("/veh_avail_rate")
    ApiResponse<VehicleAvailQueryResp> vehicleList(@RequestBody @Valid VehicleAvailListQueryReq request);

    /**
     * 车辆详情
     *
     * @param request 车辆详情查询请求
     * @return 车辆可用性查询响应
     */
    @PostMapping("/veh_rate_rule")
    ApiResponse<VehicleAvailQueryResp> vehicleDetail(@RequestBody @Valid VehicleDetailQueryReq request);

    /**
     * 车辆详情 referenceId查找
     *
     * @param request 参考ID查询请求
     * @return 参考ID查询响应
     */
    @PostMapping("/ref_query")
    ApiResponse<ReferenceQueryResp> refQuery(@RequestBody @Valid ReferenceQueryReq request);
}
