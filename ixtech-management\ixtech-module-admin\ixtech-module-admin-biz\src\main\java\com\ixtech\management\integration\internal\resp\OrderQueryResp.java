package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * v2 订单条件查询resp
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderQueryResp {

    /**
     * platform_order表id
     */
    private Long id;

    /**
     * 确认号
     *
     */
    private String sourceOrderCode;

    /**
     * 订单号- platform_order 的 order_code
     */
    private String orderCode;
    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
    private String orderTimeStr;
    /**
     * 确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmTime;
    private String confirmTimeStr;
    /**
     * 最晚确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime latestConfirmTime;
    private String latestConfirmTimeStr;

    /**
     * 确认时长; 单位小时
     */
//    private Integer confirm_duration;

    /**
     * 车辆信息 todo
     */
    private String appointCarInfo;
    /**
     * 实际车辆
     */
    private String actualAppointCarInfo;


    /**
     * 真实取车门店
     */
    private String actualStoreName;

    /**
     * 是否是同一取车门店
     */
    private Integer isSamePickupStore;


    private String pickUpStoreName;

    private String pickOffStoreName;

    /**
     * 取车门店
     */
    private Long pickupStoreId;
    /**
     * 取车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickupTime;
    private String pickupTimeStr;
    /**
     * 还车门店
     */
    private Long pickoffStoreId;

    /**
     * 还车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime returnTime;
    private String returnTimeStr;



    //租车用户
    /**
     * 姓名
     */
    private String userName;
    /**
     * 航班号
     */
    private String flightNumber;


    //订单金额
    /**
     * 订单金额
     */
    private BigDecimal totalAmount;
    /**
     * 预付金额
     */
    private BigDecimal prepaidAmount;
    /**
     * 到付金额
     */
    private BigDecimal collectAmount;
    /**
     * 金额单位
     */
    private String currency;


    /**
     * 订单来源
     */
    private String sourceChannel;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单备注
     */
    private String remark;

    private String vendorName;
    private String vendorId;


}
