server:
  port: 8080

spring:
  application:
    name: agentapi
  datasource:
    url: *************************************************************************************************************************************************************
    username: ixtech_mgt
    password: R1gfh!34LkA124(7Fg
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: HikariCP
      minimum-idle: 5
      maximum-pool-size: 10
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      auto-commit: true

  # feign相关配置
  cloud:
    discovery:
      client:
        simple:
          instances:
            ordersrv: # 服务名
              - uri: http://order-service
                metadata:
                  instanceId: instance-1
            productsrv: # 服务名
              - uri: http://product-service
                metadata:
                  instanceId: instance-1
            vendorbasicsrv: # 服务名
              - uri: http://vendor-basic-service
                metadata:
                  instanceId: instance-1

    loadbalancer:
      enabled: true # 启用 LoadBalancer

config:
  redis:
    host: **************
    port: 6379
    password: Cz#nyson/3Qp
    index: 0
    timeout: 3000
    maxWait: -1
    maxIdle: 60
    minIdle: 20
    MaxWaitMillis: 10000
    MaxTotal: 60
    TestOnBorrow: true
    UsePool: true


mybatis:
  mapper-locations: classpath*:mybatis/mapper/*.xml
  # 目的是为了省略resultType里的代码量
  #type-aliases-package: com.ixtech.global.repo.entity
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  config: classpath:logback-spring.xml # 指定 Logback 配置文件
  level:
    com.ixtech.global.repo.mapper: debug
    com.ixtech.global.**.client: DEBUG # 设置 Feign 客户端的日志级别为 DEBUG


#api:
#  auth:
#    credentials:
#      zzc: d04f4c4ad62b93f4d7c025acd7651114
#      qeeq: 9a2173c783f219028dab755a31e82c89

whitelist:
  enabled: false
  ips:
    - 127.0.0.1

    # 租租车
    - ***********
    - ***********
    - **********
    - ************
    - *************
    - ************
    - *************
    - *************
    - ************
    - ************
    - *************
    - *************
    - ************
    - ************
    - ************
  exclude-paths:
    - /public/**
    - /health

management:
  endpoints:
    web:
      exposure:
        include: prometheus,metrics
    prometheus:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}