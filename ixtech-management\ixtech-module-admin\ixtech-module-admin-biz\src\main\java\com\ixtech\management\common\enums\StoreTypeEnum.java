package com.ixtech.management.common.enums;

import lombok.Getter;

/**
 * 门店类型枚举
 *
 * <AUTHOR>
 * @date 2025/3/25
 */
@Getter
public enum StoreTypeEnum implements BaseEnum<Integer> {

    CITY(1, "城市"),
    AIRPORT(2, "机场"),
    HOTEL(3, "酒店"),
    ;

    private final Integer code;
    private final String name;

    StoreTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return asString();
    }

}
