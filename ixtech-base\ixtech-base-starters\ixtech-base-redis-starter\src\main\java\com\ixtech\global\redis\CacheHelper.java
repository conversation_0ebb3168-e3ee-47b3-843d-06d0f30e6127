package com.ixtech.global.redis;

import com.ixtech.global.common.utils.JsonUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Component
public class CacheHelper {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RedisService redisService;

    /**
     * 缓存代理
     *
     * @param supplier
     * @param keyPrefix
     * @param keys
     * @param <T>
     * @return
     */
    public <T> T cacheProxy(Supplier<T> supplier, String keyPrefix, String... keys) {
        return cacheProxy(supplier, 10, TimeUnit.SECONDS, keyPrefix, keys);
    }

    /**
     * 缓存代理
     *
     * @param keyPrefix key前缀
     * @param timeout
     * @param timeUnit
     * @param keys      key数组
     * @param supplier  数据库查询
     * @param <T>
     * @return
     */
    public <T> T cacheProxy(Supplier<T> supplier, int timeout, TimeUnit timeUnit, String keyPrefix, String... keys) {
        // 校验输入
        if (keyPrefix == null || keyPrefix.isEmpty()) {
            throw new IllegalArgumentException("keyPrefix cannot be null or empty");
        }
        // 拼接缓存键：keyPrefix::key1::key2::...
        String actualKey = keyPrefix + "::" + Arrays.stream(keys).filter(key -> key != null && !key.isEmpty()).collect(Collectors.joining("::"));
        log.debug("CacheHelper::cacheProxy::actualKey:{}", JsonUtils.stringify(actualKey));
        T res = redisService.getString(actualKey);
        if (!ObjectUtils.isEmpty(res)) {
            log.debug("CacheHelper::cacheProxy::hit res:{}", JsonUtils.stringify(res));
            return res;
        }

        res = supplier.get();
        log.debug("CacheHelper::cacheProxy::notHit res:{}", JsonUtils.stringify(res));
        redisService.setString(actualKey, res, timeout, timeUnit);
        return res;
    }
}
