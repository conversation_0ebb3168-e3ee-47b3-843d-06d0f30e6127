package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChargePriceTypeEnum implements DictInf {

    BY_DAY("1", "按天"),
    BY_TIME("2", "按次");

    /**
     * 类型值 (对应数据库中的值)
     */
    private final String value;

    /**
     * 类型名称 (中文描述)
     */
    private final String label;


    /**
     * 根据类型值获取对应的中文标签
     * 
     * @param code 类型值 (1 或 2)
     * @return 对应的中文标签，如果值无效则返回null
     */
    public static String getLabelByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ChargePriceTypeEnum type : ChargePriceTypeEnum.values()) {
            if (type.getValue().equals(code)) {
                return type.getLabel();
            }
        }
        return null;
    }
    
    /**
     * 根据类型值获取对应的枚举实例
     * 
     * @param code 类型值 (1 或 2)
     * @return 对应的枚举实例，如果值无效则返回null
     */
    public static ChargePriceTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ChargePriceTypeEnum type : ChargePriceTypeEnum.values()) {
            if (type.getValue().equals(code)) {
                return type;
            }
        }
        return null;
    }
}