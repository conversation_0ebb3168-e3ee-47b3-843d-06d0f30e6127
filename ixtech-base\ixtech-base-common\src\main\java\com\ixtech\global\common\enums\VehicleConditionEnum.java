package com.ixtech.global.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum VehicleConditionEnum {

    NORMAL(1, "车况正常"),
    CLEAN(2, "车辆保养"),
    MAINTENANCE(3, "车辆维修"),
    RENTED(4, "车辆在租"),
    SCRAP(5, "报废/售出"),
    ;

    /**
     * 保险类型值（对应数据库中的insurance_type）
     */
    private final Integer Code;

    /**
     * 保险类型名称（前端显示用）
     */
    private final String label;

    public static String getLabelByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VehicleConditionEnum insuranceTypeEnum : values()) {
            if (insuranceTypeEnum.getCode().equals(code)) {
                return insuranceTypeEnum.getLabel();
            }
        }
        return null;
    }
}