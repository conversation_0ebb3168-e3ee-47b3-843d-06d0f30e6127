package com.ixtech.management.domain.service;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.integration.internal.req.MerchantRentalOrderDetailReq;
import com.ixtech.management.integration.internal.req.OrderPageQueryReq;
import com.ixtech.management.integration.internal.req.OrderTransferReq;
import com.ixtech.management.integration.internal.req.common.SelectOption;
import com.ixtech.management.integration.internal.req.common.SelectOptionReq;
import com.ixtech.management.integration.internal.resp.MerchantRentalOrderResp;
import com.ixtech.management.integration.internal.resp.OrderQueryResp;

import java.util.List;


public interface RentalOrderService {


    ApiResponse<PageResult<OrderQueryResp>> listByPage(OrderPageQueryReq req);


    ApiResponse<MerchantRentalOrderResp> getOrderDetail(MerchantRentalOrderDetailReq req);

    ApiResponse<List<SelectOption>> storeList(SelectOptionReq req);

    ApiResponse<List<SelectOption>> vehicleGroupList(SelectOptionReq req);

    ApiResponse<List<SelectOption>> getChannelList(SelectOptionReq req);

    ApiResponse<Boolean> orderTransfer(OrderTransferReq req);
}
