package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum DocumentTypeEnum {

    ORDER(1, "订单"),
    RESERVE(2, "预占单"),
    MAINTAIN(3, "维修"),
    UPKEEP(4, "保养")
    ;

    /**
     * 状态值
     */
    private final Integer value;

    /**
     * 状态名称
     */
    private final String label;
}
