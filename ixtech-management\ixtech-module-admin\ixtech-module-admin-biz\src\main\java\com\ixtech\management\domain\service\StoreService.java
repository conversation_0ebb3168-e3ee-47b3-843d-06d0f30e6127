package com.ixtech.management.domain.service;

import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.integration.internal.req.StoreDropdownReq;
import com.ixtech.management.integration.internal.req.StoreListQueryReq;
import com.ixtech.management.integration.internal.resp.AreaSelectOptionResp;
import com.ixtech.management.integration.internal.resp.StoreInfoResp;
import com.ixtech.management.integration.internal.resp.StoreListInfoResp;

import java.util.List;

/**
 * 门店service
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:16
 */
public interface StoreService {

    /**
     * 查询门店列表
     *
     * @param storeListQueryReq
     * @return
     */
    PageResponse<StoreListInfoResp> list(StoreListQueryReq storeListQueryReq);

    /**
     * 查询门店信息
     *
     * @param id 门店id
     * @return
     */
    StoreInfoResp info(Long id);

    /**
     * 获取门店下拉列表
     *
     * @param storeDropdownReq
     * @return
     */
    List<SelectOptionResponse<Long>> dropdownList(StoreDropdownReq storeDropdownReq);

    /**
     * 获取门店地区下拉列表
     *
     * @return
     */
    List<AreaSelectOptionResp> areaDropdownList();

}
