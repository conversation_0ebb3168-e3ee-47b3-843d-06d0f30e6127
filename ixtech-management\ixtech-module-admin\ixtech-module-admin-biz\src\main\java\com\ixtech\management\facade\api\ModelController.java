package com.ixtech.management.facade.api;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.ModelService;
import com.ixtech.management.domain.service.VendorService;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.ModelSelectOptionResponse;
import com.ixtech.management.integration.internal.resp.VendorAddResp;
import com.ixtech.management.integration.internal.resp.VendorListInfoResp;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商管理
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:15
 */
@RestController
@RequestMapping("/v1/management/internal/model")
public class ModelController {

    @Resource
    private ModelService modelService;

    /**
     * 车型下拉列表
     *
     * @return
     */
    @PermitAll
    @PostMapping(value = "/dropdown_list")
    public ApiResponse<List<ModelSelectOptionResponse>> dropdownList(@RequestBody CarModelListReq req) {
        List<ModelSelectOptionResponse> result = modelService.dropdownList(req.getStoreid());
        return ApiResponse.success(result);
    }

}
