package com.ixtech.global.config;

import com.ixtech.global.common.utils.JsonUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

@Component
@Order(1)
@Slf4j
public class LoggingFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        long startTime = System.currentTimeMillis();

        // 包装请求和响应
        CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest((HttpServletRequest) request);
        CachedBodyHttpServletResponse wrappedResponse = new CachedBodyHttpServletResponse((HttpServletResponse) response);

        // 记录请求日志
        logRequest(wrappedRequest);

        // 执行后续处理
        chain.doFilter(wrappedRequest, wrappedResponse);

        // 记录响应日志
        logResponse(wrappedRequest, wrappedResponse, startTime);

        // 将响应内容写回原始响应
        wrappedResponse.copyBodyToResponse();
    }

    private void logRequest(CachedBodyHttpServletRequest request) throws IOException {
        RequestLog requestLog = new RequestLog();
        requestLog.setMethod(request.getMethod());
        requestLog.setUri(request.getRequestURI());
        requestLog.setQueryString(request.getQueryString());
        requestLog.setRemoteAddr(request.getRemoteAddr());
        String contentType = request.getHeader("Content-Type");
        String body = request.getBody();
        if (contentType != null && contentType.contains("application/json")) {
            requestLog.setBody(JsonUtils.parseObject(body, Object.class));
        } else {
            requestLog.setBody(body); // 非 JSON 保留原始字符串
        }

        Map<String, String> headers = new LinkedHashMap<>();
        Collections.list(request.getHeaderNames())
                .forEach(name -> headers.put(name, request.getHeader(name)));
        requestLog.setHeaders(headers);

        log.info("Request: {}", JsonUtils.stringify(requestLog));
    }

    private void logResponse(CachedBodyHttpServletRequest request, CachedBodyHttpServletResponse response, long startTime)
            throws IOException {
        long duration = System.currentTimeMillis() - startTime;

        ResponseLog responseLog = new ResponseLog();
        responseLog.setMethod(request.getMethod());
        responseLog.setUri(request.getRequestURI());
        responseLog.setStatus(response.getStatus());
        responseLog.setDurationMs(duration);
        String contentType = request.getHeader("Content-Type");
        String body = response.getBody();
        if (contentType != null && contentType.contains("application/json")) {
            responseLog.setBody(JsonUtils.parseObject(body, Object.class));
        } else {
            responseLog.setBody(body); // 非 JSON 保留原始字符串
        }

        log.info("Response: {}", JsonUtils.stringify(responseLog));
    }

    // 请求日志对象
    @Data
    static class RequestLog {
        private String method;
        private String uri;
        private String queryString;
        private String remoteAddr;
        private Object body;
        private Map<String, String> headers;
    }

    // 响应日志对象
    @Data
    static class ResponseLog {
        private String method;
        private String uri;
        private int status;
        private long durationMs;
        private Object body;
    }
}

// 包装请求以读取请求体
class CachedBodyHttpServletRequest extends jakarta.servlet.http.HttpServletRequestWrapper {
    private final byte[] cachedBody;

    public CachedBodyHttpServletRequest(HttpServletRequest request) throws IOException {
        super(request);
        this.cachedBody = request.getInputStream().readAllBytes();
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new CachedBodyServletInputStream(cachedBody);
    }

    @Override
    public java.io.BufferedReader getReader() throws IOException {
        return new java.io.BufferedReader(new java.io.InputStreamReader(getInputStream(), StandardCharsets.UTF_8));
    }

    public String getBody() {
        return new String(cachedBody, StandardCharsets.UTF_8);
    }
}

class CachedBodyServletInputStream extends ServletInputStream {
    private final java.io.ByteArrayInputStream cachedBodyInputStream;

    public CachedBodyServletInputStream(byte[] cachedBody) {
        this.cachedBodyInputStream = new java.io.ByteArrayInputStream(cachedBody);
    }

    @Override
    public boolean isFinished() {
        return cachedBodyInputStream.available() == 0;
    }

    @Override
    public boolean isReady() {
        return true;
    }

    @Override
    public void setReadListener(ReadListener readListener) {
        throw new UnsupportedOperationException();
    }

    @Override
    public int read() throws IOException {
        return cachedBodyInputStream.read();
    }
}

// 包装响应以捕获响应体
class CachedBodyHttpServletResponse extends jakarta.servlet.http.HttpServletResponseWrapper {
    private final ByteArrayOutputStream cachedBody = new ByteArrayOutputStream();
    private final PrintWriter writer = new PrintWriter(cachedBody);

    public CachedBodyHttpServletResponse(HttpServletResponse response) {
        super(response);
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        return new CachedBodyServletOutputStream(cachedBody);
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        return writer;
    }

    public String getBody() {
        writer.flush();
        return new String(cachedBody.toByteArray(), StandardCharsets.UTF_8);
    }

    public void copyBodyToResponse() throws IOException {
        writer.flush();
        getResponse().getOutputStream().write(cachedBody.toByteArray());
    }
}

class CachedBodyServletOutputStream extends ServletOutputStream {
    private final ByteArrayOutputStream cachedBody;

    public CachedBodyServletOutputStream(ByteArrayOutputStream cachedBody) {
        this.cachedBody = cachedBody;
    }

    @Override
    public boolean isReady() {
        return true;
    }

    @Override
    public void setWriteListener(WriteListener writeListener) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void write(int b) throws IOException {
        cachedBody.write(b);
    }

    @Override
    public void write(byte[] b, int off, int len) throws IOException {
        cachedBody.write(b, off, len);
    }
}
