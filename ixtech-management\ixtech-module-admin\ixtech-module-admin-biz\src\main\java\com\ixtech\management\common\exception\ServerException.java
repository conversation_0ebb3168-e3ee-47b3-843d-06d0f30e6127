package com.ixtech.management.common.exception;

import com.ixtech.management.common.exception.inf.ErrorCode;
import lombok.Getter;

/**
 * @description: 业务异常
 * @author: JP
 * @date: 2025/3/18
 */
@Getter
public class ServerException extends RuntimeException {
    /**
     * -- GETTER --
     * 获取错误码
     *
     * @return 错误码实例
     */
    private final ErrorCode errorCode;

    // 私有构造函数，强制使用静态工厂方法
    private ServerException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 使用错误码创建异常
     *
     * @param errorCode 错误码实例
     * @return ServerException 实例
     */
    public static ServerException of(ErrorCode errorCode) {
        return new ServerException(errorCode, errorCode.getMessage(), null);
    }

    /**
     * 使用错误码和自定义消息创建异常
     *
     * @param errorCode 错误码实例
     * @param message   自定义错误消息
     * @return ServerException 实例
     */
    public static ServerException of(ErrorCode errorCode, String message) {
        return new ServerException(errorCode, message, null);
    }

    /**
     * 使用错误码和原因创建异常
     *
     * @param errorCode 错误码实例
     * @param cause     异常原因
     * @return ServerException 实例
     */
    public static ServerException of(ErrorCode errorCode, Throwable cause) {
        return new ServerException(errorCode, errorCode.getMessage(), cause);
    }

    /**
     * 使用错误码、自定义消息和原因创建异常
     *
     * @param errorCode 错误码实例
     * @param message   自定义错误消息
     * @param cause     异常原因
     * @return ServerException 实例
     */
    public static ServerException of(ErrorCode errorCode, String message, Throwable cause) {
        return new ServerException(errorCode, message, cause);
    }

    /**
     * 获取完整错误码
     *
     * @return 例如 "0001S0000"
     */
    public String getFullCode() {
        return errorCode.getFullCode();
    }

    /**
     * 获取服务码
     *
     * @return 4位服务码
     */
    public String getServiceCode() {
        return errorCode.getServiceCode();
    }

    /**
     * 获取错误类型
     *
     * @return S, P 或 B
     */
    public String getErrorType() {
        return errorCode.getErrorType();
    }

    /**
     * 获取具体错误码
     *
     * @return 4位具体错误码
     */
    public String getSpecificCode() {
        return errorCode.getSpecificCode();
    }
}
