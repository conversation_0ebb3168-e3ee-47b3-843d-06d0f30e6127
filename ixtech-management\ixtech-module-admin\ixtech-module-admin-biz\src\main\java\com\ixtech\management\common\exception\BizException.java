package com.ixtech.management.common.exception;

import java.text.MessageFormat;
import lombok.Generated;

public class BizException extends RuntimeException {
    private static final int DefaultCode = 1;
    private final int code;

    public BizException(int code, String message) {
        super(message);
        this.code = code;
    }

    public BizException(int code, String message, Object... args) {
        this(code, MessageFormat.format(message, args));
    }

    public BizException(int code) {
        this.code = code;
    }

    public BizException(String message) {
        this(1, message);
    }

    public BizException(String message, Object... args) {
        this(1, message, args);
    }

    @Generated
    public int getCode() {
        return this.code;
    }
}