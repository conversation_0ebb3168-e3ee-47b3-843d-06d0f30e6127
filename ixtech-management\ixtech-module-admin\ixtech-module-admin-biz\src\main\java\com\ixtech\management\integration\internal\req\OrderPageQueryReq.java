package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.ixtech.management.common.dto.PageRequest;
import lombok.Data;

import java.util.List;

/**
 * v2 订单条件查询req
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class OrderPageQueryReq extends PageRequest {

    private Long vendorId;
    /**
     * 订单号/确认号
     */
    private String orderCode;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String orderStartTime;
    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String orderEndTime;

    /**
     * 取车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String pickupStartTime;
    /**
     * 取车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String pickupEndTime;

    /**
     * 取车门店
     */
    private List<Long> pickupStoreIds;

    /**
     * 还车门店
     */
    private List<Long> pickoffStoreIds;

    /**
     * 还车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String pickoffStartTime;
    /**
     * 还车日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String pickoffEndTime;

    /**
     * 车型组
     */
    private List<Integer> vehicleGroupId;

    /**
     * 订单来源
     */
    private List<String> sourceChannelList;

    /**
     * 订单状态;
     */
    private List<Integer> statusList;

    /**
     * tab类型；-1:全部 0:近24小时新增 1:今日待取订单 2:今日待还订单 3:逾期待取订单 4:逾期待还订单
     */
    private Integer tabType;


}
