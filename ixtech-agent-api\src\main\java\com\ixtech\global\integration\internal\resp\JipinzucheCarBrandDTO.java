package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 车辆品牌
 *
 * <AUTHOR>
 * @since 2025-03-25 16:05:38
 */
@Data
public class JipinzucheCarBrandDTO {

    @JsonProperty("id")
    private Long id; // 修改：从 Integer 改为 Long

    /**
     * 名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 所属国家
     */
    @JsonProperty("country")
    private String country;

    /**
     * 品牌logo
     */
    @JsonProperty("logo")
    private String logo;
}
