<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.JipinzucheCarOrderPaytypeMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.JipinzucheCarOrderPaytype">
            <id property="id" column="id" />
            <result property="active" column="active" />
            <result property="deleted" column="deleted" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="title" column="title" />
            <result property="stage" column="stage" />
            <result property="isDelete" column="is_delete" />
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,title,
        stage,is_delete
    </sql>

    <sql id="Base_delete">
        and active = 1 and deleted = 0
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
            from jipinzuche_car_order_paytype
        where id = #{id}
        <include refid="Base_delete" />
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_order_paytype
        where active = 1 and deleted = 0
    </select>
</mapper>
