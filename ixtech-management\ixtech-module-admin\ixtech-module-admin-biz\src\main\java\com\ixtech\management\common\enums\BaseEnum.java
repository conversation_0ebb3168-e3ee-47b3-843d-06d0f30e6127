package com.ixtech.management.common.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通用枚举基接口
 *
 * @param <C> 枚举的code类型
 * <AUTHOR> hu
 * @date 2025/4/4 11:01
 */
public interface BaseEnum<C> {

    /**
     * 获取枚举的编码
     */
    C getCode();

    /**
     * 获取枚举的名称
     */
    String getName();

    /**
     * 根据code获取枚举实例
     *
     * @param enumClass 枚举class类
     * @param code      枚举code属性
     * @return
     */
    static <E extends Enum<E> & BaseEnum<C>, C> E fromCode(Class<E> enumClass, C code) {
        return Arrays.stream(enumClass.getEnumConstants())
                .filter(e -> e.getCode()
                        .equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据name获取枚举实例
     *
     * @param enumClass 枚举class类
     * @param name      枚举名称属性
     * @return
     */
    static <E extends Enum<E> & BaseEnum<C>, C> E fromName(Class<E> enumClass, String name) {
        return Arrays.stream(enumClass.getEnumConstants())
                .filter(e -> e.getName()
                        .equals(name))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有枚举实例
     *
     * @param enumClass 枚举class类
     * @return
     */
    static <E extends Enum<E> & BaseEnum<C>, C> List<E> getAll(Class<E> enumClass) {
        return Arrays.asList(enumClass.getEnumConstants());
    }

    /**
     * 获取所有枚举的code
     *
     * @param enumClass 枚举class类
     * @return
     */
    static <E extends Enum<E> & BaseEnum<C>, C> List<C> getAllCodes(Class<E> enumClass) {
        return Arrays.stream(enumClass.getEnumConstants())
                .map(BaseEnum::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 默认toString实现
     */
    default String asString() {
        return getCode() + " - " + getName();
    }

}
