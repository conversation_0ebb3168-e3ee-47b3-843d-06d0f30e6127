package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车辆归属店铺枚举
 */
@Getter
@AllArgsConstructor
public enum VehicleAttributionEnum implements DictInf {

    PICKUP_STORE(1, "取车门店"),
    PICKOFF_STORE(2, "还车门店");

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }
}
