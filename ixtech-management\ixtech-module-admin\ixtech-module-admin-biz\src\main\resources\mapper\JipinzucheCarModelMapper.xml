<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.JipinzucheCarModelMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.JipinzucheCarModel">
            <id property="id" column="id" />
            <result property="active" column="active" />
            <result property="deleted" column="deleted" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="brandid" column="brandid" />
            <result property="litpic" column="litpic" />
            <result property="name" column="name" />
            <result property="carcode" column="carcode" />
            <result property="seat" column="seat" />
            <result property="door" column="door" />
            <result property="luggage" column="luggage" />
            <result property="transmission" column="transmission" />
            <result property="time" column="time" />
            <result property="ip" column="ip" />
            <result property="mid" column="mid" />
            <result property="status" column="status" />
            <result property="dayprice" column="dayprice" />
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,brandid,
        litpic,name,carcode,seat,door,
        luggage,transmission,time,ip,mid,
        status,dayprice
    </sql>
    <sql id="Base_delete">
        and active = 1 and deleted = 0
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
            from jipinzuche_car_model
        where id = #{id}
        <include refid="Base_delete" />
    </select>
    <select id="selectAll" resultType="com.ixtech.management.repo.entity.JipinzucheCarModel">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_model
        where active = 1 and deleted = 0
    </select>


</mapper>
