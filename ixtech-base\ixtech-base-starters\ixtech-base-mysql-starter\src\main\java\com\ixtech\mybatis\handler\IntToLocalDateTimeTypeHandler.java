package com.ixtech.mybatis.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * mybatis TypeHandler。实现 BaseTypeHandler<LocalDateTime>，处理 INT 到 LocalDateTime 的转换。
 */
@MappedTypes(LocalDateTime.class)
@MappedJdbcTypes(JdbcType.INTEGER)
public class IntToLocalDateTimeTypeHandler extends BaseTypeHandler<LocalDateTime> {

    // 默认时区，可以根据需要调整
    private static final ZoneId ZONE_ID = ZoneId.systemDefault();

    /**
     * 将 Java 的 LocalDateTime 转换为数据库的 INT（时间戳，秒）
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType)
            throws SQLException {
        // 将 LocalDateTime 转换为秒时间戳
        long timestamp = parameter.atZone(ZONE_ID).toEpochSecond();
        ps.setInt(i, (int) timestamp);
    }


    /**
     * 从 ResultSet 中获取 INT 并转换为 LocalDateTime（按列名）
     */
    @Override
    public LocalDateTime getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int timestamp = rs.getInt(columnName);
        return timestamp == 0 ? null : LocalDateTime.ofInstant(
                Instant.ofEpochSecond(timestamp), ZONE_ID);
    }

    /**
     * 从 ResultSet 中获取 INT 并转换为 LocalDateTime（按列索引）
     */
    @Override
    public LocalDateTime getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int timestamp = rs.getInt(columnIndex);
        return timestamp == 0 ? null : LocalDateTime.ofInstant(
                Instant.ofEpochSecond(timestamp), ZONE_ID);
    }

    /**
     * 从 CallableStatement 中获取 INT 并转换为 LocalDateTime
     */
    @Override
    public LocalDateTime getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int timestamp = cs.getInt(columnIndex);
        return timestamp == 0 ? null : LocalDateTime.ofInstant(
                Instant.ofEpochSecond(timestamp), ZONE_ID);
    }
}
