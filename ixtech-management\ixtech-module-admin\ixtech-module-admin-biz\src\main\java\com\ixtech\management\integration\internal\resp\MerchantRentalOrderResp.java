package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MerchantRentalOrderResp {

    /**
     * 平台订单编号
     */
    private String orderCode;

    /**
     * 订单状态  -1-已取消，0-待确认，1-已确认，2-已取车，3-已还车，4-已完成
     */
    private Integer orderStatus;

    /**
     * 来源渠道 1-租租车，2-携程Trip,3-线下订单
     */
    private String sourceChannel;

    /**
     * 结算订单金额
     */
    private BigDecimal settlementAmount;

    /**
     * 确认号
     */
    private String confirmNumber;

    /**
     * 最晚确认时间
     */
    private String latestConfirmTime;

    /**
     * 确认时间
     */
    private String confirmTime;

    /**
     * 下单渠道 订单金额
     */
    private BigDecimal totalAmount;

    /**
     * 预付金额
     */
    private BigDecimal originAmount;

    /**
     * 到付金额
     */
    private BigDecimal actualAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 下单时间
     */
    private String orderTime;

    /**
     * 计费租期
     */
    private String appointDays;

    /**
     * 取车门店
     */
    private String pickupLocationName;

    /**
     * 还车门店
     */
    private String dropoffLocationName;

    /**
     * 取车时间
     */
    private String pickupTime;

    /**
     * 还车时间
     */
    private String dropoffTime;

    /**
     * 预定车型 sippcode+车型
     */
    private String appointModel;

    /**
     * 取消时间
     */
    private String cancelTime;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 订单备注
     */
    private String markContent;

    /**
     * 实际车辆（车牌号）
     */
    private String actualVehicle;

    /**
     * 绑定车机
     */
    private String bindVehicle;

    /**
     * 商品、设备、保险
     */
    private List<GoodsAndEquipmentAndServiceResp> goodList;
    /**
     * 商品、设备及服务合计费用
     */
    private BigDecimal totalProductAmount;

    /**
     * 政策费用
     */
    private List<OrderChargeResp> orderChargeRespList;

    /**
     * 客户信息
     */
    private List<CustomerResp> customerRespList;


    /**
     * 取车信息
     */
    private PickupInformationResp pickupInformationResp;

    /**
     * 还车信息
     */
    private CheckoutInformationResp checkoutInformationResp;

//    /**
//     * 车机信息
//     */
//    private DeviceTelemetryResp deviceTelemetryResp;

    /**
     * 订单日志
     */
    private List<OrderLogResq> orderLogResqList;

    /**
     * 是否逾期 0-未预期 1-已预期
     */
    private Integer overDue;












}
