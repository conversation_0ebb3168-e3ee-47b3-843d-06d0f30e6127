{"groups": [{"name": "yudao.security", "type": "cn.iocoder.yudao.framework.security.config.SecurityProperties", "sourceType": "cn.iocoder.yudao.framework.security.config.SecurityProperties"}], "properties": [{"name": "yudao.security.mock-enable", "type": "java.lang.Bo<PERSON>an", "description": "mock 模式的开关", "sourceType": "cn.iocoder.yudao.framework.security.config.SecurityProperties"}, {"name": "yudao.security.mock-secret", "type": "java.lang.String", "description": "mock 模式的密钥 一定要配置密钥，保证安全性", "sourceType": "cn.iocoder.yudao.framework.security.config.SecurityProperties"}, {"name": "yudao.security.password-encoder-length", "type": "java.lang.Integer", "description": "PasswordEncoder 加密复杂度，越高开销越大", "sourceType": "cn.iocoder.yudao.framework.security.config.SecurityProperties"}, {"name": "yudao.security.permit-all-urls", "type": "java.util.List<java.lang.String>", "description": "免登录的 URL 列表", "sourceType": "cn.iocoder.yudao.framework.security.config.SecurityProperties"}, {"name": "yudao.security.token-header", "type": "java.lang.String", "description": "HTTP 请求时，访问令牌的请求 Header", "sourceType": "cn.iocoder.yudao.framework.security.config.SecurityProperties"}, {"name": "yudao.security.token-parameter", "type": "java.lang.String", "description": "HTTP 请求时，访问令牌的请求参数 初始目的：解决 WebSocket 无法通过 header 传参，只能通过 token 参数拼接", "sourceType": "cn.iocoder.yudao.framework.security.config.SecurityProperties"}], "hints": []}