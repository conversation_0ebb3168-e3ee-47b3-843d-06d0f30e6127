package com.ixtech.global.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.util.List;
import java.util.Map;

/**
 * jackson处理工具类
 */
@Slf4j
public class JsonUtils {
    private static final ObjectMapper objectMapper;
    private static final ObjectMapper objectMapperForMilliSec;


    static {
        objectMapper = Jackson2ObjectMapperBuilder.json().simpleDateFormat("yyyy-MM-dd HH:mm:ss")
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
                        DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).timeZone("GMT+0").build();
        // 序列化结果不包含为null的字段
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        objectMapperForMilliSec = Jackson2ObjectMapperBuilder.json().simpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
                        DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).timeZone("GMT+0").build();
        // 序列化结果不包含为null的字段
        objectMapperForMilliSec.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 将Java对象转换为Json String
     */
    public static String stringify(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            log.error("Java转Json异常", e);
            return null;
        }
    }

    public static String stringifyForMilliSec(Object object) {
        try {
            return objectMapperForMilliSec.writeValueAsString(object);
        } catch (Exception e) {
            log.error("Java转Json异常", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为java对象
     */
    public static <T> T map2Object(String jsonStr, Class<T> clazz) {
        try {
            if(StringUtils.isEmpty(jsonStr)){
                log.warn("输入字符串信息为空，返回null");
                return null;
            }
            return objectMapper.readValue(jsonStr, clazz);
        } catch (Exception e) {
            log.error("Json转Java异常, jsonString: " + jsonStr + ", exception: ", e);
            return null;
        }
    }

    public static <T> T map2ObjectForMilliSec(String jsonStr, Class<T> clazz) {
        try {
            if(StringUtils.isEmpty(jsonStr)){
                log.warn("输入字符串信息为空，返回null");
                return null;
            }
            return objectMapperForMilliSec.readValue(jsonStr, clazz);
        } catch (Exception e) {
            log.error("Json转Java异常", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为java对象
     */
    public static <T> T map2Object(Object object, Class<T> clazz) {
        try {
            return objectMapper.readValue(stringify(object), clazz);
        } catch (Exception e) {
            log.error("Json转Java异常", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为Map<String, Object></>对象
     */
    public static Map<String, Object> parseObject(String jsonStr) {
        try {
            if(StringUtils.isEmpty(jsonStr)){
                log.warn("输入字符串信息为空，返回null");
                return null;
            }
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(Map.class, String.class, Object.class);
            return objectMapper.readValue(jsonStr, javaType);
        } catch (Exception e) {
            log.error("Json转Java异常", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为java对象
     */
    public static <T> T parseObject(String jsonStr, Class<T> clazz) {
        return map2Object(jsonStr, clazz);
    }

    /**
     * 将JSON字符串转换为复杂类型的Java对象
     */
    public static <T> T parseObject(String jsonStr, TypeReference<T> typeReference) {
        try {
            if(StringUtils.isEmpty(jsonStr)){
                log.warn("输入字符串信息为空，返回null");
                return null;
            }
            return objectMapper.readValue(jsonStr, typeReference);
        } catch (Exception e) {
            log.error("Json转Java异常", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为list对象
     */
    public static <T> List<T> parseArray(String jsonStr, Class<T> clazz) {
        try {
            if(StringUtils.isEmpty(jsonStr)){
                log.warn("输入字符串信息为空，返回null");
                return null;
            }
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, clazz);
            return objectMapper.readValue(jsonStr, javaType);
        } catch (Exception e) {
            log.error("Json转Java异常", e);
            return null;
        }
    }

    public static <T> List<T> parseArrayForMilliSec(String jsonStr, Class<T> clazz) {
        try {
            if(StringUtils.isEmpty(jsonStr)){
                log.warn("输入字符串信息为空，返回null");
                return null;
            }
            JavaType javaType = objectMapperForMilliSec.getTypeFactory().constructParametricType(List.class, clazz);
            return objectMapperForMilliSec.readValue(jsonStr, javaType);
        } catch (Exception e) {
            log.error("Json转Java异常", e);
            return null;
        }
    }

    public static <T> T convert(Object object, Class<T> clazz) {
       return objectMapper.convertValue(object, clazz);
    }

    public static <T> T convert(Object object, TypeReference<T> typeReference) {
        return objectMapper.convertValue(object, typeReference);
    }
}
