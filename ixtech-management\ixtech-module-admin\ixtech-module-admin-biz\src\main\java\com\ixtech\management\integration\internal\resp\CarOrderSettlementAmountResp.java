package com.ixtech.management.integration.internal.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单结算金额信息Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/7 15:49
 */
@Data
public class CarOrderSettlementAmountResp {

    /**
     * 结算对象
     */
    private String settlementTarget;

    /**
     * 结算方式（字符串描述）
     */
    private String settlementModeStr;

    /**
     * 结算类型（字符串描述）
     */
    private String settlementTypeStr;

    /**
     * 结算金额
     */
    private BigDecimal amount;

    /**
     * 结算金额（字符串描述）
     */
    private String amountStr;

    /**
     * 结算金额货币
     */
    private String currency;

}
