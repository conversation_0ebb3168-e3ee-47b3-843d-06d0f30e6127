package com.ixtech.global.common.utils;

import com.ixtech.global.common.context.SpringContextHolder;
import com.ixtech.global.common.dto.inf.ScheduleInf;
import net.iakovlev.timeshape.TimeZoneEngine;
import org.apache.commons.lang3.Validate;
import org.springframework.util.ObjectUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * LocalDateTime 工具类，参考 Apache Commons Lang 风格实现时间区间判断
 */
public class LocalDateTimeUtils {

    public static final String SHORT_TIME_PATTERN = "HH:mm";
    public static final String TIME_PATTERN = "HH:mm:ss";
    public static final String MONTH_PATTERN = "yyyy-MM";
    public static final String SHORT_DATE_PATTERN = "yyMMdd";
    public static final String SHORT_DATETIME_PATTERN = "yyyy-MM-dd HH:mm";
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_PATTERN = "yyyy-MM-dd";

    public static final DateTimeFormatter SHORT_TIME_FORMATTER = DateTimeFormatter.ofPattern(SHORT_TIME_PATTERN);
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(TIME_PATTERN);
    public static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern(MONTH_PATTERN);
    public static final DateTimeFormatter SHORT_DATE_FORMATTER = DateTimeFormatter.ofPattern(SHORT_DATE_PATTERN);
    public static final DateTimeFormatter SHORT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern(SHORT_DATETIME_PATTERN);
    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(DATETIME_PATTERN);
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);
    public static final int ONE_DAY_IN_SECONDS = 86400;
    public static final int ONE_HOUR_IN_SECONDS = 3600;
    public static final int ONE_MINUTE_IN_SECONDS = 60;
    public static final TimeZoneEngine INITIALIZE = TimeZoneEngine.initialize();

    /**
     * 返回当前的日期
     */
    public static LocalDate getCurrentLocalDate() {
        return LocalDate.now();
    }

    /**
     * 返回当前时间
     */
    public static LocalTime getCurrentLocalTime() {
        return LocalTime.now();
    }

    /**
     * 返回当前日期时间
     */
    public static LocalDateTime getCurrentLocalDateTime() {
        return LocalDateTime.now();
    }

    /**
     * yyyyMMdd
     */
    public static String getCurrentDateStr() {
        return LocalDate.now().format(DATE_FORMATTER);
    }

    /**
     * yyMMdd
     */
    public static String getCurrentShortDateStr() {
        return LocalDate.now().format(SHORT_DATE_FORMATTER);
    }

    public static String getCurrentMonthStr() {
        return LocalDate.now().format(MONTH_FORMATTER);
    }

    /**
     * yyyyMMdd HH:mm:ss
     */
    public static String getCurrentDateTimeStr() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }

    /**
     * yyMMdd HH:mm:ss
     */
    public static String getCurrentShortDateTimeStr() {
        return LocalDateTime.now().format(SHORT_DATETIME_FORMATTER);
    }

    /**
     * HH:mm:ss
     */
    public static String getCurrentTimeStr() {
        return LocalTime.now().format(TIME_FORMATTER);
    }

    /**
     * HH:mm:ss
     */
    public static String getCurrentShortTimeStr() {
        return LocalTime.now().format(SHORT_TIME_FORMATTER);
    }

    public static String getCurrentDateStr(String pattern) {
        return LocalDate.now().format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String getCurrentDateTimeStr(String pattern) {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String getCurrentTimeStr(String pattern) {
        return LocalTime.now().format(DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalDate parseLocalDate(String dateStr, String pattern) {
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalDateTime parseLocalDateTime(String dateTimeStr, String pattern) {
        return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalTime parseLocalTime(String timeStr, String pattern) {
        return LocalTime.parse(timeStr, DateTimeFormatter.ofPattern(pattern));
    }

    public static String formatLocalDate(LocalDate date, String pattern) {
        return date.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String formatLocalDateTime(LocalDateTime datetime, String pattern) {
        Validate.notNull(pattern, "时间格式不能为空");
        return datetime.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String formatLocalTime(LocalTime time, String pattern) {
        return time.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalDate parseLocalDate(String dateStr) {
        return LocalDate.parse(dateStr, DATE_FORMATTER);
    }

    public static LocalDateTime parseLocalDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, DATETIME_FORMATTER);
    }

    public static LocalTime parseLocalTime(String timeStr) {
        return LocalTime.parse(timeStr, TIME_FORMATTER);
    }

    public static String formatLocalDate(LocalDate date) {
        return date.format(DATE_FORMATTER);
    }

    public static String formatLocalDateTime(LocalDateTime datetime) {
        return datetime.format(DATETIME_FORMATTER);
    }

    public static String formatLocalTime(LocalTime time) {
        return time.format(TIME_FORMATTER);
    }

    /**
     * 日期相隔天数
     */
    public static long periodDays(LocalDate startDateInclusive, LocalDate endDateExclusive) {
        return endDateExclusive.toEpochDay() - startDateInclusive.toEpochDay();
    }

    /**
     * 日期相隔天数 (不足一天按一天计算，负数或零也按一天计算)
     * 计算两个 LocalDateTime 之间的天数差。
     * - 如果时间差大于0，不足一天按一天计算 (向上取整)。
     * - 如果时间差小于或等于0，统一按 1 天计算。
     * 例如：
     * - 1小时 -> 1天
     * - 25小时 -> 2天
     * - -1小时 -> 1天
     * - 0小时 -> 1天
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 相隔的天数 (至少为 1)
     * @throws IllegalArgumentException 如果开始时间或结束时间为 null
     */
    public static long periodDays(LocalDateTime startTime, LocalDateTime endTime) {
        Validate.notNull(startTime, "开始时间不能为空");
        Validate.notNull(endTime, "结束时间不能为空");

        // 计算两个时间之间的秒数差
        Duration duration = Duration.between(startTime, endTime);
        long totalSeconds = duration.getSeconds();

        // 如果总秒数小于或等于0，则按1天计算
        if (totalSeconds <= 0) {
            return 1L;
        } else {
            // 如果总秒数大于0，则将秒数差转换为天数（浮点数）
            double days = (double) totalSeconds / ONE_DAY_IN_SECONDS;
            // 向上取整，不足一天按一天算
            return (long) Math.ceil(days);
        }
    }

    /**
     * 日期相隔小时
     */
    public static long durationHours(Temporal startInclusive, Temporal endExclusive) {
        return Duration.between(startInclusive, endExclusive).toHours();
    }

    /**
     * 日期相隔小时 (向上取整，允许负数)
     * 计算两个时间之间的差值（以小时为单位，向上取整）。
     * 向上取整的定义：对于正数或零，向上取整到最近的整数；对于负数，向上取整到最近的、更小的（负向）整数。
     * 例如：
     * - 1小时30分钟 -> 2小时
     * - 59分钟 -> 1小时
     * - -1小时30分钟 -> -2小时
     * - -30分钟 -> -1小时
     *
     * @param startInclusive 开始时间
     * @param endExclusive   结束时间
     * @return 相隔的小时数 (向上取整)，可能为负数
     */
    public static long durationHoursCeil(Temporal startInclusive, Temporal endExclusive) {
        Duration duration = Duration.between(startInclusive, endExclusive);
        double totalMinutes = duration.toMinutes();
        double hours = totalMinutes / ONE_MINUTE_IN_SECONDS;

        // 根据正负选择 Math.ceil 或 Math.floor
        if (totalMinutes >= 0) {
            return (long) Math.ceil(hours);
        } else {
            return (long) Math.floor(hours);
        }
    }

    /**
     * 日期相隔分钟
     */
    public static long durationMinutes(Temporal startInclusive, Temporal endExclusive) {
        return Duration.between(startInclusive, endExclusive).toMinutes();
    }

    /**
     * 日期相隔毫秒数
     */
    public static long durationMillis(Temporal startInclusive, Temporal endExclusive) {
        return Duration.between(startInclusive, endExclusive).toMillis();
    }

    /**
     * 是否当天
     */
    public static boolean isToday(LocalDate date) {
        return getCurrentLocalDate().equals(date);
    }

    /**
     * 获取此日期时间与配置时区组合的时间毫秒数
     */
    public static Long toEpochMilli(LocalDateTime dateTime) {
        return dateTime.atZone(getSystemZoneId()).toInstant().toEpochMilli();
    }

    /**
     * 前一个日期是否在后一个日期 之前
     */
    public static boolean isBefore(LocalDateTime paramA, LocalDateTime paramB) {
        return paramA.isBefore(paramB);
    }

    /**
     * 前一个日期是否在后一个日期 之后
     */
    public static boolean isAfter(LocalDateTime paramA, LocalDateTime paramB) {
        return paramA.isAfter(paramB);
    }

    /**
     * 判断目标时间是否在指定时间范围内（闭区间 [start, end]）
     *
     * @param target 目标时间
     * @param start  开始时间
     * @param end    结束时间
     * @return true 如果目标时间在 [开始时间, 结束时间] 范围内
     * @throws IllegalArgumentException 如果任何参数为 null
     */
    public static boolean isBetween(LocalDateTime target, LocalDateTime start, LocalDateTime end) {
        Validate.notNull(target, "目标时间不能为空");
        Validate.notNull(start, "开始时间不能为空");
        Validate.notNull(end, "结束时间不能为空");
        Validate.isTrue(!start.isAfter(end), "开始时间不能晚于结束时间");

        return !target.isBefore(start) && !target.isAfter(end);
    }

    /**
     * 判断目标时间是否在指定时间范围内（闭区间 [start, end]）
     *
     * @param target 目标时间
     * @param start  开始时间
     * @param end    结束时间
     * @return true 如果目标时间在 [开始时间, 结束时间] 范围内
     */
    public static boolean isBetweenV2(LocalDateTime target, LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null || target == null) {
            return false;
        }

        return !target.isBefore(start) && !target.isAfter(end);
    }

    /**
     * 判断目标时间是否在指定时间范围内（闭区间 [start, end]）
     *
     * @param target 目标时间
     * @param start  开始时间
     * @param end    结束时间
     * @return true 如果目标时间在 [开始时间, 结束时间] 范围内。如果任何参数为 null，则返回 false。
     */
    public static boolean isBetweenV2(LocalDate target, LocalDate start, LocalDate end) {
        if (start == null || end == null || target == null) {
            return false;
        }
        return !target.isBefore(start) && !target.isAfter(end);
    }

    /**
     * 判断目标时间是否在指定时间范围内（开区间 (start, end)）
     *
     * @param target 目标时间
     * @param start  开始时间
     * @param end    结束时间
     * @return true 如果目标时间在 (开始时间, 结束时间) 范围内
     * @throws IllegalArgumentException 如果任何参数为 null
     */
    public static boolean isBetweenExclusive(LocalDateTime target, LocalDateTime start, LocalDateTime end) {
        Validate.notNull(target, "目标时间不能为空");
        Validate.notNull(start, "开始时间不能为空");
        Validate.notNull(end, "结束时间不能为空");
        Validate.isTrue(!start.isAfter(end), "开始时间不能晚于结束时间");

        return target.isAfter(start) && target.isBefore(end);
    }

    /**
     * 判断目标时间是否在指定时间范围内（自定义边界）
     *
     * @param target         目标时间
     * @param start          开始时间
     * @param end            结束时间
     * @param startInclusive 是否包含开始时间
     * @param endInclusive   是否包含结束时间
     * @return true 如果目标时间在指定范围内
     * @throws IllegalArgumentException 如果任何参数为 null
     */
    public static boolean isBetweenCustom(LocalDateTime target, LocalDateTime start, LocalDateTime end,
                                          boolean startInclusive, boolean endInclusive) {
        Validate.notNull(target, "目标时间不能为空");
        Validate.notNull(start, "开始时间不能为空");
        Validate.notNull(end, "结束时间不能为空");
        Validate.isTrue(!start.isAfter(end), "开始时间不能晚于结束时间");

        boolean afterStart = startInclusive ? !target.isBefore(start) : target.isAfter(start);
        boolean beforeEnd = endInclusive ? !target.isAfter(end) : target.isBefore(end);
        return afterStart && beforeEnd;
    }

    /**
     * 计算两个时间之间的差值（以指定单位返回）
     *
     * @param start 开始时间
     * @param end   结束时间
     * @param unit  时间单位（如 ChronoUnit.MINUTES）
     * @return 时间差值
     * @throws IllegalArgumentException 如果开始时间或结束时间为 null
     */
    public static long durationBetween(LocalDateTime start, LocalDateTime end, ChronoUnit unit) {
        Validate.notNull(start, "开始时间不能为空");
        Validate.notNull(end, "结束时间不能为空");
        return unit.between(start, end);
    }

    /**
     * 计算两个时间相隔的小时数 向下去整
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 相隔的小时数（正数表示 end 在 start 之后，负数表示 end 在 start 之前）
     * @throws IllegalArgumentException 如果开始时间或结束时间为 null
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        Validate.notNull(start, "开始时间不能为空");
        Validate.notNull(end, "结束时间不能为空");
        return ChronoUnit.HOURS.between(start, end);
    }

    /**
     * 计算两个时间相隔的小时数 向上取整
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 相隔的小时数（正数表示 end 在 start 之后，负数表示 end 在 start 之前）
     * @throws IllegalArgumentException 如果开始时间或结束时间为 null
     */
    public static long hoursBetween(LocalTime start, LocalTime end) {
        Validate.notNull(start, "开始时间不能为空");
        Validate.notNull(end, "结束时间不能为空");
        return ChronoUnit.HOURS.between(start, end);
    }

    /**
     * 将指定时间往后推迟若干小时
     *
     * @param dateTime 原始时间
     * @param hours    要推迟的小时数（可以为正数或负数，正数表示往后推，负数表示往前推）
     * @return 推迟后的新时间
     * @throws IllegalArgumentException 如果原始时间为 null
     */
    public static LocalDateTime plusHours(LocalDateTime dateTime, long hours) {
        Validate.notNull(dateTime, "原始时间不能为空");
        return dateTime.plusHours(hours);
    }

    /**
     * 将指定时间往后推迟若干天，并返回日期部分
     *
     * @param dateTime 原始时间
     * @param days     要推迟的天数（可以为正数或负数）
     * @return 推迟后的新时间的日期部分
     * @throws IllegalArgumentException 如果原始时间为 null
     */
    public static LocalDate plusDays(LocalDateTime dateTime, long days) {
        Validate.notNull(dateTime, "原始时间不能为空");
        return dateTime.plusDays(days).toLocalDate();
    }

    /**
     * 取当天凌晨时间戳
     */
    public static Long parseTimeToMidnight(LocalDateTime dateTime) {
        Validate.notNull(dateTime, "时间参数不能为空");
        LocalDateTime midnight = dateTime.with(LocalTime.MIN);
        return midnight.atZone(getSystemZoneId()).toInstant().toEpochMilli() / 1000;
    }

    /**
     * 取当天结束时间戳
     */
    public static Long parseTimeToEndPoint(LocalDateTime dateTime) {
        Validate.notNull(dateTime, "时间参数不能为空");
        LocalDateTime midnight = dateTime.with(LocalTime.MAX);
        return midnight.atZone(getSystemZoneId()).toInstant().toEpochMilli() / 1000;
    }


    /**
     * 计算时间天数差（按24小时为一天）
     */
    public static int calculate24HourDaysBetween(LocalDateTime start, LocalDateTime end) {
        // 确保start早于end，否则交换两者
        if (start.isAfter(end)) {
            LocalDateTime temp = start;
            start = end;
            end = temp;
        }
        // 计算总秒数差
        Duration duration = Duration.between(start, end);
        long totalSeconds = duration.getSeconds();
        // 转换为天数（24小时=86400秒）
        return (int) (totalSeconds / ONE_DAY_IN_SECONDS);
    }

    /**
     * 计算时间小时差
     */
    public static int calculateHourDurationBetween(LocalDateTime start, LocalDateTime end) {
        // 确保start早于end，否则交换两者
        if (start.isAfter(end)) {
            LocalDateTime temp = start;
            start = end;
            end = temp;
        }
        // 计算总秒数差
        Duration duration = Duration.between(start, end);
        long totalSeconds = duration.getSeconds();
        // 转换为小时（1小时=3600秒）
        return (int) (totalSeconds / ONE_HOUR_IN_SECONDS);
    }

    /**
     * 计算时间间的分钟差
     */
    public static int calculateMinuteDurationBetween(LocalDateTime start, LocalDateTime end) {
        // 确保start早于end，否则交换两者
        if (start.isAfter(end)) {
            LocalDateTime temp = start;
            start = end;
            end = temp;
        }
        // 计算总秒数差
        Duration duration = Duration.between(start, end);
        long totalSeconds = duration.getSeconds();
        // 转换为分钟
        return (int) (totalSeconds / ONE_MINUTE_IN_SECONDS);
    }


    /**
     * 计算两时间小时差之外的分钟差，例如09:30-11:40,小时差2，分钟差10
     */
    public static int calcMinutesDurationBesideHour(LocalDateTime start, LocalDateTime end) {
        if (start.isAfter(end)) {
            LocalDateTime temp = start;
            start = end;
            end = temp;
        }
        // 计算总秒数差
        Duration duration = Duration.between(start, end);
        long totalSeconds = duration.getSeconds();
        int hours = (int) (totalSeconds / ONE_HOUR_IN_SECONDS);
        // 去除小时差后的分钟差
        return (int) (totalSeconds - hours * ONE_HOUR_IN_SECONDS) / ONE_MINUTE_IN_SECONDS;
    }


    /**
     * 根据经纬度返回时区（默认回退到配置时区）
     */
    public static ZoneId latLonToTimeZone(double latitude, double longitude) {
        try {
            Optional<ZoneId> zoneId = INITIALIZE.query(latitude, longitude);
            return zoneId.orElseGet(LocalDateTimeUtils::getSystemZoneId);
        } catch (Exception e) {
            return getSystemZoneId();
        }
    }


    /**
     * 根据经纬度将当前时间转换为经纬度所在地时区的时间
     */
    public static LocalDateTime convertCurrentToLocalTime(LocalDateTime currentTime, double latitude, double longitude) {
        if (currentTime == null) {
            return null;
        }
        // 将 UTC 时间转换为 ZonedDateTime
        ZonedDateTime utcZonedDateTime = currentTime.atZone(getSystemZoneId());
        // 根据经纬度返回时区
        ZoneId zoneId = latLonToTimeZone(latitude, longitude);
        // 转换为目标时区的时间
        ZonedDateTime targetZonedDateTime = utcZonedDateTime.withZoneSameInstant(zoneId);
        // 返回 LocalDateTime
        return targetZonedDateTime.toLocalDateTime();
    }

    /**
     * 判断日期是否重叠方法
     *
     * @param schedules
     * @param <T>
     * @return
     */
    public static <T extends ScheduleInf> boolean hasOverlap(List<T> schedules) {
        if (schedules == null || schedules.isEmpty()) {
            return false;
        }

        // 过滤掉 null 或无效时间段
        List<T> validSchedules = schedules.stream()
                .filter(schedule -> schedule != null && schedule.getStartDate() != null && schedule.getEndDate() != null)
                .sorted(Comparator.comparing(ScheduleInf::getStartDate))
                .toList();

        // 检查相邻时间段是否重叠
        for (int i = 0; i < validSchedules.size() - 1; i++) {
            T current = validSchedules.get(i);
            T next = validSchedules.get(i + 1);
            if (!current.getEndDate().isBefore(next.getStartDate())) {
                return true; // 发现重叠
            }
        }
        return false; // 无重叠
    }

    /**
     * 获取系统配置的时区（从Spring配置读取）
     */
    public static ZoneId getSystemZoneId() {
        String zoneId = SpringContextHolder.getProperty("app.timezone", "");
        if (ObjectUtils.isEmpty(zoneId)) {
            return ZoneId.systemDefault();
        }
        return ZoneId.of("UTC"); // todo 临时解决，后续需要从配置中读取
    }


    /**
     * 将字符串转换为 UTC 时间的 LocalDateTime
     *
     * @param timeStr    输入时间字符串（格式 yyyy-MM-dd HH:mm）
     * @param sourceZone 输入字符串的时区（null 表示 UTC）
     */
    public static LocalDateTime toUtcLocalDateTime(String timeStr, ZoneId sourceZone) {
        LocalDateTime localDateTime = LocalDateTime.parse(timeStr, SHORT_DATETIME_FORMATTER);
        return sourceZone != null
                ? localDateTime.atZone(sourceZone)
                .withZoneSameInstant(ZoneOffset.UTC)
                .toLocalDateTime()
                : localDateTime;
    }

    /**
     * 计算LocalDateTime差值并返回时分秒
     *
     * @param start
     * @param end
     * @return
     */
    public static String calculateTimeDifference(LocalDateTime start, LocalDateTime end) {
        // 确保start早于end
        if (start.isAfter(end)) {
            LocalDateTime temp = start;
            start = end;
            end = temp;
        }

        Duration duration = Duration.between(start, end);

        long hours = duration.toHours();
        int minutes = (int) ((duration.getSeconds() % 3600) / 60);
        int seconds = (int) (duration.getSeconds() % 60);

        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }


}
