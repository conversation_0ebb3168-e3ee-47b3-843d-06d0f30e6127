package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.IxCarOrderCustomer;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【ix_car_order_customer(订单-客户信息表)】的数据库操作Mapper
* @createDate 2025-04-23 00:25:37
* @Entity com.ixtech.management.repo.entity.IxCarOrderCustomer
*/
@DS("ix")
@Mapper
public interface IxCarOrderCustomerMapper {
    Integer insert(IxCarOrderCustomer ixCarOrderCustomer);
}




