package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 履约状态枚举
 * 对应表: platform_order_fulfillment, 字段: status
 * (0-待开始，1-进行中，2-已完结)
 */
@Getter
@AllArgsConstructor
public enum FulfillmentStatusEnum implements DictInf {

    PENDING_START(0, "待开始"),
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完结"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

    public static FulfillmentStatusEnum fromCode(Object code) {
        return DictInf.fromCode(values(), code);
    }
}
