package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商列表响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:50
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VendorListInfoResp {

    /**
     * 供应商id
     */
    private Long id;

    /**
     * 供应商简称
     */
    private String name;

    /**
     * 公司全称
     */
    private String companyFullName;

    /**
     * 供应商代码
     */
    private String code;

    /**
     * 负责人
     */
    private String principal;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 电话区号
     */
    private String contactNumberCode;

    /**
     * 报价货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    private String quoteCurrency;

    /**
     * 结算货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    private String settlementCurrency;

    /**
     * 结算模式 1：底价模式 2：抽佣模式
     */
    private Byte settlementMode;

    /**
     * 结算模式（字符串描述）
     */
    private String settlementModeStr;

    /**
     * 比例x%
     */
    private BigDecimal rate;

    /**
     * 上下线状态 true：上线 false：下线
     */
    private Boolean active;

    /**
     * 覆盖国家数量
     */
    private Integer coveredCountryCount;

    /**
     * 覆盖城市数量
     */
    private Integer coveredCityCount;

    /**
     * 门店数量
     */
    private Integer storeCount;

    /**
     * 在线门店数量
     */
    private Integer activeStoreCount;

    /**
     * 录入车辆数
     */
    private Integer carCount;

    /**
     * 在线车辆数
     */
    private Integer activeCarCount;

}
