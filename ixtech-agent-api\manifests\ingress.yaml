apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agentapi-ingress
  namespace: default
spec:
  rules:
    - host: ${HOST1}
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: agent-service
                port:
                  number: 80
    - host: ${HOST2}
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: agent-service
                port:
                  number: 80