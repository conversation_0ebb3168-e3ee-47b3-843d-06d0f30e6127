package com.ixtech.management.facade.api;


import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.ChannelService;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.ChannelAddResp;
import com.ixtech.management.integration.internal.resp.ChannelInfoResp;
import com.ixtech.management.integration.internal.resp.ChannelListInfoResp;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 渠道管理
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:14
 */
@RestController
@RequestMapping("/v1/management/internal/channel")
public class ChannelController {

    @Resource
    private ChannelService channelService;

    /**
     * 查询渠道列表
     *
     * @param channelListQueryReq 条件筛选参数
     * @return
     */
    @PermitAll
    @PostMapping(value = "/list")
    public ApiResponse<PageResponse<ChannelListInfoResp>> list(@RequestBody ChannelListQueryReq channelListQueryReq) {
        PageResponse<ChannelListInfoResp> result = channelService.list(channelListQueryReq);
        return ApiResponse.success(result);
    }

    /**
     * 查询渠道信息
     *
     * @param id 渠道id
     * @return
     */
    @PermitAll
    @GetMapping(value = "/info")
    public ApiResponse<ChannelInfoResp> info(@RequestParam(value = "id") Long id) {
        ChannelInfoResp result = channelService.info(id);
        return ApiResponse.success(result);
    }

    /**
     * 添加渠道
     *
     * @param channelAddReq
     * @return
     */
    @PermitAll
    @PostMapping(value = "/add")
    public ApiResponse<ChannelAddResp> add(@Validated @RequestBody ChannelAddReq channelAddReq) {
        ChannelAddResp result = channelService.add(channelAddReq);
        return ApiResponse.success(result);
    }

    /**
     * 更新渠道
     *
     * @param channelUpdateReq
     * @return
     */
    @PermitAll
    @PostMapping(value = "/update")
    public ApiResponse<Void> update(@Validated @RequestBody ChannelUpdateReq channelUpdateReq) {
        channelService.update(channelUpdateReq);
        return ApiResponse.success();
    }

    /**
     * 渠道上下线
     *
     * @param channelSetActiveReq
     * @return
     */
    @PermitAll
    @PostMapping(value = "/set_active")
    public ApiResponse<Void> setActive(@Validated @RequestBody ChannelSetActiveReq channelSetActiveReq) {
        channelService.setActive(channelSetActiveReq);
        return ApiResponse.success();
    }

    /**
     * 渠道下拉列表
     *
     * @param channelDropdownReq
     * @return
     */
    @PermitAll
    @PostMapping(value = "/dropdown_list")
    public ApiResponse<List<SelectOptionResponse<Long>>> dropdownList(@RequestBody ChannelDropdownReq channelDropdownReq) {
        List<SelectOptionResponse<Long>> result = channelService.dropdownList(channelDropdownReq);
        return ApiResponse.success(result);
    }

}
