<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="ixtech-base-mysql-starter" />
        <module name="ixtech-vendor-basic" />
        <module name="ixtech-base-redis-starter" />
        <module name="ixtech-product" />
        <module name="ixtech-agent-api" />
        <module name="ixtech-base-feign-starter" />
        <module name="ixtech-order" />
        <module name="ixtech-base-common" />
        <module name="ixtech-translation" />
        <module name="ixtech-base-i18n-starter" />
        <module name="ixtech-vendor-management" />
      </profile>
      <profile name="Annotation profile for yudao-base" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../.m2/repository/org/springframework/boot/spring-boot-configuration-processor/3.4.1/spring-boot-configuration-processor-3.4.1.jar" />
          <entry name="$PROJECT_DIR$/../.m2/repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="$PROJECT_DIR$/../.m2/repository/org/mapstruct/mapstruct-processor/1.6.3/mapstruct-processor-1.6.3.jar" />
          <entry name="$PROJECT_DIR$/../.m2/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar" />
        </processorPath>
        <module name="yudao-spring-boot-starter-job" />
        <module name="yudao-server" />
        <module name="yudao-module-infra-biz (1)" />
        <module name="yudao-module-system-biz" />
        <module name="ixtech-module-admin-api" />
        <module name="yudao-spring-boot-starter-biz-tenant (1)" />
        <module name="yudao-spring-boot-starter-redis" />
        <module name="yudao-spring-boot-starter-security (1)" />
        <module name="yudao-spring-boot-starter-mybatis" />
        <module name="yudao-module-system-api (1)" />
        <module name="yudao-spring-boot-starter-protection" />
        <module name="yudao-spring-boot-starter-biz-tenant" />
        <module name="yudao-spring-boot-starter-websocket" />
        <module name="yudao-module-system-biz (1)" />
        <module name="yudao-spring-boot-starter-excel (1)" />
        <module name="yudao-spring-boot-starter-mq" />
        <module name="yudao-spring-boot-starter-protection (1)" />
        <module name="yudao-common" />
        <module name="yudao-spring-boot-starter-excel" />
        <module name="ix-merchants" />
        <module name="yudao-spring-boot-starter-web (1)" />
        <module name="yudao-spring-boot-starter-biz-data-permission" />
        <module name="yudao-spring-boot-starter-security" />
        <module name="yudao-spring-boot-starter-job (1)" />
        <module name="yudao-spring-boot-starter-web" />
        <module name="yudao-spring-boot-starter-websocket (1)" />
        <module name="yudao-spring-boot-starter-mybatis (1)" />
        <module name="yudao-spring-boot-starter-biz-ip" />
        <module name="yudao-module-infra-api" />
        <module name="yudao-spring-boot-starter-monitor" />
        <module name="yudao-module-infra-biz" />
        <module name="yudao-spring-boot-starter-biz-data-permission (1)" />
        <module name="ixtech-module-merchant-biz" />
        <module name="yudao-spring-boot-starter-monitor (1)" />
        <module name="yudao-spring-boot-starter-mq (1)" />
        <module name="yudao-module-infra-api (1)" />
        <module name="yudao-module-system-api" />
        <module name="yudao-spring-boot-starter-biz-ip (1)" />
        <module name="ixtech-module-admin-biz" />
        <module name="yudao-spring-boot-starter-redis (1)" />
        <module name="ixtech-module-merchant-api" />
        <module name="yudao-common (1)" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ix-merchants" options="-parameters" />
      <module name="ixtech-module-admin-api" options="-parameters" />
      <module name="ixtech-module-admin-biz" options="-parameters" />
      <module name="ixtech-module-merchant-api" options="-parameters" />
      <module name="ixtech-module-merchant-biz" options="-parameters" />
      <module name="yudao-common" options="-parameters" />
      <module name="yudao-common (1)" options="-parameters" />
      <module name="yudao-module-infra-api" options="-parameters" />
      <module name="yudao-module-infra-api (1)" options="-parameters" />
      <module name="yudao-module-infra-biz" options="-parameters" />
      <module name="yudao-module-infra-biz (1)" options="-parameters" />
      <module name="yudao-module-infra-biz (2)" options="-parameters" />
      <module name="yudao-module-system-api" options="-parameters" />
      <module name="yudao-module-system-api (1)" options="-parameters" />
      <module name="yudao-module-system-biz" options="-parameters" />
      <module name="yudao-module-system-biz (1)" options="-parameters" />
      <module name="yudao-module-system-biz (2)" options="-parameters" />
      <module name="yudao-server" options="-parameters" />
      <module name="yudao-spring-boot-starter-biz-data-permission" options="-parameters" />
      <module name="yudao-spring-boot-starter-biz-data-permission (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-biz-ip" options="-parameters" />
      <module name="yudao-spring-boot-starter-biz-ip (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-biz-tenant" options="-parameters" />
      <module name="yudao-spring-boot-starter-biz-tenant (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-excel" options="-parameters" />
      <module name="yudao-spring-boot-starter-excel (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-job" options="-parameters" />
      <module name="yudao-spring-boot-starter-job (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-monitor" options="-parameters" />
      <module name="yudao-spring-boot-starter-monitor (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-mq" options="-parameters" />
      <module name="yudao-spring-boot-starter-mq (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-mybatis" options="-parameters" />
      <module name="yudao-spring-boot-starter-mybatis (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-protection" options="-parameters" />
      <module name="yudao-spring-boot-starter-protection (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-redis" options="-parameters" />
      <module name="yudao-spring-boot-starter-redis (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-security" options="-parameters" />
      <module name="yudao-spring-boot-starter-security (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-web" options="-parameters" />
      <module name="yudao-spring-boot-starter-web (1)" options="-parameters" />
      <module name="yudao-spring-boot-starter-websocket" options="-parameters" />
      <module name="yudao-spring-boot-starter-websocket (1)" options="-parameters" />
    </option>
  </component>
</project>