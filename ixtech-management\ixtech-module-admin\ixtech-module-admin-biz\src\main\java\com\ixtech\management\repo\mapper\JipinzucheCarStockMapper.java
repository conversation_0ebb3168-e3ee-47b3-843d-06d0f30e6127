package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.JipinzucheCarStock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【jipinzuche_car_stock(车辆库存表)】的数据库操作Mapper
* @createDate 2025-04-20 22:53:08
* @Entity com.ixtech.management.repo.entity.JipinzucheCarStock
*/
@DS("ix")
@Mapper
public interface JipinzucheCarStockMapper {
    JipinzucheCarStock selectById(Long id);

    List<JipinzucheCarStock> selectByModelId(Long id);

    List<JipinzucheCarStock> selectByModelIdAndStoreId(@Param("modelid") Long modelid, @Param("storeid") Long storeid);
}




