package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.JipinzucheCarModel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【jipinzuche_car_model(车辆表)】的数据库操作Mapper
* @createDate 2025-04-20 22:44:05
* @Entity com.ixtech.management.repo.entity.JipinzucheCarModel
*/
@DS("ix")
@Mapper
public interface JipinzucheCarModelMapper {

    JipinzucheCarModel selectById(Long id);

    List<JipinzucheCarModel> selectAll();

}




