package com.ixtech.global.common.enums;
import com.ixtech.global.common.enums.inf.DictInf;
import lombok.Getter;
import lombok.AllArgsConstructor;

/**
 * 门店类型(站点类型)枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StoreTypeEnum implements DictInf {

    /**
     * 门店
     */
    STORE("0", "门店"),

    /**
     * 服务点
     */
    SERVICE_POINT("1", "服务点");

    /**
     * 类型值 (与数据库定义一致)
     */
    private final String value;

    /**
     * 类型名称
     */
    private final String label;

    public static String getLabelByValue(String value) {
        for (StoreTypeEnum e : values()) {
            if (String.valueOf(e.value).equals(value)) {
                return e.label;
            }
        }
        return "";
    }
}