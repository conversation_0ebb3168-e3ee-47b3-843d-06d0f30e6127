<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.JipinzucheCarStockMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.JipinzucheCarStock">
            <id property="id" column="id" />
            <result property="active" column="active" />
            <result property="deleted" column="deleted" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="modelid" column="modelid" />
            <result property="storeid" column="storeid" />
            <result property="stock" column="stock" />
            <result property="groupName" column="group_name" />
            <result property="accident" column="accident" />
            <result property="accidentUnit" column="accident_unit" />
            <result property="time" column="time" />
            <result property="ip" column="ip" />
            <result property="mid" column="mid" />
            <result property="status" column="status" />
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,modelid,
        storeid,stock,group_name,accident,accident_unit,
        time,ip,mid,status
    </sql>

    <sql id="Base_delete">
        and active = 1 and deleted = 0
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
            from jipinzuche_car_stock
            where id = #{id}
        <include refid="Base_delete" />
    </select>
    <select id="selectByModelId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_stock
        where modelid = #{id}
        <include refid="Base_delete" />
    </select>

    <select id="selectByModelIdAndStoreId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_stock
        where modelid = #{modelid} and storeid = #{storeid}
        <include refid="Base_delete" />
        order by id desc
    </select>
</mapper>
