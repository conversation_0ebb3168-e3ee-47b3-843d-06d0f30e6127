package com.ixtech.global.config;

import com.ixtech.global.I18nTranslationExecutor;
import com.ixtech.global.aop.I18nResponseBodyAdvice;
import com.ixtech.global.aop.I18nTranslationAopAspect;
import com.ixtech.global.cache.I18nTranslationCacheInitializer;
import com.ixtech.global.cache.I18nTranslationCacheManager;
import com.ixtech.global.feign.client.TranslationFeignClient;
import com.ixtech.global.interceptor.AcceptLanguageFeignInterceptor;
import com.ixtech.global.interceptor.AcceptLanguageFilter;
import feign.Feign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * i18n功能自动装配
 *
 * <AUTHOR> hu
 * @date 2025/6/17 10:31
 */
@Slf4j
@AutoConfiguration
@EnableFeignClients(basePackageClasses = {TranslationFeignClient.class})
@EnableConfigurationProperties(I18nProperties.class)
@ConditionalOnProperty(prefix = "i18n", name = "enable", havingValue = "true", matchIfMissing = true)
public class I18nAutoConfiguration implements WebMvcConfigurer {

    @Bean
    @ConditionalOnMissingBean
    public I18nTranslationCacheManager translationCacheManager(I18nProperties i18nProperties) {
        return new I18nTranslationCacheManager(i18nProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    public I18nTranslationCacheInitializer translationCacheInitializer(I18nTranslationCacheManager cacheManager) {
        return new I18nTranslationCacheInitializer(cacheManager);
    }

    @Bean
    @ConditionalOnMissingBean
    public I18nTranslationExecutor translationExecutor(I18nTranslationCacheManager cacheManager, TranslationFeignClient feignClient) {
        return new I18nTranslationExecutor(cacheManager, feignClient);
    }

    @Bean
    public FilterRegistrationBean<AcceptLanguageFilter> acceptLanguageFilterRegistration(I18nProperties i18nProperties) {
        FilterRegistrationBean<AcceptLanguageFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new AcceptLanguageFilter());
        registration.addUrlPatterns(i18nProperties.getLanguageFilter().getUrlPatterns());
        registration.setOrder(i18nProperties.getLanguageFilter().getOrder());
        return registration;
    }

    @Bean
    @ConditionalOnClass(Feign.class)
    @ConditionalOnMissingBean
    public AcceptLanguageFeignInterceptor feignAcceptLanguageInterceptor() {
        return new AcceptLanguageFeignInterceptor();
    }

    @Bean
    @ConditionalOnMissingBean
    public I18nTranslationAopAspect i18nTranslationAspect(I18nTranslationExecutor i18nTranslationExecutor) {
        return new I18nTranslationAopAspect(i18nTranslationExecutor);
    }

    @Bean
    @ConditionalOnMissingBean
    public I18nResponseBodyAdvice i18nResponseBodyAdvice(I18nTranslationExecutor i18nTranslationExecutor, I18nProperties i18nProperties) {
        return new I18nResponseBodyAdvice(i18nTranslationExecutor, i18nProperties);
    }

//    @Bean
//    public BeanPostProcessor i18nValidatorPostProcessor(I18nTranslationExecutor i18nTranslationExecutor) {
//        return new BeanPostProcessor() {
//            @Override
//            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
//                // 只处理 LocalValidatorFactoryBean 类型的 Bean
//                if (bean instanceof LocalValidatorFactoryBean factory) {
//
//                    // 获取默认的 MessageInterpolator
//                    MessageInterpolator defaultInterpolator = Optional.ofNullable(factory.getMessageInterpolator())
//                            .orElseGet(() -> Validation.byDefaultProvider().configure().getDefaultMessageInterpolator());
//
//                    // 创建自定义的 MessageInterpolator，并包装默认的 Interpolator
//                    I18nValidateMessageInterpolator i18nValidateMessageInterpolator =
//                            new I18nValidateMessageInterpolator(defaultInterpolator, i18nTranslationExecutor);
//                    factory.setMessageInterpolator(i18nValidateMessageInterpolator);
//
//                    log.info("=== 已替换 LocalValidatorFactoryBean 的 MessageInterpolator ===");
//                }
//                return bean;
//            }
//        };
//    }

}
