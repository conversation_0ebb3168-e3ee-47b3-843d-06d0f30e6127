package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 免费延迟还车时间单位枚举
 * 对应字段: free_delay_return_time_unit
 * (1-小时，默认; 2-分钟)
 */
@Getter
@AllArgsConstructor
public enum FreeDelayReturnTimeUnitEnum implements DictInf {

    HOUR(1, "小时"),
    MINUTE(2, "分钟"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }

}
