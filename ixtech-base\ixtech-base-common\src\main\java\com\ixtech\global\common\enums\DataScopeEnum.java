package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据范围枚举
 * (1-全部服务单位, 2-自定义服务单位, 3-本服务单位, 4-本服务单位及下属服务单位)
 */
@Getter
@AllArgsConstructor
public enum DataScopeEnum implements DictInf {

    ALL_SERVICE_UNITS(1, "全部服务单位"),
    CUSTOM_SERVICE_UNITS(2, "自定义服务单位"),
    CURRENT_SERVICE_UNIT(3, "本服务单位"),
    CURRENT_AND_SUB_SERVICE_UNITS(4, "本服务单位及下属服务单位"),
    ;

    private final Integer code;
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(this.code);
    }
}