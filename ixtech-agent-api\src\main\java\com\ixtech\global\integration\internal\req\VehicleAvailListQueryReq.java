package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 车辆搜索req
 *
 * <AUTHOR>
 * @date  2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehicleAvailListQueryReq implements Serializable {

 private static final long serialVersionUID = 7985504938627085943L;

 /**
  * 车辆偏好列表
  */
 private List<String> vehPrefs;

 /**
  * 供应商偏好代码
  */
 private String vendorPrefCode;

 /**
  * 费率限定
  */
 private RateQualifierReq rateQualifier;

 /**
  * 取车时间
  */
 @NotNull(message = "取车时间不能为空")
 private String pickUpDateTime;

 /**
  * 还车时间
  */
 private String returnDateTime;

 /**
  * 取车地点
  */
 private LocationReq pickupLocation;

 /**
  * 还车地点
  */
 private LocationReq returnLocation;

 /**
  * 车辆可用性请求信息
  */
 private VehAvailQueryInfoReq vehAvailRqInfo;

}
