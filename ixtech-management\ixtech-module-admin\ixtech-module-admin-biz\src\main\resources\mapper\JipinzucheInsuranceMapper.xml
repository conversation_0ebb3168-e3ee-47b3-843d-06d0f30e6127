<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.JipinzucheInsuranceMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.JipinzucheInsurance">
            <id property="id" column="id" />
            <result property="active" column="active" />
            <result property="deleted" column="deleted" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="title" column="title" />
            <result property="code" column="code" />
            <result property="mid" column="mid" />
            <result property="ip" column="ip" />
            <result property="isDelete" column="is_delete" />
            <result property="info" column="info" />
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,title,
        code,mid,ip,is_delete,info
    </sql>

    <sql id="Base_delete">
        and active=1 and deleted = 0 and is_delete = 0
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
            from jipinzuche_insurance
        where id = #{id}
        <include refid="Base_delete" />
    </select>

</mapper>
