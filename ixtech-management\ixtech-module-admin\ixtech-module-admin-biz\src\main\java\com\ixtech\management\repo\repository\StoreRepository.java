package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.CarModelPO;
import com.ixtech.management.repo.entity.Store;
import com.ixtech.management.repo.mapper.CarModelMapper;
import com.ixtech.management.repo.mapper.StoreMapper;
import com.ixtech.management.repo.model.CountModel;
import com.ixtech.management.repo.model.VendorAreaCountModel;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:29
 */
@Repository
public class StoreRepository {

    @Resource
    private StoreMapper storeMapper;
    @Resource
    private CarModelMapper carModelMapper;

    /**
     * 获取指定供应商下的门店覆盖国家/城市数量
     *
     * @param vendorIds 供应商id列表
     * @return
     */
    public Map<Long, VendorAreaCountModel> getStoreCoveredAreaCountByVendorIds(Collection<Long> vendorIds) {
        if (CollectionUtils.isEmpty(vendorIds)) {
            return new HashMap<>();
        }
        List<VendorAreaCountModel> list = storeMapper.getStoreCoveredAreaCountByVendorIds(vendorIds);
        return CollectionUtils.emptyIfNull(list).stream()
                .collect(Collectors.toMap(VendorAreaCountModel::getVendorId, Function.identity()));
    }

    /**
     * 查询指定供应商的门店数量
     *
     * @param vendorIds 供应商id列表
     * @return
     */
    public Map<Long, CountModel> getStoreCountByVendorIds(Collection<Long> vendorIds) {
        if (CollectionUtils.isEmpty(vendorIds)) {
            return new HashMap<>();
        }
        List<CountModel> list = storeMapper.getStoreCountByVendorIds(vendorIds);
        return CollectionUtils.emptyIfNull(list).stream()
                .collect(Collectors.toMap(CountModel::getTargetId, Function.identity()));
    }

    /**
     * 查询指定供应商下的门店车辆数量
     *
     * @param vendorIds 供应商id列表
     * @return
     */
    public Map<Long, CountModel> getStoreCarCountByVendorIds(Collection<Long> vendorIds) {
        if (CollectionUtils.isEmpty(vendorIds)) {
            return new HashMap<>();
        }
        List<CountModel> list = storeMapper.getStoreCarCountByVendorIds(vendorIds);
        return CollectionUtils.emptyIfNull(list).stream()
                .collect(Collectors.toMap(CountModel::getTargetId, Function.identity()));
    }

    /**
     * 查询指定门店的车辆数量
     *
     * @param storeIds 门店id列表
     * @return
     */
    public Map<Long, CountModel> getStoreCarCountByStoreIds(Collection<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return new HashMap<>();
        }
        List<CountModel> list = storeMapper.getStoreCarCountByStoreIds(storeIds);
        return CollectionUtils.emptyIfNull(list).stream()
                .collect(Collectors.toMap(CountModel::getTargetId, Function.identity()));
    }

    /**
     * 查询指定门店的信息
     *
     * @param id 门店id
     * @return
     */
    public Store selectById(Long id) {
        return storeMapper.selectByPrimaryKey(id);
    }

    /**
     * 条件查询门店信息
     *
     * @param name          门店名称
     * @param countryId     国家id
     * @param provinceId    省份id
     * @param cityId        城市id
     * @param vendorId      供应商id
     * @param types         类型列表
     * @param displayStatus 上下线状态
     * @return
     */
    public List<Store> searchByCondition(String name,
                                         Long countryId,
                                         Long provinceId,
                                         Long cityId,
                                         Long vendorId,
                                         List<Integer> types,
                                         Integer displayStatus) {
        return storeMapper.selectByCondition(name, countryId, provinceId, cityId, vendorId, types, displayStatus);
    }

    /**
     * 查询门店覆盖车型
     *
     * @param id 门店id
     * @return
     */
    public List<CarModelPO> getCarModelsByStoreId(Long id) {
        return carModelMapper.getCarModelsByStoreId(id);
    }

}
