package com.ixtech.management.common.utils;

import com.ixtech.management.common.exception.BizException;
import net.iakovlev.timeshape.TimeZoneEngine;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import java.time.Instant;
import java.time.ZoneId;
import java.util.Optional;
import java.util.TimeZone;

/**
 * <AUTHOR> hu
 * @date 2025/4/13 13:58
 */
public class DateUtils {

    // 默认系统时区
    private static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();

    // UTC 时区
    private static final ZoneId UTC_ZONE = ZoneOffset.UTC;

    // 默认日期时间格式（ISO-8601）
    private static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter DEFAULT_FORMATTER =
            DateTimeFormatter.ofPattern(DEFAULT_PATTERN);

    /**
     * LocalDateTime → Date（默认时区）
     */
    public static Date toDate(LocalDateTime localDateTime) {
        return toDate(localDateTime, DEFAULT_ZONE);
    }

    /**
     * LocalDateTime → Date（指定时区）
     */
    public static Date toDate(LocalDateTime localDateTime, ZoneId zoneId) {
        return Date.from(localDateTime.atZone(zoneId).toInstant());
    }

    /**
     * Date → LocalDateTime（默认时区）
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        return toLocalDateTime(date, DEFAULT_ZONE);
    }

    /**
     * Date → LocalDateTime（指定时区）
     */
    public static LocalDateTime toLocalDateTime(Date date, ZoneId zoneId) {
        return date.toInstant().atZone(zoneId).toLocalDateTime();
    }

    /**
     * LocalDateTime（本地时间）→ UTC Date
     */
    public static Date toUtcDate(LocalDateTime localDateTime) {
        return toDate(localDateTime, UTC_ZONE);
    }

    /**
     * Date（本地时间）→ UTC Date
     */
    public static Date toUtcDate(Date date) {
        return toDate(toLocalDateTime(date), UTC_ZONE);
    }

    /**
     * 当前时间 → UTC Date
     */
    public static Date nowAsUtcDate() {
        return toUtcDate(LocalDateTime.now());
    }

    /**
     * 格式化 Date（默认时区）
     */
    public static String format(Date date) {
        return format(date, DEFAULT_PATTERN, DEFAULT_ZONE);
    }

    /**
     * 格式化 Date（指定时区）
     */
    public static String format(Date date, String pattern, ZoneId zoneId) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        sdf.setTimeZone(TimeZone.getTimeZone(zoneId));
        return sdf.format(date);
    }

    /**
     * 格式化 Date（UTC 时区）
     */
    public static String formatAsUtc(Date date) {
        return format(date, DEFAULT_PATTERN, UTC_ZONE);
    }

    /**
     * 解析字符串 → Date（默认时区）
     */
    public static Date parse(String dateStr) {
        return parse(dateStr, DEFAULT_PATTERN, DEFAULT_ZONE);
    }

    /**
     * 解析字符串 → Date（指定时区
     */
    public static Date parse(String dateStr, String pattern, ZoneId zoneId) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            sdf.setTimeZone(TimeZone.getTimeZone(zoneId));
            return sdf.parse(dateStr);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期解析失败: " + dateStr, e);
        }
    }

    /**
     * 解析字符串 → Date（UTC 时区）
     */
    public static Date parseAsUtc(String dateStr) {
        return parse(dateStr, DEFAULT_PATTERN, UTC_ZONE);
    }

    /**
     * LocalDateTime -> string
     * @param dateTime
     * @return
     */
    public static String format(LocalDateTime dateTime) {
        return dateTime.format(DEFAULT_FORMATTER);
    }

    /**
     * LocalDateTime -> string (处理空值情况)
     * @param dateTime
     * @return
     */
    public static String formatSafe(LocalDateTime dateTime) {
        return dateTime != null ? format(dateTime) : "";
    }

    /**
     * string -> LocalDateTime
     * @param dateStr
     * @return
     */
    public static LocalDateTime parseStringToLocalDateTime(String dateStr) {
        if (null == dateStr || dateStr.isEmpty()) {
            return null;
        }
        return LocalDateTime.parse(dateStr, DEFAULT_FORMATTER);
    }

    /**
     * str -> timestamp
     * @param dateStr
     * @return
     */
    public static long parseToTimestamp(String dateStr) {
        DateTimeFormatter FORMATTER =
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(dateStr, FORMATTER)
                .atZone(ZoneId.of("GMT"))
                .toEpochSecond();
    }

    // 自动识别时间戳并转换为字符串（默认格式）
    public static String timestampToString(long timestamp) {
        if (timestamp <= 0) {
            return "";
        }
        return timestampToString(timestamp, DEFAULT_FORMATTER);
    }

    // 带格式化的转换
    public static String timestampToString(long timestamp, DateTimeFormatter formatter) {
        long normalizedTs = normalizeTimestamp(timestamp);
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(normalizedTs),
                ZoneId.systemDefault()
        );
        return dateTime.format(formatter);
    }

    // 传统SimpleDateFormat方式
    public static String timestampToString(long timestamp, String pattern) {
        long normalizedTs = normalizeTimestamp(timestamp);
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(new Date(normalizedTs));
    }

    // 判断时间戳是否为秒级
    public static boolean isSecondLevelTimestamp(long timestamp) {
        return timestamp < 1_000_000_000_000L;
    }

    public static long normalizeTimestamp(long timestamp) {
        // 判断是秒级还是毫秒级时间戳
        if (timestamp < 1_000_000_000_000L) { // 小于 2001-09-09 01:46:40
            return timestamp * 1000; // 秒级转毫秒级
        }
        return timestamp; // 已经是毫秒级
    }

    public static int calculateMinuteDurationBetween(LocalDateTime start, LocalDateTime end) {
        if (start.isAfter(end)) {
            LocalDateTime temp = start;
            start = end;
            end = temp;
        }

        Duration duration = Duration.between(start, end);
        long totalSeconds = duration.getSeconds();
        return (int)(totalSeconds / 60L);
    }

    public static LocalDateTime timestampToLocalDateTime(long timestamp) {
        // 判断是秒级还是毫秒级
        if (String.valueOf(timestamp).length() == 10) {
            // 秒级时间戳
            return LocalDateTime.ofInstant(
                    Instant.ofEpochSecond(timestamp),
                    ZoneId.systemDefault()
            );
        } else {
            // 毫秒级时间戳
            return LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(timestamp),
                    ZoneId.systemDefault()
            );
        }
    }

    public static ZoneId latLonToTimeZone(double latitude, double longitude) {
        TimeZoneEngine engine = TimeZoneEngine.initialize();
        try {
            Optional<ZoneId> zoneId = engine.query(latitude, longitude);
            if (zoneId.isPresent()) {
                return zoneId.get();
            }
        } catch (Exception e) {
            throw new BizException("获取时区失败");
        }
        return null;
    }

    /**
     * 将 UTC 时间戳转换为当前时区的日期时间字符串
     * @param utcTimestamp UTC 时间戳（毫秒）
     * @return 当前时区的时间字符串
     */
    public static String utcToLocalTimeString(Long utcTimestamp, double latitude, double longitude) {
        if (utcTimestamp.toString().length() != 10 && utcTimestamp.toString().length() != 13) {
            throw new BizException("时间戳非法");
        }
        Instant instant = Instant.ofEpochMilli(utcTimestamp);
        ZoneId zoneId = latLonToTimeZone(latitude, longitude);
        if (zoneId != null) {
            ZonedDateTime localTime = instant.atZone(zoneId);
            return localTime.format(DEFAULT_FORMATTER);
        } else {
            throw new BizException("时区转换失败");
        }
    }

}
