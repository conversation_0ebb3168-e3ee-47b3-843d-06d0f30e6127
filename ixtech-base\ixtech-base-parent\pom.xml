<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ixtech.global</groupId>
        <artifactId>ixtech-base</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ixtech-base-parent</artifactId>
    <name>ixtech-base-parent</name>
    <packaging>pom</packaging>

    <properties>
        <java.version>21</java.version>
        <project.basedir>${basedir}/..</project.basedir>
        <spring-boot.version>3.4.4</spring-boot.version>
        <!--<spring.context>6.0.11</spring.context>-->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <lombok.version>1.18.34</lombok.version>
        <maven.flatten.version>1.7.0</maven.flatten.version>
        <mybatis-boot-starter.version>3.0.3</mybatis-boot-starter.version>
        <mybatis.version>3.5.16</mybatis.version>
        <druid.spring.version>1.2.21</druid.spring.version>
        <druid.version>1.2.20</druid.version>
        <mysql.connector.version>8.4.0</mysql.connector.version>
        <commons-lang3.version>3.8</commons-lang3.version>
        <commons.collections4.version>4.3</commons.collections4.version>
        <redission.version>3.27.0</redission.version>
        <javax.version>1.3.2</javax.version>
        <openfeign.version>4.2.1</openfeign.version>
        <jackson-core-version>2.18.0</jackson-core-version>
        <jackson-databind-version>2.16.0</jackson-databind-version>
        <guava.version>33.2.0-jre</guava.version>
        <hashids.version>1.0.3</hashids.version>
        <generator.version>1.3.7</generator.version>
        <jodd.version>5.0.13</jodd.version>
        <junit.version>5.12.1</junit.version>
        <github.pagehelper.version>6.1.0</github.pagehelper.version>
        <ttl.version>2.14.5</ttl.version>
        <timeshape.version>2024a.25</timeshape.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <oss.version>3.17.4</oss.version>
        <feign.jackson.version>12.3</feign.jackson.version>
        <easyexcel.version>4.0.3</easyexcel.version>
        <commons.beanutils.version>1.9.4</commons.beanutils.version>
        <yitter.idgenerator.version>1.0.6</yitter.idgenerator.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ixtech.global</groupId>
                <artifactId>ixtech-base-mysql-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.ixtech.global</groupId>
                <artifactId>ixtech-base-feign-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.ixtech.global</groupId>
                <artifactId>ixtech-base-redis-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.ixtech.global</groupId>
                <artifactId>ixtech-base-i18n-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.ixtech.global</groupId>
                <artifactId>ixtech-base-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--<dependency>-->
            <!--    <groupId>org.springframework.boot</groupId>-->
            <!--    <artifactId>spring-boot-starter</artifactId>-->
            <!--    <version>${spring-boot.version}</version>-->
            <!--    <type>pom</type>-->
            <!--    <scope>import</scope>-->
            <!--</dependency>-->
            <!--<dependency>-->
            <!--    <groupId>org.springframework.boot</groupId>-->
            <!--    <artifactId>spring-boot-starter-test</artifactId>-->
            <!--    <version>${spring-boot.version}</version>-->
            <!--    <type>pom</type>-->
            <!--    <scope>import</scope>-->
            <!--</dependency>-->
            <!--<dependency>-->
            <!--    <groupId>org.springframework.boot</groupId>-->
            <!--    <artifactId>spring-boot-starter-web</artifactId>-->
            <!--    <version>${spring-boot.version}</version>-->
            <!--    <type>pom</type>-->
            <!--    <scope>import</scope>-->
            <!--</dependency>-->
            <!--<dependency>-->
            <!--    <groupId>org.springframework.boot</groupId>-->
            <!--    <artifactId>spring-boot-autoconfigure</artifactId>-->
            <!--    <version>${spring-boot.version}</version>-->
            <!--    <type>pom</type>-->
            <!--    <scope>import</scope>-->
            <!--</dependency>-->
            <!--<dependency>-->
            <!--    <groupId>org.springframework</groupId>-->
            <!--    <artifactId>spring-context</artifactId>-->
            <!--    <version>${spring.context}</version>-->
            <!--    <type>pom</type>-->
            <!--    <scope>import</scope>-->
            <!--</dependency>-->
            <!-- 配置注解支持 -->
            <!--<dependency>-->
            <!--    <groupId>org.springframework.boot</groupId>-->
            <!--    <artifactId>spring-boot-configuration-processor</artifactId>-->
            <!--    <version>${spring-boot.version}</version>-->
            <!--    <type>pom</type>-->
            <!--    <scope>import</scope>-->
            <!--    <optional>true</optional>-->
            <!--</dependency>-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons.collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redission.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-web</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>${javax.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${openfeign.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-loadbalancer</artifactId>
                <version>${openfeign.version}</version>
            </dependency>
            <!--工具类-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hashids</groupId>
                <artifactId>hashids</artifactId>
                <version>${hashids.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${generator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${generator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodd</groupId>
                <artifactId>jodd-bean</artifactId>
                <version>${jodd.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${github.pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson-databind-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson-databind-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${ttl.version}</version>
            </dependency>
            <dependency>
                <groupId>net.iakovlev</groupId>
                <artifactId>timeshape</artifactId>
                <version>${timeshape.version}</version>
            </dependency>
            <!-- MapStruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${oss.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-jackson</artifactId>
                <version>${feign.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons.beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yitter</groupId>
                <artifactId>yitter-idgenerator</artifactId>
                <version>${yitter.idgenerator.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
