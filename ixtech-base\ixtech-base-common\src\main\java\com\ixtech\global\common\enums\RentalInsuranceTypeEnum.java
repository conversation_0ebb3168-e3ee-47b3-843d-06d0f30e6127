package com.ixtech.global.common.enums;

import lombok.Getter;

/**
 * 车辆状态枚举类
 */
@Getter
public enum RentalInsuranceTypeEnum {

    BASIC(1, "基本险"),

    ALL(2, "全额险"),

    STRONG(3, "综合险");

    /**
     * 码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

    /**
     * 构造方法
     *
     * @param code 状态码
     * @param name 状态名称
     */
    RentalInsuranceTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RentalInsuranceTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RentalInsuranceTypeEnum type : RentalInsuranceTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
