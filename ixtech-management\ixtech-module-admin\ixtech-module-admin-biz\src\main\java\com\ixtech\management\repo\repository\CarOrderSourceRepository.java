package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.CarOrderSource;
import com.ixtech.management.repo.mapper.CarOrderSourceMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/18 14:15
 */
@Repository
public class CarOrderSourceRepository {

    @Resource
    private CarOrderSourceMapper carOrderSourceMapper;

    /**
     * 条件查询渠道信息
     *
     * @param id              渠道id
     * @param channelName     渠道简称
     * @param companyFullName 公司全称
     * @return
     */
    public List<CarOrderSource> searchByCondition(Long id, String channelName, String companyFullName) {
        return carOrderSourceMapper.searchByCondition(id, channelName, companyFullName);
    }

    /**
     * 查询指定渠道信息
     *
     * @param id 渠道id
     * @return
     */
    public CarOrderSource selectById(Long id) {
        return carOrderSourceMapper.selectByPrimaryKey(id);
    }

    /**
     * 插入渠道信息
     *
     * @param channel
     * @return
     */
    public Long insertSelective(CarOrderSource channel) {
        return carOrderSourceMapper.insertSelective(channel);
    }

    /**
     * 更新渠道信息
     *
     * @param channel
     */
    public int updateByPrimaryKeySelective(CarOrderSource channel) {
        return carOrderSourceMapper.updateByPrimaryKeySelective(channel);
    }

    /**
     * 查询指定stage对应的渠道信息
     *
     * @param stage
     * @return
     */
    public List<CarOrderSource> selectByStage(String stage) {
        return carOrderSourceMapper.selectByStage(stage);
    }

    /**
     * 获取所有供应商
     *
     * @return
     */
    public List<CarOrderSource> queryAllCarOrderSources() {
        return carOrderSourceMapper.findAllByJipinzucheCarOrderSource();
    }

    public CarOrderSource selectByTitle(String fileType) {
        return carOrderSourceMapper.selectByTitle(fileType);
    }

}
