package com.ixtech.global.config;

import feign.Request;
import org.springframework.context.annotation.Bean;

import java.util.concurrent.TimeUnit;

/**
 * 翻译服务调用FeignClient配置
 *
 * <AUTHOR> hu
 * @date 2025/6/20 20:22
 */
public class TranslationFeignClientConfiguration {

    @Bean
    public Request.Options options(I18nProperties i18nProperties) {
        I18nProperties.Feign feign = i18nProperties.getFeign();
        return new Request.Options(
                feign.getConnectTimeout(), TimeUnit.MILLISECONDS,
                feign.getReadTimeout(), TimeUnit.MILLISECONDS,
                true);
    }

}
