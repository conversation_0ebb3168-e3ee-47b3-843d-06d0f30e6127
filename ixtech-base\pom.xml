<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ixtech.global</groupId>
    <artifactId>ixtech-base</artifactId>
    <version>${revision}</version>
    <name>ixtech-base</name>
    <description>ixtech base</description>
    <packaging>pom</packaging>

    <modules>
        <module>ixtech-base-common</module>
        <module>ixtech-base-parent</module>
        <module>ixtech-base-starters</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <revision>1.0.19-SNAPSHOT</revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.basedir>${basedir}</project.basedir>
        <maven.flatten.version>1.7.0</maven.flatten.version>
    </properties>


    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.7.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <outputDirectory>${project.build.directory}/flatten</outputDirectory>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <pomElements>
                        <dependencyManagement>expand</dependencyManagement>
                        <pluginManagement>expand</pluginManagement>
                        <properties>keep</properties>
                        <repositories>keep</repositories>
                        <distributionManagement>remove</distributionManagement>
                    </pomElements>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


    <distributionManagement>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <name>rdc-snapshots</name>
            <url>
                https://packages.aliyun.com/677b967610def31b486d77d9/maven/2513107-snapshot-nrql5x
            </url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

</project>
