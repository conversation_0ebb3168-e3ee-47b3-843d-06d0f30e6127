package com.ixtech.management.repo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 供应商实体
 *
 * <AUTHOR> hu
 * @date 2025/4/4 11:12
 */
@Data
@NoArgsConstructor
public class Vendor extends BaseEntity {

    /**
     * 供应商代码
     */
    private String code;

    /**
     * 供应商名称
     */
    private String name;

    /**
     * 供应商logo
     */
    private String logo;

    /**
     * 供应商描述
     */
    private String description;

    /**
     * 公司全称
     */
    private String companyFullName;

    /**
     * 报价货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    private String quoteCurrency;

    /**
     * 结算货币
     *
     * @see com.ixtech.management.common.enums.SettlementCurrencyEnum
     */
    private String settlementCurrency;

    /**
     * 结算模式 1:底价模式 2:抽佣模式
     *
     * @see com.ixtech.management.common.enums.SettlementModeEnum
     */
    private Byte settlementMode;

    /**
     * 比例x%
     */
    private BigDecimal rate;

    /**
     * 负责人
     */
    private String principal;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 电话区号
     */
    private String contactNumberCode;

}