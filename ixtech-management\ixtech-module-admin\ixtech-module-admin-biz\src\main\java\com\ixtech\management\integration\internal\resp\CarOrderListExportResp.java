package com.ixtech.management.integration.internal.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 车辆订单列表响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 21:20
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CarOrderListExportResp {

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String ordercode;

    /**
     * 确认号
     */
    @ExcelProperty("确认号")
    private String confirmcode;

    /**
     * 供应商
     */
    @ExcelProperty("供应商")
    private String vendorName;

    /**
     * 1->门店；2->网站；3->微信；4->淘宝；5->携程；6->租租车；7->惠租车；8->租租车ERC; 9->易途8；
     */
    @ExcelProperty("订单来源")
    private String source;

    /**
     * 来源订单号
     */
    @ExcelProperty("渠道订单号")
    private String sourceOrdercode;

    /**
     * 下单时间
     */
    @ExcelProperty("下单时间")
    private String time;

    /**
     * -3->拒单;-2->删除；-1->已取消；1->待付款；2->待确认；3->已确认,待取车；4->已取车,待还车；5->已还车，保养中；6->完成
     */
    @ExcelProperty("订单状态")
    private String status;

    /**
     * 付款时间
     */
    @ExcelProperty("付款时间")
    private String paytime;

    /**
     * 订单取消时间
     */
    @ExcelProperty("订单取消时间")
    private String canceltime;

    /**
     * 预约取车门店名称
     */
    @ExcelProperty("取车门店")
    private String appointGetstorename;

    /**
     * 租车开始时间
     */
    @ExcelProperty("取车时间")
    private String appointStarttime;

    /**
     * 预约还车门店名称
     */
    @ExcelProperty("还车门店")
    private String appointReturnstorename;

    /**
     * 租车结束时间
     */
    @ExcelProperty("还车时间")
    private String appointEndtime;

    /**
     * 预约天数
     */
    @ExcelProperty("预约天数")
    private Integer appointDays;

    /**
     * 车辆四字码
     */
    @ExcelProperty("车辆四字码")
    private String appointCarcode;

    /**
     * 车辆名称
     */
    @ExcelProperty("车型")
    private String appointCarname;

    /**
     * 1->男；2->女
     */
    @ExcelProperty("客户性别")
    private String sex;

    /**
     * 名
     */
    @ExcelProperty("客户姓名")
    private String username;

    /**
     * 国籍
     */
    @ExcelProperty("客户国籍")
    private String citizen_country_code;

    /**
     * 电话
     */
    @ExcelProperty("电话")
    private String mobile;

    /**
     * 用户邮箱
     */
    @ExcelProperty("用户邮箱")
    private String email;

    /**
     * 航班号
     */
    @ExcelProperty("航班号")
    private String contactline;

    /**
     * 车辆保险名称
     */
    @ExcelProperty("保险名称")
    private String stockInsTitle;

    /**
     * 总价
     */
    @ExcelProperty("总价")
    private String totalprice;

    /**
     * 货币单位，暂本地货币
     */
    @ExcelProperty("货币单位")
    private String localCurrency;

    /**
     * 1->支付宝；2->微信；3->信用卡；4->现金；5->携程；6->租租车；7->惠租车；8->租租车ERC；9->Booking;
     */
    @ExcelProperty("支付方式")
    private String paytype;

    @ExcelProperty("单程费")
    private String oneWayFee;

    /**
     * 保险价格
     */
    @ExcelProperty("保险价格")
    private String insuranceprice;

    /**
     * 订单取消应扣金额：1.取车（当地时间）前48小时可免费取消； 2、48小时内取消，将从车款中扣除2日租金
     */
    @ExcelProperty("订单取消应扣金额")
    private String cancelprice;

    /**
     * 额外费用
     */
    @ExcelProperty("额外费用")
    private String extraprice;

    /**
     * 其他费用
     */
    @ExcelProperty("其他费用")
    private String otherprice;

    /**
     * 正常营业可加班费用
     */
    @ExcelProperty("正常营业可加班费用")
    private String overtimecost;

    /**
     * 是否预付加班费用 1->是；0->否
     */
    @ExcelProperty("是否预付加班费用")
    private String overtimePrepay;

    /**
     * 异地还车费
     */
    @ExcelProperty("异地还车费")
    private String carreturnPrice;

    @ExcelProperty("预付金额（含加价）")
    private String prepaidAmount;

    @ExcelProperty("到付金额（含加价）")
    private String cashOnDeliveryAmount;

    @ExcelProperty("特殊取车时间费")
    private String specialPickupTimeFee;

    @ExcelProperty("特殊还车时间费")
    private String specialReturnTimeFee;

    @ExcelProperty("结算金额")
    private String settlementAmount;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String note;

    /**
     * 创建类型，0-手动，1-api，2-导入
     */
    @ExcelProperty("创建类型")
    private String createType;



    /**
     * 表 #__car_list
     */
//    @ExcelProperty("车牌号")
//    private String platecode;

//    /**
//     * 表 #__car_list
//     */
//    @ExcelProperty("车辆颜色")
//    private String color;
//
//    /**
//     * 表 #__car_list
//     */
//    @ExcelProperty("燃油")
//    private String fuelmodel;
//
//
//
//
//
//
//
//    /**
//     * 车辆图片
//     */
//    @ExcelProperty("车辆图片")
//    private String appointCarimg;
//
//    /**
//     * 车牌号码
//     */
//    @ExcelProperty("车辆号码")
//    private String appointPlatenumber;
//
//
//
//
//
//
//    /**
//     * 罚单收货地址
//     */
//    @ExcelProperty("罚单收货地址")
//    private String ticketAddress;
//
//
//
//    /**
//     * 下单ip
//     */
//    @ExcelProperty("下单ip")
//    private String ip;
//
//
//
//    /**
//     * 1->普通订单；2->闪租；3->无忧组
//     */
//    @ExcelProperty("订单类型")
//    private String ordertype;
//
//
//
//
//
////    /**
////     * 下单人
////     */
////    @ExcelProperty("下单人id")
////    private Integer mid;
//
//    /**
//     * 1->基本险；2->全额险
//     */
//    @ExcelProperty("保险")
//    private String insurance;
//
//    /**
//     * 车辆的保险 car_stock_insurance 表的id
//     */
////    @ExcelProperty("车辆的保险id")
////    private Integer stockInsId;
//
//
//
//
//
//    /**
//     * 姓
//     */
//    @ExcelProperty("姓")
//    private String surname;
//
//
//
//
//
//    /**
//     * 护照号
//     */
//    @ExcelProperty("护照号")
//    private String passportnum;
//
//    /**
//     * 颜色
//     */
////    @ExcelProperty("颜色")
////    private String appointCarcolor;
//
//
//
//    /**
//     * 日均价（元）
//     */
////    @ExcelProperty("日均价（元）")
////    private Double price;
//
//
//
//
//
//    /**
//     * 付款金额
//     */
//    @ExcelProperty("付款金额")
//    private BigDecimal payprice;
//
//    /**
//     * 当地货币总金额
//     */
//    @ExcelProperty("当地货币总金额")
//    private Double localPayprice;
//
//
//
//    /**
//     * 1->支付宝；2->微信；3->信用卡；4->现金；5->携程；6->租租车；7->惠租车；8->租租车ERC；9->Booking;
//     */
//    @ExcelProperty("支付方式")
//    private String paytype;
//
//    /**
//     * 押金
//     */
//    @ExcelProperty("押金")
//    private Double deposit;
//
//    /**
//     * 押金付款方式：1->支付宝；2->微信；3->信用卡；4->现金；5->"闪租"；6->"无忧租"; 7->"芝麻信誉";
//     */
//    @ExcelProperty("押金付款方式")
//    private String depositPaytype;
//
//    /**
//     * 订单确认时间
//     */
//    @ExcelProperty("订单确认时间")
//    private String confirmtime;
//
//    /**
//     * 订单确认ip
//     */
//    @ExcelProperty("订单确认ip")
//    private String confirmip;
//
////    /**
////     * 订单确认人
////     */
////    @ExcelProperty("订单确认人")
////    private Integer confirmmid;
//
//
//
//    /**
//     * 确认码
//     */
//    @ExcelProperty("确认码")
//    private String confirmcode;
//
//
//
//    /**
//     * 取车时间
//     */
//    @ExcelProperty("取车时间")
//    private String getcartime;
//
//    /**
//     * 点击确认取车时间
//     */
//    @ExcelProperty("点击确认取车时间")
//    private String realGetcartime;
//
//    /**
//     * 确认取车人
//     */
////    @ExcelProperty("确认取车人id")
////    private Integer getcarmid;
//
//    /**
//     * 实际取车#__car_list 的id
//     */
////    @ExcelProperty("实际取车id")
////    private Integer carid;
//
//    /**
//     * 实际取车 #__car_stock的id
//     */
////    @ExcelProperty("实际取车stock id")
////    private Integer stockid;
//
//    /**
//     * 实际取车 车辆名称
//     */
////    @ExcelProperty("实际取车车辆名称")
////    private String carname;
//
//    /**
//     * 实际取车  车辆图片
//     */
////    @ExcelProperty("实际取车车辆图片")
////    private String carimg;
//
//    /**
//     * 实际取车 车辆四字码
//     */
////    @ExcelProperty("实际取车车辆四字码")
////    private String carcode;
//
//    /**
//     * 实际取车 车辆颜色
//     */
////    @ExcelProperty("实际取车车辆颜色")
////    private String carcolor;
//
//    /**
//     * 实际取车 车牌号
//     */
////    @ExcelProperty("实际取车车牌号")
////    private String platenumber;
//
//    /**
//     * 还车时间
//     */
//    @ExcelProperty("还车时间")
//    private String returntime;
//
//    /**
//     * 点击还车时间
//     */
//    @ExcelProperty("点击还车时间")
//    private String realReturntime;
//
//    /**
//     * 还车确认人
//     */
////    @ExcelProperty("还车确认人")
////    private Integer returnmid;
//
//    /**
//     * 还车门店
//     */
////    @ExcelProperty("还车门店")
////    private Integer returnstoreid;
//
//    /**
//     * 还车门店名称
//     */
////    @ExcelProperty("还车门店名称")
////    private String returnstorename;
//
//    /**
//     * 车辆入库时间
//     */
////    @ExcelProperty("车辆入库时间")
////    private String orderEndtime;
//
//    /**
//     * 确认车辆入库人
//     */
////    @ExcelProperty("确认车辆入库人")
////    private Integer orderEndmid;
//
//    /**
//     * 本次行驶里程
//     */
////    @ExcelProperty("本次行驶里程")
////    private Double mileage;
//
//    /**
//     * 应退押金
//     */
////    @ExcelProperty("应退押金")
////    private Double returnDeposit;
////
////    /**
////     * 1->待退；2->已退
////     */
////    @ExcelProperty("退押金状态")
////    private String returnDepositStatus;
//
//    /**
//     * 押金退还时间
//     */
////    @ExcelProperty("押金退还时间")
////    private LocalDateTime returnDepositTime;
//
//    /**
//     * 退款人
//     */
////    @ExcelProperty("退款人")
////    private Integer returnDepositMid;
//
//
//    /**
//     * 取消人的mid
//     */
////    @ExcelProperty("取消人的mid")
////    private Integer cancelmid;
//
//    /**
//     * 订单取消应扣金额：1.取车（当地时间）前48小时可免费取消； 2、48小时内取消，将从车款中扣除2日租金
//     */
//    @ExcelProperty("订单取消应扣金额")
//    private Double cancelprice;
//
////    /**
////     * 上次修改时间
////     */
////    @ExcelProperty("上次修改时间")
////    private LocalDateTime edittime;
////
////    /**
////     * 上次修改人
////     */
////    @ExcelProperty("上次修改时间")
////    private Integer editmid;
////
////    /**
////     * 删除时间
////     */
////    private LocalDateTime deltime;
////
////    /**
////     * 删除人
////     */
////    private Integer delmid;
//
//
//
//    /**
//     * 额外备注
//     */
//    @ExcelProperty("额外备注")
//    private String extranote;
//
//    /**
//     * 1->普通单 2->芝麻单
//     */
////    @ExcelProperty("订单是否芝麻单")
////    private Integer sesame;
//
//    /**
//     * 芝麻信誉分数
//     */
////    @ExcelProperty("芝麻信誉分数")
////    private Integer grade;
//
//
//
//    /**
//     * 其他费用备注
//     */
//    @ExcelProperty("其他费用备注")
//    private String othernote;
//
//    /**
//     * 驾照图片
//     */
//    @ExcelProperty("驾照图片")
//    private String licensePic;
//
////    /**
////     * 工作人员确认取消状态码  0->代表未确认  1->已确认
////     */
////    @ExcelProperty("工作人员确认取消状态码")
////    private Integer canceledstatus;
//
//    /**
//     * 确认取消人
//     */
////    @ExcelProperty("确认取消人")
////    private Integer canceledmid;
//
//    /**
//     * 折扣价当地货币
//     */
////    @ExcelProperty("折扣价当地货币")
////    private Double discountprice;
////
////    /**
////     * 记录每笔订单单日里程限制
////     */
////    @ExcelProperty("单日里程限制")
////    private Double daymileage;
////
////    /**
////     * 近日任务备注
////     */
////    @ExcelProperty("近日任务备注")
////    private String morenote;
//
//    /**
//     * 拒单时间
//     */
////    @ExcelPropety("拒单时间")
////    private LocalDateTime refusetime;
////
////    /**
////     * 拒单人的ID
////     */
////    @ExcelProperty("拒单人的ID")
////    private Integer refusemid;
//
//    /**
//     * 儿童座椅数量
//     */
////    @ExcelProperty("儿童座椅数量")
////    private String childseat;
//
//    /**
//     * 儿童座椅收取费用
//     */
////    @ExcelProperty("儿童座椅收取费用")
////    private Double childseatprice;
//
//    /**
//     * 是否预付儿童座椅 1->是；0->否
//     */
////    @ExcelProperty("是否预付儿童座椅")
////    private String childseatPrepay;
//
//
//
//
//
//
//    /**
//     * 携程回传价格
//     */
////    @ExcelProperty("携程回传价格")
////    private Double ctripprice;
//
//    /**
//     * 区分无忧租下面的全险和非权限0->非全险   1->全险
//     */
////    @ExcelProperty("携程回传价格")
////    private Integer ordertypes;
//
//    /**
//     * 租租车会员优惠码
//     */
////    private String discountCode;
//
//    /**
//     * 信誉卡卡号
//     */
////    private String cardnumber;
//
//    /**
//     * 信誉卡的有效年限
//     */
////    private String termofvalidity;
//
//    /**
//     * cvv码
//     */
////    private Integer cvvnumbber;
//
//    /**
//     * 下单时记录的汇率
//     */
////    private Double exchangeRate;
//
//    /**
//     * 租租车会员对应的日租金
//     */
////    private Double discountDayprice;
//
//    /**
//     * 货币单位
//     */
//    @ExcelProperty("货币单位")
//    private String unit;
//
//    /**
//     * 是否是早鸟套餐 0->否；1->是
//     */
////    private Integer isRate;
//
//    /**
//     * 早鸟套餐码
//     */
////    private String rateCode;
//
//    /**
//     * 排班表id
//     */
////    private Long orderRangeId;
//
//    /**
//     * 创建类型，0-手动，1-api，2-导入
//     */
//    @ExcelProperty("创建类型")
//    private String createType;
//
//    @ExcelProperty("预付金额（含加价）")
//    private BigDecimal prepaidAmount;
//    @ExcelProperty("到付金额（含加价）")
//    private BigDecimal cashOnDeliveryAmount;
//
//    @ExcelProperty("特殊取车时间费")
//    private BigDecimal specialPickupTimeFee;
//    @ExcelProperty("特殊还车时间费")
//    private BigDecimal specialReturnTimeFee;
//    @ExcelProperty("平台险购买状态")
//    private String buyInsurance;
//    @ExcelProperty("结算金额")
//    private BigDecimal settlementAmount;

}
