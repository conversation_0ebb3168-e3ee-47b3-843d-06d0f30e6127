package com.ixtech.management.integration.internal.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 车辆订单详情响应Resp
 *
 * <AUTHOR> hu
 * @date 2025/4/4 21:21
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CarOrderInfoResp {

    // 基础属性
    /**
     * 订单id
     */
    private Long id;

    /**
     * 订单号
     */
    private String sourceOrdercode;

    /**
     * 订单状态 -3->拒单;-2->删除；-1->已取消；1->待付款；2->待确认；3->已确认,待取车；4->已取车,待还车；5->已还车，保养中；6->完成
     */
    private Integer status;

    /**
     * 订单状态（字符串描述）
     */
    private String statusStr;

    /**
     * 订单来源 1->门店；2->网站；3->微信；4->淘宝；5->携程；6->租租车；7->惠租车；8->租租车ERC; 9->易途8；
     */
    private Integer source;

    /**
     * 订单来源（字符串描述）
     */
    private String sourceStr;

    /**
     * 订单创建方式
     */
    private Integer createType;

    /**
     * 订单创建方式（字符串描述）
     */
    private String createTypeStr;

    /**
     * 订单创建时间
     */
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private String time;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店名称
     */
    private String storeName;


    private String cancelTime;

    private String cancelprice;


    // 车型信息
    /**
     * 下单车型
     */
    private String orderedCarModel;

    /**
     * 实排车型
     */
    private String actualCarModel;

    // 取车信息
    /**
     * 预计取车时间
     */
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private String appointStarttime;

    /**
     * 取车门店
     */
    private String appointGetstorename;

    private Long appointGetstoreid;

    /**
     * 取车地址
     */
    private String pickupAddress;

    /**
     * 取车方式
     */
    private String pickupMethod;

    /**
     * 取车油量百分比
     */
    private BigDecimal pickupFuelLevel;

    /**
     * 取车里程
     */
    private String pickupMileage;

    // 还车信息
    /**
     * 预计还车时间
     */
//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private String appointEndtime;

    /**
     * 还车门店
     */
    private String appointReturnstorename;

    private Long appointReturnstoreid;

    /**
     * 还车地址
     */
    private String returnAddress;

    /**
     * 还车方式
     */
    private String returnMethod;

    /**
     * 还车油量百分比
     */
    private BigDecimal returnFuelLevel;

    /**
     * 还车里程
     */
    private String returnMileage;

    // 用车时长
    /**
     * 预计用车时长
     */
    private String expectedUsageDuration;

    /**
     * 实际用车时长
     */
    private String actualUsageDuration;

    /**
     * 油量差值
     */
    private BigDecimal fuelDifference;

    /**
     * 里程差值
     */
    private String mileageDifference;


    /**
     * 租赁总天数
     */
    private Integer appointDays;


    // 保险信息
    /**
     * 平台险购买状态 true：是 false：否
     */
    private Integer buyInsurance;

    /**
     * 车辆的保险id
     */
    private Long stockInsId;

    private Long appointStockid;

    private Long appointCarid;

    /**
     * 车辆保险名称
     */
    private String stockInsTitle;


    /**
     * 备注
     */
    private String note;

    private BigDecimal totalprice;

    private String unit;


    // 订单金额（预定）
    /**
     * 订单金额（预定）列表
     */
    private List<CarOrderAmountResp> orderAmountList;

    /**
     * 单程费
     */
    private BigDecimal oneWayFee;

    private BigDecimal localPayprice;

    private String localCurrency;
//
//    /**
//     * 单程费货币
//     */
//    private String oneWayFeeCurrency;
//
//    /**
//     * 单程费（字符串描述）
//     */
//    private String oneWayFeeStr;
//
//
    /**
     * 特殊取车时间费
     */
    private BigDecimal specialPickupTimeFee;
//
//    /**
//     * 特殊取车时间费货币
//     */
//    private String specialPickupTimeFeeCurrency;
//
//    /**
//     * 特殊取车时间费（字符串描述）
//     */
//    private String specialPickupTimeFeeStr;
//
//
    /**
     * 特殊还车时间费
     */
    private BigDecimal specialReturnTimeFee;
//
//    /**
//     * 特殊还车时间费货币
//     */
//    private BigDecimal specialReturnTimeFeeCurrency;
//
//    /**
//     * 特殊还车时间费（字符串描述）
//     */
//    private String specialReturnTimeFeeStr;
//
//
    /**
     * 预付金额（含加价）
     */
    private BigDecimal prepaidAmount;
//
//    /**
//     * 预付金额（含加价）货币
//     */
//    private String prepaidAmountCurrency;
//
//    /**
//     * 预付金额（含加价）（字符串描述）
//     */
//    private String prepaidAmountStr;
//
//
    /**
     * 到付金额（含加价）
     */
    private BigDecimal cashOnDeliveryAmount;

//    private Integer buyPlatformInsurance;

    private BigDecimal settlementAmount;
//
//    /**
//     * 到付金额（含加价）货币
//     */
//    private String cashOnDeliveryAmountCurrency;
//
//    /**
//     * 到付金额（含加价）（字符串描述）
//     */
//    private String cashOnDeliveryAmountStr;
//
//
//    /**
//     * 总金额（含加价）
//     */
//    private BigDecimal totalAmount;
//
//    /**
//     * 总金额（含加价）货币
//     */
//    private String totalAmountCurrency;
//
//    /**
//     * 总金额（含加价）（字符串描述）
//     */
//    private String totalAmountStr;
//
//
    /**
     * 支付方式
     */
    private String paymentMethod;

    private Integer paytype;
//
//    /**
//     * 支付方式（字符串描述）
//     */
//    private String paymentMethodStr;
//

    // 订单金额（结算）
    /**
     * 订单金额（结算）列表
     */
    private List<CarOrderSettlementAmountResp> settlementAmountList;

    private List<Customer> customerList;

    @Data
    public static class Customer {
        /**
         * 客户姓名
         */
        private String customerName;

        /**
         * 客户联系方式
         */
        private String customerMobile;

        /**
         * 客户电子邮箱
         */
        private String customerEmail;

        /**
         * 客户国籍
         */
        private String customerNationality;

        /**
         * 航班号
         */
        private String flightNumber;
    }

}
