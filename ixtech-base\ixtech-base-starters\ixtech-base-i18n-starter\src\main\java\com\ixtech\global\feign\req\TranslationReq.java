package com.ixtech.global.feign.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR> hu
 * @date 2025/6/10 14:30
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TranslationReq {

    /**
     * 翻译资源key列表
     */
    private Collection<String> resourceKeys;

    /**
     * 语言code
     */
    private String languageCode;

}
