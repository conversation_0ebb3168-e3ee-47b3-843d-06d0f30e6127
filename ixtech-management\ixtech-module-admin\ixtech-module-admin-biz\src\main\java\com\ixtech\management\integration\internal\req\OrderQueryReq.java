package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订单查询 请求对象
 * 用于接收订单相关的请求数据
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
public class OrderQueryReq {

    /**
     * 来源平台
     * 表示订单的来源，必填字段
     */
    @NotNull(message = "来源平台不能为空")
    @JsonProperty("source")
    private Integer source;

    /**
     * 来源平台订单号
     * 来源平台的唯一订单标识，必填字段
     */
    @NotBlank(message = "来源平台订单号不能为空")
    @JsonProperty("source_order_id")
    private String sourceOrderId;

    /**
     * IX平台订单号
     * IX平台的订单标识，非必填字段
     */
    @JsonProperty("ix_order_code")
    private String ixOrderCode;

    /**
     * IX平台确认号
     * IX平台的确认标识，非必填字段
     */
    @JsonProperty("ix_confirm_code")
    private String ixConfirmCode;
}
