apiVersion: apps/v1
kind: Deployment
metadata:
  name: management-service
  namespace: default
  labels:
    app: management-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: management-service
  template:
    metadata:
      labels:
        app: management-service
    spec:
      containers:
        - name: management-service
          image: ${IMAGE}
          ports:
            - containerPort: 8080
#        resources:
#          limits:
#            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: management-service
  namespace: default
  labels:
    app: management-service
spec:
  selector:
    app: management-service
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 8080
    - name: https
      protocol: TCP
      port: 443
      targetPort: 8080
#  type: NodePort
