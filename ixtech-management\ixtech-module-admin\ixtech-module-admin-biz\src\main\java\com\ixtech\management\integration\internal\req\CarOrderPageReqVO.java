package com.ixtech.management.integration.internal.req;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 车辆订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CarOrderPageReqVO extends PageParam {

    @Schema(description = "预约取车门店  表 #__car_stock 的id", example = "11752")
    private Integer appointGetstoreid;

    @Schema(description = "预约还车门店  #__store的id", example = "7143")
    private Integer appointReturnstoreid;


}