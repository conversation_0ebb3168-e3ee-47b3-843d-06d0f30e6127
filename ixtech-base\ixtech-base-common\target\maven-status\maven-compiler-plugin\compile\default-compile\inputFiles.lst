C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\LocalDateTimeUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\LanguageEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\CurrencyEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\StoreLocationTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\annotation\ValidEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\MarkTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\DateTimeUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\RentUnitEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\StoreStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\exception\ApiException.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\exception\inf\ErrorCode.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\filter\ChannelContextWebFilter.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\SnapshotVersionEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\FuelPolicyEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferMessageSendStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\valid\EnumValueValidator.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\filter\VendorContextWebFilter.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\IxChannelInfo.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\FileTaskStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\inf\ScheduleInf.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\StoreOperationTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferOrderStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\RangeUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferOrderChargeTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\exception\ServerException.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\serialize\TimestampToLocalDateTimeDeserializer.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\context\ChannelContextHolder.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\RentalOrderStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\ScheduleTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferMessageSendTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\CollUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\Md5Utils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferOrderContactTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\TransportationMethodEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\constant\IxtechConstants.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\PriceTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\FulfillmentStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferOperatorTerminalEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TranferVehicleStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferPaymentTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\exception\handler\GlobalExceptionHandler.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\inf\RangeInterval.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\EnumFillType.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\OrderFulfillmentTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\StoreChargeMethodEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferServiceTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\valid\Update.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\listener\validator\ApplicationStartValidator.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\VehicleOnlineStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\config\IdGeneratorProperties.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\context\SpringContextHolder.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\BeanUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\DataDigestUtil.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferEstimatedTimeUnitEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\RentalPaymentStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferEstimatedMileageUnitEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\InsuranceStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\JsonUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\DocumentTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TranferVehicleColorEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferContactTargetTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\SaleStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\TransmissionEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\AES256Utils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\FileUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\ObfuscationUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\VehicleAttributionEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\FreeDelayReturnTimeUnitEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\SupportStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\InsuranceTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\PlatformOrderStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\RangeReserveStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferDispatchStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\StrUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\RentalInsuranceTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\OrderChargeTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\PageRequest.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\EnabledStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferSourceCodeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\DayOfWeekEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferVehicleOwnerEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\ApiResponse.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\MileageUnitEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\FeiShuBotUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferDispatchTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\DrivingLicenseTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\VehicleConditionEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\OrderLogOperationTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\context\VendorContextHolder.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\PageResponse.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\serialize\JacksonHttpMessageConverter.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\DocumentStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\RangeResult.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\exception\handler\ServerExceptionHandler.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\SiteTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\VendorInfo.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\ChannelCooperationStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\interceptor\RequestLogInterceptor.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\utils\ExcelUtils.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\context\OnlineContext.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\exception\enums\CommonErrorCode.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\CountryEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\serialize\LocalDateTimeToTimestampSerializer.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\config\IdGeneratorConfig.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\StoreTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\interceptor\UserInitInterceptor.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\biz\rental\constant\NormalConstant.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\FuelTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\InsuranceChildTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\biz\rental\bo\ReferenceIdBO.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\listener\event\ApplicationStartCheckEvent.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\VehicleDesignModelEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\ShuttleTimeUnitEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\inf\DictInf.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferNotifyStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\ErrorData.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferAmenityServiceTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\ChargePriceTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\CancellerTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\VehicleInstantConfirmEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\dto\TimeInterval.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\ChannelBrandEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\context\DictConfiguration.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\biz\rental\utils\ReferenceIdUtil.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\SpecialEquipPricingMethodEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\valid\Create.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\annotation\JacksonFill.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferOrderTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferOrderCreateTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\transfer\TransferPaymentStatusEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\OrderTypeEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\PayMethodEnum.java
C:\Users\<USER>\projectsBE\ixtech-base\ixtech-base-common\src\main\java\com\ixtech\global\common\enums\RentalPaymentMethodEnum.java
