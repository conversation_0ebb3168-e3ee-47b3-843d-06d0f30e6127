package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.ixtech.global.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商搜索 req
 *
 * @author: Phili
 * @date： 2025/3/31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VendorSearchReq extends PageRequest {

    private static final long serialVersionUID = -4381075801048303456L;

    /**
     * 供应商
     */
    private Vendor vendor;


    @Data
    public static class Vendor {

        /**
         * 供应商代码
         */
        private String code;

        /**
         * 供应商名称
         */
        private String companyName;
    }
}
