package com.ixtech.global.annotation;

import com.ixtech.global.constant.I18nConstants;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 翻译字段注解，用于指定要翻译的目标字段
 *
 * <AUTHOR> hu
 * @date 2025/6/10 10:34
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface I18nField {

    /**
     * 指定要翻译的目标字段名
     * 默认为空，表示当前注解所在字段是需要翻译的字段
     */
    String link() default I18nConstants.EMPTY;

    /**
     * 翻译资源key
     * 默认为空，表示使用当前注解所在的字段的值作为翻译资源key
     */
    String resourceKey() default I18nConstants.EMPTY;

}
