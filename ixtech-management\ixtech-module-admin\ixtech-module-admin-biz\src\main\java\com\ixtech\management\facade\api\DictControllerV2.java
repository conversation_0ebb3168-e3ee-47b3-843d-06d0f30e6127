package com.ixtech.management.facade.api;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.domain.service.DictServiceV2;
import com.ixtech.management.integration.internal.req.DictTypeListReq;
import com.ixtech.management.integration.internal.resp.DictTypeListResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 字典接口
 */
@Tag(name = "字典管理", description = "字典管理接口")
@RestController
@RequestMapping("/v2/managementsrv/api/dict")
public class DictControllerV2 {

    @Resource
    private DictServiceV2 dictServiceV2;


    /**
     * 根据字典类型列表获取字典
     *
     * @param req 包含字典类型列表的请求
     * @return 字典数据
     */
    @PostMapping("/list")
    public ApiResponse<DictTypeListResp> dictList(@RequestBody DictTypeListReq req) {
        return dictServiceV2.getDictByTypeList(req);
    }
}