package com.ixtech.management.repo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Database Table Remarks:
 *   车辆表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table jipinzuche_car_model
 */
public class CarModelPO implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   car_brand的id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.brandid
     *
     * @mbg.generated
     */
    private Long brandid;

    /**
     * Database Column Remarks:
     *   车辆图片
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.litpic
     *
     * @mbg.generated
     */
    private String litpic;

    /**
     * Database Column Remarks:
     *   车型名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     * Database Column Remarks:
     *   车辆四字码
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.carcode
     *
     * @mbg.generated
     */
    private String carcode;

    /**
     * Database Column Remarks:
     *   座位数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.seat
     *
     * @mbg.generated
     */
    private Integer seat;

    /**
     * Database Column Remarks:
     *   车门数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.door
     *
     * @mbg.generated
     */
    private Integer door;

    /**
     * Database Column Remarks:
     *   行李数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.luggage
     *
     * @mbg.generated
     */
    private Integer luggage;

    /**
     * Database Column Remarks:
     *   1->自动挡；2->手动挡
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.transmission
     *
     * @mbg.generated
     */
    private Integer transmission;

    /**
     * Database Column Remarks:
     *   添加时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.time
     *
     * @mbg.generated
     */
    private Long time;

    /**
     * Database Column Remarks:
     *   添加ip
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.ip
     *
     * @mbg.generated
     */
    private String ip;

    /**
     * Database Column Remarks:
     *   添加人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.mid
     *
     * @mbg.generated
     */
    private Long mid;

    /**
     * Database Column Remarks:
     *   1->有效；-1->删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     * Database Column Remarks:
     *   车辆成本价
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.dayprice
     *
     * @mbg.generated
     */
    private BigDecimal dayprice;

    /**
     * Database Column Remarks:
     *   create_time in UTC
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     * Database Column Remarks:
     *   update_time in UTC
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     * Database Column Remarks:
     *   active
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.active
     *
     * @mbg.generated
     */
    private Boolean active;

    /**
     * Database Column Remarks:
     *   deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column jipinzuche_car_model.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table jipinzuche_car_model
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.id
     *
     * @return the value of jipinzuche_car_model.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.id
     *
     * @param id the value for jipinzuche_car_model.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.brandid
     *
     * @return the value of jipinzuche_car_model.brandid
     *
     * @mbg.generated
     */
    public Long getBrandid() {
        return brandid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.brandid
     *
     * @param brandid the value for jipinzuche_car_model.brandid
     *
     * @mbg.generated
     */
    public void setBrandid(Long brandid) {
        this.brandid = brandid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.litpic
     *
     * @return the value of jipinzuche_car_model.litpic
     *
     * @mbg.generated
     */
    public String getLitpic() {
        return litpic;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.litpic
     *
     * @param litpic the value for jipinzuche_car_model.litpic
     *
     * @mbg.generated
     */
    public void setLitpic(String litpic) {
        this.litpic = litpic == null ? null : litpic.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.name
     *
     * @return the value of jipinzuche_car_model.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.name
     *
     * @param name the value for jipinzuche_car_model.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.carcode
     *
     * @return the value of jipinzuche_car_model.carcode
     *
     * @mbg.generated
     */
    public String getCarcode() {
        return carcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.carcode
     *
     * @param carcode the value for jipinzuche_car_model.carcode
     *
     * @mbg.generated
     */
    public void setCarcode(String carcode) {
        this.carcode = carcode == null ? null : carcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.seat
     *
     * @return the value of jipinzuche_car_model.seat
     *
     * @mbg.generated
     */
    public Integer getSeat() {
        return seat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.seat
     *
     * @param seat the value for jipinzuche_car_model.seat
     *
     * @mbg.generated
     */
    public void setSeat(Integer seat) {
        this.seat = seat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.door
     *
     * @return the value of jipinzuche_car_model.door
     *
     * @mbg.generated
     */
    public Integer getDoor() {
        return door;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.door
     *
     * @param door the value for jipinzuche_car_model.door
     *
     * @mbg.generated
     */
    public void setDoor(Integer door) {
        this.door = door;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.luggage
     *
     * @return the value of jipinzuche_car_model.luggage
     *
     * @mbg.generated
     */
    public Integer getLuggage() {
        return luggage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.luggage
     *
     * @param luggage the value for jipinzuche_car_model.luggage
     *
     * @mbg.generated
     */
    public void setLuggage(Integer luggage) {
        this.luggage = luggage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.transmission
     *
     * @return the value of jipinzuche_car_model.transmission
     *
     * @mbg.generated
     */
    public Integer getTransmission() {
        return transmission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.transmission
     *
     * @param transmission the value for jipinzuche_car_model.transmission
     *
     * @mbg.generated
     */
    public void setTransmission(Integer transmission) {
        this.transmission = transmission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.time
     *
     * @return the value of jipinzuche_car_model.time
     *
     * @mbg.generated
     */
    public Long getTime() {
        return time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.time
     *
     * @param time the value for jipinzuche_car_model.time
     *
     * @mbg.generated
     */
    public void setTime(Long time) {
        this.time = time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.ip
     *
     * @return the value of jipinzuche_car_model.ip
     *
     * @mbg.generated
     */
    public String getIp() {
        return ip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.ip
     *
     * @param ip the value for jipinzuche_car_model.ip
     *
     * @mbg.generated
     */
    public void setIp(String ip) {
        this.ip = ip == null ? null : ip.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.mid
     *
     * @return the value of jipinzuche_car_model.mid
     *
     * @mbg.generated
     */
    public Long getMid() {
        return mid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.mid
     *
     * @param mid the value for jipinzuche_car_model.mid
     *
     * @mbg.generated
     */
    public void setMid(Long mid) {
        this.mid = mid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.status
     *
     * @return the value of jipinzuche_car_model.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.status
     *
     * @param status the value for jipinzuche_car_model.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.dayprice
     *
     * @return the value of jipinzuche_car_model.dayprice
     *
     * @mbg.generated
     */
    public BigDecimal getDayprice() {
        return dayprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.dayprice
     *
     * @param dayprice the value for jipinzuche_car_model.dayprice
     *
     * @mbg.generated
     */
    public void setDayprice(BigDecimal dayprice) {
        this.dayprice = dayprice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.create_time
     *
     * @return the value of jipinzuche_car_model.create_time
     *
     * @mbg.generated
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.create_time
     *
     * @param createTime the value for jipinzuche_car_model.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.update_time
     *
     * @return the value of jipinzuche_car_model.update_time
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.update_time
     *
     * @param updateTime the value for jipinzuche_car_model.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.active
     *
     * @return the value of jipinzuche_car_model.active
     *
     * @mbg.generated
     */
    public Boolean getActive() {
        return active;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.active
     *
     * @param active the value for jipinzuche_car_model.active
     *
     * @mbg.generated
     */
    public void setActive(Boolean active) {
        this.active = active;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column jipinzuche_car_model.deleted
     *
     * @return the value of jipinzuche_car_model.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column jipinzuche_car_model.deleted
     *
     * @param deleted the value for jipinzuche_car_model.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}