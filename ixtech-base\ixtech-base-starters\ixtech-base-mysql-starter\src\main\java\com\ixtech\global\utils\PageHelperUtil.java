package com.ixtech.global.utils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ixtech.global.common.dto.PageRequest;
import com.ixtech.global.common.dto.PageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.function.Supplier;

/**
 * PageHelper 分页工具类
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public class PageHelperUtil {

    private static final Logger logger = LoggerFactory.getLogger(PageHelperUtil.class);

    /**
     * 执行分页查询并返回标准分页结果
     *
     * @param pageRequest   页码（从 1 开始）
     * @param querySupplier 查询逻辑，提供 List<T> 结果
     * @param <T>           实体类型
     * @return 分页结果，包含 list 和 total
     * @throws IllegalArgumentException 如果 pageNum 或 pageSize 无效
     */
    public static <T> PageResponse<T> executePageQuery(PageRequest pageRequest, Supplier<List<T>> querySupplier) {
        int pageNum = pageRequest.getPageNum();
        int pageSize = pageRequest.getPageSize();
        // 参数校验
        if (pageNum < 1) {
            logger.error("Invalid pageNum: {}", pageNum);
            throw new IllegalArgumentException("pageNum must be positive");
        }
        if (pageSize < 1) {
            logger.error("Invalid pageSize: {}", pageSize);
            throw new IllegalArgumentException("pageSize must be positive");
        }

        logger.info("Executing page query: pageNum={}, pageSize={}", pageNum, pageSize);

        // 设置分页
        PageHelper.startPage(pageNum, pageSize);

        try {
            // 执行查询
            List<T> list = querySupplier.get();

            // 获取分页信息
            PageInfo<T> pageInfo = new PageInfo<>(list);

            // 返回结果
            return PageResponse.success(pageInfo.getList(), pageInfo.getTotal(), pageRequest.getPageNum());
        } finally {
            // 清理 ThreadLocal，防止内存泄漏
            PageHelper.clearPage();
        }
    }
}
