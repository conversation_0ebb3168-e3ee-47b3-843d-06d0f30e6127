<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.CarModelMapper">
    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.CarModelPO">
        <!--@mbg.generated-->
        <!--@Table jipinzuche_car_model-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="active" jdbcType="BIT" property="active"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="brandid" jdbcType="INTEGER" property="brandid"/>
        <result column="litpic" jdbcType="VARCHAR" property="litpic"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="carcode" jdbcType="VARCHAR" property="carcode"/>
        <result column="seat" jdbcType="INTEGER" property="seat"/>
        <result column="door" jdbcType="INTEGER" property="door"/>
        <result column="luggage" jdbcType="INTEGER" property="luggage"/>
        <result column="transmission" jdbcType="INTEGER" property="transmission"/>
        <result column="time" jdbcType="INTEGER" property="time"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="mid" jdbcType="INTEGER" property="mid"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="dayprice" jdbcType="INTEGER" property="dayprice"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, active, deleted, create_time, update_time, brandid, litpic, `name`, carcode,
        seat, door, luggage, transmission, `time`, ip, mid, `status`, dayprice
    </sql>
    <sql id="Ignore_Deleted">
        status != -1
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from jipinzuche_car_model
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.ixtech.management.repo.entity.CarModelPO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_car_model (active, deleted, create_time,
        update_time, brandid, litpic,
        `name`, carcode, seat,
        door, luggage, transmission,
        `time`, ip, mid, `status`,
        dayprice)
        values (#{active,jdbcType=BIT}, #{deleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{brandid,jdbcType=INTEGER}, #{litpic,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR}, #{carcode,jdbcType=VARCHAR}, #{seat,jdbcType=INTEGER},
        #{door,jdbcType=INTEGER}, #{luggage,jdbcType=INTEGER}, #{transmission,jdbcType=INTEGER},
        #{time,jdbcType=INTEGER}, #{ip,jdbcType=VARCHAR}, #{mid,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
        #{dayprice,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.ixtech.management.repo.entity.CarModelPO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_car_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="active != null">
                active,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="brandid != null">
                brandid,
            </if>
            <if test="litpic != null">
                litpic,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="carcode != null">
                carcode,
            </if>
            <if test="seat != null">
                seat,
            </if>
            <if test="door != null">
                door,
            </if>
            <if test="luggage != null">
                luggage,
            </if>
            <if test="transmission != null">
                transmission,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="mid != null">
                mid,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="dayprice != null">
                dayprice,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="active != null">
                #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="brandid != null">
                #{brandid,jdbcType=INTEGER},
            </if>
            <if test="litpic != null">
                #{litpic,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="carcode != null">
                #{carcode,jdbcType=VARCHAR},
            </if>
            <if test="seat != null">
                #{seat,jdbcType=INTEGER},
            </if>
            <if test="door != null">
                #{door,jdbcType=INTEGER},
            </if>
            <if test="luggage != null">
                #{luggage,jdbcType=INTEGER},
            </if>
            <if test="transmission != null">
                #{transmission,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                #{time,jdbcType=INTEGER},
            </if>
            <if test="ip != null">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="mid != null">
                #{mid,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="dayprice != null">
                #{dayprice,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ixtech.management.repo.entity.CarModelPO">
        <!--@mbg.generated-->
        update jipinzuche_car_model
        <set>
            <if test="active != null">
                active = #{active,jdbcType=BIT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="brandid != null">
                brandid = #{brandid,jdbcType=INTEGER},
            </if>
            <if test="litpic != null">
                litpic = #{litpic,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="carcode != null">
                carcode = #{carcode,jdbcType=VARCHAR},
            </if>
            <if test="seat != null">
                seat = #{seat,jdbcType=INTEGER},
            </if>
            <if test="door != null">
                door = #{door,jdbcType=INTEGER},
            </if>
            <if test="luggage != null">
                luggage = #{luggage,jdbcType=INTEGER},
            </if>
            <if test="transmission != null">
                transmission = #{transmission,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=INTEGER},
            </if>
            <if test="ip != null">
                ip = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="mid != null">
                mid = #{mid,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="dayprice != null">
                dayprice = #{dayprice,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ixtech.management.repo.entity.CarModelPO">
        <!--@mbg.generated-->
        update jipinzuche_car_model
        set active = #{active,jdbcType=BIT},
        deleted = #{deleted,jdbcType=BIT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        brandid = #{brandid,jdbcType=INTEGER},
        litpic = #{litpic,jdbcType=VARCHAR},
        `name` = #{name,jdbcType=VARCHAR},
        carcode = #{carcode,jdbcType=VARCHAR},
        seat = #{seat,jdbcType=INTEGER},
        door = #{door,jdbcType=INTEGER},
        luggage = #{luggage,jdbcType=INTEGER},
        transmission = #{transmission,jdbcType=INTEGER},
        `time` = #{time,jdbcType=INTEGER},
        ip = #{ip,jdbcType=VARCHAR},
        mid = #{mid,jdbcType=INTEGER},
        `status` = #{status,jdbcType=INTEGER},
        dayprice = #{dayprice,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update jipinzuche_car_model
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="brandid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.brandid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="litpic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.litpic,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carcode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.carcode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="seat = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.seat,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="door = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.door,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="luggage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.luggage,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="transmission = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.transmission,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="`time` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.time,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.ip,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.mid,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="dayprice = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.dayprice,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update jipinzuche_car_model
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="active = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.active != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.active,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deleted != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.deleted,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="brandid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.brandid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.brandid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="litpic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.litpic != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.litpic,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="carcode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.carcode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.carcode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="seat = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.seat != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.seat,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="door = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.door != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.door,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="luggage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.luggage != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.luggage,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transmission = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.transmission != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.transmission,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`time` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.time != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.time,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ip = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ip != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.ip,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.mid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dayprice = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dayprice != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.dayprice,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into jipinzuche_car_model
        (active, deleted, create_time, update_time, brandid, litpic, `name`, carcode, seat,
        door, luggage, transmission, `time`, ip, mid, `status`, dayprice)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.active,jdbcType=BIT}, #{item.deleted,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.brandid,jdbcType=INTEGER}, #{item.litpic,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR}, #{item.carcode,jdbcType=VARCHAR}, #{item.seat,jdbcType=INTEGER},
            #{item.door,jdbcType=INTEGER}, #{item.luggage,jdbcType=INTEGER}, #{item.transmission,jdbcType=INTEGER},
            #{item.time,jdbcType=INTEGER}, #{item.ip,jdbcType=VARCHAR}, #{item.mid,jdbcType=INTEGER},
            #{item.status,jdbcType=INTEGER}, #{item.dayprice,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="getCarModelsByStoreId" resultMap="BaseResultMap">

        SELECT
        DISTINCT cm.id, cm.name, cm.brandid
        FROM jipinzuche_car_model cm
        INNER JOIN jipinzuche_car_stock cs ON cs.modelid = cm.id
        INNER JOIN jipinzuche_car_list cl ON cl.stockid = cs.id
        <where>
            cs.storeid = #{storeId,jdbcType=INTEGER}
            AND cm.
            <include refid="Ignore_Deleted"/>
            AND cs.
            <include refid="Ignore_Deleted"/>
            AND cl.
            <include refid="Ignore_Deleted"/>
        </where>

    </select>

</mapper>