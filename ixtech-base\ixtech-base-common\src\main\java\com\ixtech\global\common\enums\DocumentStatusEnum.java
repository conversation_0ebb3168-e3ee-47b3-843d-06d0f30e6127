package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.N;


@Getter
@AllArgsConstructor
public enum DocumentStatusEnum implements DictInf {

    UNASSIGN(1, "待分配"),
    ASSIGNED(2, "已分配"),
    PICKUP(3, "已取车"),
    RESERVE(4, "预占订单"),
    THIRD_PARTY(5, "第三方车辆"),
    MAINTAIN(6, "维修保养"),
    COMPLETED(7, "完成"),
    ;

    /**
     * 状态值
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String label;

    @Override
    public String getValue() {
        return String.valueOf(code);
    }
}
