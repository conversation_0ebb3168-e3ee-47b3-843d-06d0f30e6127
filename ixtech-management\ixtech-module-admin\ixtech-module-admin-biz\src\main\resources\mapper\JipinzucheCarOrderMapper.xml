<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ixtech.management.repo.mapper.JipinzucheCarOrderMapper">

    <resultMap id="BaseResultMap" type="com.ixtech.management.repo.entity.JipinzucheCarOrder">
        <!--@Table jipinzuche_car_order-->
        <result property="id" column="id"/>
        <result property="ordercode" column="ordercode"/>
        <result property="appointCarid" column="appoint_carid"/>
        <result property="appointStockid" column="appoint_stockid"/>
        <result property="appointGetstoreid" column="appoint_getstoreid"/>
        <result property="appointReturnstoreid" column="appoint_returnstoreid"/>
        <result property="appointGetstorename" column="appoint_getstorename"/>
        <result property="appointReturnstorename" column="appoint_returnstorename"/>
        <result property="appointCarname" column="appoint_carname"/>
        <result property="appointCarimg" column="appoint_carimg"/>
        <result property="appointPlatenumber" column="appoint_platenumber"/>
        <result property="appointStarttime" column="appoint_starttime"/>
        <result property="appointEndtime" column="appoint_endtime"/>
        <result property="appointDays" column="appoint_days"/>
        <result property="ticketAddress" column="ticket_address"/>
        <result property="time" column="time"/>
        <result property="ip" column="ip"/>
        <result property="source" column="source"/>
        <result property="ordertype" column="ordertype"/>
        <result property="sourceOrdercode" column="source_ordercode"/>
        <result property="note" column="note"/>
        <result property="mid" column="mid"/>
        <result property="insurance" column="insurance"/>
        <result property="stockInsId" column="stock_ins_id"/>
        <result property="stockInsTitle" column="stock_ins_title"/>
        <result property="sex" column="sex"/>
        <result property="surname" column="surname"/>
        <result property="username" column="username"/>
        <result property="mobile" column="mobile"/>
        <result property="email" column="email"/>
        <result property="passportnum" column="passportnum"/>
        <result property="appointCarcolor" column="appoint_carcolor"/>
        <result property="appointCarcode" column="appoint_carcode"/>
        <result property="price" column="price"/>
        <result property="insuranceprice" column="insuranceprice"/>
        <result property="totalprice" column="totalprice"/>
        <result property="payprice" column="payprice"/>
        <result property="localPayprice" column="local_payprice"/>
        <result property="paytime" column="paytime"/>
        <result property="paytype" column="paytype"/>
        <result property="deposit" column="deposit"/>
        <result property="depositPaytype" column="deposit_paytype"/>
        <result property="confirmtime" column="confirmtime"/>
        <result property="confirmip" column="confirmip"/>
        <result property="confirmmid" column="confirmmid"/>
        <result property="contactline" column="contactline"/>
        <result property="confirmcode" column="confirmcode"/>
        <result property="status" column="status"/>
        <result property="getcartime" column="getcartime"/>
        <result property="realGetcartime" column="real_getcartime"/>
        <result property="getcarmid" column="getcarmid"/>
        <result property="carid" column="carid"/>
        <result property="stockid" column="stockid"/>
        <result property="carname" column="carname"/>
        <result property="carimg" column="carimg"/>
        <result property="carcode" column="carcode"/>
        <result property="carcolor" column="carcolor"/>
        <result property="platenumber" column="platenumber"/>
        <result property="returntime" column="returntime"/>
        <result property="realReturntime" column="real_returntime"/>
        <result property="returnmid" column="returnmid"/>
        <result property="returnstoreid" column="returnstoreid"/>
        <result property="returnstorename" column="returnstorename"/>
        <result property="orderEndtime" column="order_endtime"/>
        <result property="orderEndmid" column="order_endmid"/>
        <result property="mileage" column="mileage"/>
        <result property="returnDeposit" column="return_deposit"/>
        <result property="returnDepositStatus" column="return_deposit_status"/>
        <result property="returnDepositTime" column="return_deposit_time"/>
        <result property="returnDepositMid" column="return_deposit_mid"/>
        <result property="canceltime" column="canceltime"/>
        <result property="cancelmid" column="cancelmid"/>
        <result property="cancelprice" column="cancelprice"/>
        <result property="edittime" column="edittime"/>
        <result property="editmid" column="editmid"/>
        <result property="deltime" column="deltime"/>
        <result property="delmid" column="delmid"/>
        <result property="extraprice" column="extraprice"/>
        <result property="extranote" column="extranote"/>
        <result property="sesame" column="sesame"/>
        <result property="grade" column="grade"/>
        <result property="otherprice" column="otherprice"/>
        <result property="othernote" column="othernote"/>
        <result property="licensePic" column="license_pic"/>
        <result property="canceledstatus" column="canceledstatus"/>
        <result property="canceledmid" column="canceledmid"/>
        <result property="discountprice" column="discountprice"/>
        <result property="daymileage" column="daymileage"/>
        <result property="morenote" column="morenote"/>
        <result property="refusetime" column="refusetime"/>
        <result property="refusemid" column="refusemid"/>
        <result property="childseat" column="childseat"/>
        <result property="childseatprice" column="childseatprice"/>
        <result property="childseatPrepay" column="childseat_prepay"/>
        <result property="overtimecost" column="overtimecost"/>
        <result property="overtimePrepay" column="overtime_prepay"/>
        <result property="carreturnPrice" column="carreturn_price"/>
        <result property="ctripprice" column="ctripprice"/>
        <result property="ordertypes" column="ordertypes"/>
        <result property="discountCode" column="discount_code"/>
        <result property="cardnumber" column="cardnumber"/>
        <result property="termofvalidity" column="termofvalidity"/>
        <result property="cvvnumbber" column="cvvnumbber"/>
        <result property="exchangeRate" column="exchange_rate"/>
        <result property="discountDayprice" column="discount_dayprice"/>
        <result property="unit" column="unit"/>
        <result property="isRate" column="is_rate"/>
        <result property="rateCode" column="rate_code"/>
        <result property="orderRangeId" column="order_range_id"/>
        <result property="active" column="active"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createType" column="create_type"/>
        <result property="vendorId" column="vendor_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,active,deleted,create_time,update_time,ordercode,
        appoint_carid,appoint_stockid,appoint_getstoreid,appoint_returnstoreid,appoint_getstorename,
        appoint_returnstorename,appoint_carname,appoint_carimg,appoint_platenumber,appoint_starttime,
        appoint_endtime,appoint_days,ticket_address,time,ip,
        source,ordertype,source_ordercode,note,mid,
        insurance,stock_ins_id,stock_ins_title,sex,surname,
        username,mobile,email,passportnum,appoint_carcolor,
        appoint_carcode,price,insuranceprice,totalprice,payprice,
        local_payprice,paytime,paytype,deposit,deposit_paytype,
        confirmtime,confirmip,confirmmid,contactline,confirmcode,
        status,getcartime,real_getcartime,getcarmid,carid,
        stockid,carname,carimg,carcode,carcolor,
        platenumber,returntime,real_returntime,returnmid,returnstoreid,
        returnstorename,order_endtime,order_endmid,mileage,return_deposit,
        return_deposit_status,return_deposit_time,return_deposit_mid,canceltime,cancelmid,
        cancelprice,edittime,editmid,deltime,delmid,
        extraprice,extranote,sesame,grade,otherprice,
        othernote,license_pic,canceledstatus,canceledmid,discountprice,
        daymileage,morenote,refusetime,refusemid,childseat,
        childseatprice,childseat_prepay,overtimecost,overtime_prepay,carreturn_price,
        ctripprice,ordertypes,discount_code,cardnumber,termofvalidity,
        cvvnumbber,exchange_rate,discount_dayprice,unit,is_rate,
        rate_code,order_range_id,create_type, vendor_id
    </sql>

    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO jipinzuche_car_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ordercode!=null">
                ordercode,
            </if>
            <if test="orderRangeId!=null">
                order_range_id,
            </if>
            <if test="appointCarid!=null">
                appoint_carid,
            </if>
            <if test="appointStockid!=null">
                appoint_stockid,
            </if>
            <if test="appointGetstoreid!=null">
                appoint_getstoreid,
            </if>
            <if test="appointReturnstoreid!=null">
                appoint_returnstoreid,
            </if>
            <if test="appointGetstorename!=null">
                appoint_getstorename,
            </if>
            <if test="appointReturnstorename!=null">
                appoint_returnstorename,
            </if>
            <if test="appointCarname!=null">
                appoint_carname,
            </if>
            <if test="appointCarimg!=null">
                appoint_carimg,
            </if>
            <if test="appointPlatenumber!=null">
                appoint_platenumber,
            </if>
            <if test="appointStarttime!=null">
                appoint_starttime,
            </if>
            <if test="appointEndtime!=null">
                appoint_endtime,
            </if>
            <if test="appointDays!=null">
                appoint_days,
            </if>
            <if test="ticketAddress!=null">
                ticket_address,
            </if>
            <if test="time!=null">
                `time`,
            </if>
            <if test="ip!=null">
                ip,
            </if>
            <if test="source!=null">
                `source`,
            </if>
            <if test="ordertype!=null">
                ordertype,
            </if>
            <if test="sourceOrdercode!=null">
                source_ordercode,
            </if>
            <if test="note!=null">
                note,
            </if>
            <if test="mid!=null">
                mid,
            </if>
            <if test="insurance!=null">
                insurance,
            </if>
            <if test="stockInsId!=null">
                stock_ins_id,
            </if>
            <if test="stockInsTitle!=null">
                stock_ins_title,
            </if>
            <if test="sex!=null">
                sex,
            </if>
            <if test="surname!=null">
                surname,
            </if>
            <if test="username!=null">
                username,
            </if>
            <if test="mobile!=null">
                mobile,
            </if>
            <if test="email!=null">
                email,
            </if>
            <if test="passportnum!=null">
                passportnum,
            </if>
            <if test="appointCarcolor!=null">
                appoint_carcolor,
            </if>
            <if test="appointCarcode!=null">
                appoint_carcode,
            </if>
            <if test="price!=null">
                price,
            </if>
            <if test="insuranceprice!=null">
                insuranceprice,
            </if>
            <if test="totalprice!=null">
                totalprice,
            </if>
            <if test="payprice!=null">
                payprice,
            </if>
            <if test="localPayprice!=null">
                local_payprice,
            </if>
            <if test="paytime!=null">
                paytime,
            </if>
            <if test="paytype!=null">
                paytype,
            </if>
            <if test="deposit!=null">
                deposit,
            </if>
            <if test="depositPaytype!=null">
                deposit_paytype,
            </if>
            <if test="confirmtime!=null">
                confirmtime,
            </if>
            <if test="confirmip!=null">
                confirmip,
            </if>
            <if test="confirmmid!=null">
                confirmmid,
            </if>
            <if test="contactline!=null">
                contactline,
            </if>
            <if test="confirmcode!=null">
                confirmcode,
            </if>
            <if test="status!=null">
                `status`,
            </if>
            <if test="getcartime!=null">
                getcartime,
            </if>
            <if test="realGetcartime!=null">
                real_getcartime,
            </if>
            <if test="getcarmid!=null">
                getcarmid,
            </if>
            <if test="carid!=null">
                carid,
            </if>
            <if test="stockid!=null">
                stockid,
            </if>
            <if test="carname!=null">
                carname,
            </if>
            <if test="carimg!=null">
                carimg,
            </if>
            <if test="carcode!=null">
                carcode,
            </if>
            <if test="carcolor!=null">
                carcolor,
            </if>
            <if test="platenumber!=null">
                platenumber,
            </if>
            <if test="returntime!=null">
                returntime,
            </if>
            <if test="realReturntime!=null">
                real_returntime,
            </if>
            <if test="returnmid!=null">
                returnmid,
            </if>
            <if test="returnstoreid!=null">
                returnstoreid,
            </if>
            <if test="returnstorename!=null">
                returnstorename,
            </if>
            <if test="orderEndtime!=null">
                order_endtime,
            </if>
            <if test="orderEndmid!=null">
                order_endmid,
            </if>
            <if test="mileage!=null">
                mileage,
            </if>
            <if test="returnDeposit!=null">
                return_deposit,
            </if>
            <if test="returnDepositStatus!=null">
                return_deposit_status,
            </if>
            <if test="returnDepositTime!=null">
                return_deposit_time,
            </if>
            <if test="returnDepositMid!=null">
                return_deposit_mid,
            </if>
            <if test="canceltime!=null">
                canceltime,
            </if>
            <if test="cancelmid!=null">
                cancelmid,
            </if>
            <if test="cancelprice!=null">
                cancelprice,
            </if>
            <if test="edittime!=null">
                edittime,
            </if>
            <if test="editmid!=null">
                editmid,
            </if>
            <if test="deltime!=null">
                deltime,
            </if>
            <if test="delmid!=null">
                delmid,
            </if>
            <if test="extraprice!=null">
                extraprice,
            </if>
            <if test="extranote!=null">
                extranote,
            </if>
            <if test="sesame!=null">
                sesame,
            </if>
            <if test="grade!=null">
                grade,
            </if>
            <if test="otherprice!=null">
                otherprice,
            </if>
            <if test="othernote!=null">
                othernote,
            </if>
            <if test="licensePic!=null">
                license_pic,
            </if>
            <if test="canceledstatus!=null">
                canceledstatus,
            </if>
            <if test="canceledmid!=null">
                canceledmid,
            </if>
            <if test="discountprice!=null">
                discountprice,
            </if>
            <if test="daymileage!=null">
                daymileage,
            </if>
            <if test="morenote!=null">
                morenote,
            </if>
            <if test="refusetime!=null">
                refusetime,
            </if>
            <if test="refusemid!=null">
                refusemid,
            </if>
            <if test="childseat!=null">
                childseat,
            </if>
            <if test="childseatprice!=null">
                childseatprice,
            </if>
            <if test="childseatPrepay!=null">
                childseat_prepay,
            </if>
            <if test="overtimecost!=null">
                overtimecost,
            </if>
            <if test="overtimePrepay!=null">
                overtime_prepay,
            </if>
            <if test="carreturnPrice!=null">
                carreturn_price,
            </if>
            <if test="ctripprice!=null">
                ctripprice,
            </if>
            <if test="ordertypes!=null">
                ordertypes,
            </if>
            <if test="discountCode!=null">
                discount_code,
            </if>
            <if test="cardnumber!=null">
                cardnumber,
            </if>
            <if test="termofvalidity!=null">
                termofvalidity,
            </if>
            <if test="cvvnumbber!=null">
                cvvnumbber,
            </if>
            <if test="exchangeRate!=null">
                exchange_rate,
            </if>
            <if test="discountDayprice!=null">
                discount_dayprice,
            </if>
            <if test="unit!=null">
                unit,
            </if>
            <if test="isRate!=null">
                is_rate,
            </if>
            <if test="rateCode!=null">
                rate_code,
            </if>
            <if test="createType!=null">
                create_type,
            </if>
            <if test="vendorId!=null">
                vendor_id,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ordercode!=null">
                #{ordercode},
            </if>
            <if test="orderRangeId!=null">
                #{orderRangeId},
            </if>
            <if test="appointCarid!=null">
                #{appointCarid},
            </if>
            <if test="appointStockid!=null">
                #{appointStockid},
            </if>
            <if test="appointGetstoreid!=null">
                #{appointGetstoreid},
            </if>
            <if test="appointReturnstoreid!=null">
                #{appointReturnstoreid},
            </if>
            <if test="appointGetstorename!=null">
                #{appointGetstorename},
            </if>
            <if test="appointReturnstorename!=null">
                #{appointReturnstorename},
            </if>
            <if test="appointCarname!=null">
                #{appointCarname},
            </if>
            <if test="appointCarimg!=null">
                #{appointCarimg},
            </if>
            <if test="appointPlatenumber!=null">
                #{appointPlatenumber},
            </if>
            <if test="appointStarttime!=null">
                #{appointStarttime},
            </if>
            <if test="appointEndtime!=null">
                #{appointEndtime},
            </if>
            <if test="appointDays!=null">
                #{appointDays},
            </if>
            <if test="ticketAddress!=null">
                #{ticketAddress},
            </if>
            <if test="time!=null">
                #{time},
            </if>
            <if test="ip!=null">
                #{ip},
            </if>
            <if test="source!=null">
                #{source},
            </if>
            <if test="ordertype!=null">
                #{ordertype},
            </if>
            <if test="sourceOrdercode!=null">
                #{sourceOrdercode},
            </if>
            <if test="note!=null">
                #{note},
            </if>
            <if test="mid!=null">
                #{mid},
            </if>
            <if test="insurance!=null">
                #{insurance},
            </if>
            <if test="stockInsId!=null">
                #{stockInsId},
            </if>
            <if test="stockInsTitle!=null">
                #{stockInsTitle},
            </if>
            <if test="sex!=null">
                #{sex},
            </if>
            <if test="surname!=null">
                #{surname},
            </if>
            <if test="username!=null">
                #{username},
            </if>
            <if test="mobile!=null">
                #{mobile},
            </if>
            <if test="email!=null">
                #{email},
            </if>
            <if test="passportnum!=null">
                #{passportnum},
            </if>
            <if test="appointCarcolor!=null">
                #{appointCarcolor},
            </if>
            <if test="appointCarcode!=null">
                #{appointCarcode},
            </if>
            <if test="price!=null">
                #{price},
            </if>
            <if test="insuranceprice!=null">
                #{insuranceprice},
            </if>
            <if test="totalprice!=null">
                #{totalprice},
            </if>
            <if test="payprice!=null">
                #{payprice},
            </if>
            <if test="localPayprice!=null">
                #{localPayprice},
            </if>
            <if test="paytime!=null">
                #{paytime},
            </if>
            <if test="paytype!=null">
                #{paytype},
            </if>
            <if test="deposit!=null">
                #{deposit},
            </if>
            <if test="depositPaytype!=null">
                #{depositPaytype},
            </if>
            <if test="confirmtime!=null">
                #{confirmtime},
            </if>
            <if test="confirmip!=null">
                #{confirmip},
            </if>
            <if test="confirmmid!=null">
                #{confirmmid},
            </if>
            <if test="contactline!=null">
                #{contactline},
            </if>
            <if test="confirmcode!=null">
                #{confirmcode},
            </if>
            <if test="status!=null">
                #{status},
            </if>
            <if test="getcartime!=null">
                #{getcartime},
            </if>
            <if test="realGetcartime!=null">
                #{realGetcartime},
            </if>
            <if test="getcarmid!=null">
                #{getcarmid},
            </if>
            <if test="carid!=null">
                #{carid},
            </if>
            <if test="stockid!=null">
                #{stockid},
            </if>
            <if test="carname!=null">
                #{carname},
            </if>
            <if test="carimg!=null">
                #{carimg},
            </if>
            <if test="carcode!=null">
                #{carcode},
            </if>
            <if test="carcolor!=null">
                #{carcolor},
            </if>
            <if test="platenumber!=null">
                #{platenumber},
            </if>
            <if test="returntime!=null">
                #{returntime},
            </if>
            <if test="realReturntime!=null">
                #{realReturntime},
            </if>
            <if test="returnmid!=null">
                #{returnmid},
            </if>
            <if test="returnstoreid!=null">
                #{returnstoreid},
            </if>
            <if test="returnstorename!=null">
                #{returnstorename},
            </if>
            <if test="orderEndtime!=null">
                #{orderEndtime},
            </if>
            <if test="orderEndmid!=null">
                #{orderEndmid},
            </if>
            <if test="mileage!=null">
                #{mileage},
            </if>
            <if test="returnDeposit!=null">
                #{returnDeposit},
            </if>
            <if test="returnDepositStatus!=null">
                #{returnDepositStatus},
            </if>
            <if test="returnDepositTime!=null">
                #{returnDepositTime},
            </if>
            <if test="returnDepositMid!=null">
                #{returnDepositMid},
            </if>
            <if test="canceltime!=null">
                #{canceltime},
            </if>
            <if test="cancelmid!=null">
                #{cancelmid},
            </if>
            <if test="cancelprice!=null">
                #{cancelprice},
            </if>
            <if test="edittime!=null">
                #{edittime},
            </if>
            <if test="editmid!=null">
                #{editmid},
            </if>
            <if test="deltime!=null">
                #{deltime},
            </if>
            <if test="delmid!=null">
                #{delmid},
            </if>
            <if test="extraprice!=null">
                #{extraprice},
            </if>
            <if test="extranote!=null">
                #{extranote},
            </if>
            <if test="sesame!=null">
                #{sesame},
            </if>
            <if test="grade!=null">
                #{grade},
            </if>
            <if test="otherprice!=null">
                #{otherprice},
            </if>
            <if test="othernote!=null">
                #{othernote},
            </if>
            <if test="licensePic!=null">
                #{licensePic},
            </if>
            <if test="canceledstatus!=null">
                #{canceledstatus},
            </if>
            <if test="canceledmid!=null">
                #{canceledmid},
            </if>
            <if test="discountprice!=null">
                #{discountprice},
            </if>
            <if test="daymileage!=null">
                #{daymileage},
            </if>
            <if test="morenote!=null">
                #{morenote},
            </if>
            <if test="refusetime!=null">
                #{refusetime},
            </if>
            <if test="refusemid!=null">
                #{refusemid},
            </if>
            <if test="childseat!=null">
                #{childseat},
            </if>
            <if test="childseatprice!=null">
                #{childseatprice},
            </if>
            <if test="childseatPrepay!=null">
                #{childseatPrepay},
            </if>
            <if test="overtimecost!=null">
                #{overtimecost},
            </if>
            <if test="overtimePrepay!=null">
                #{overtimePrepay},
            </if>
            <if test="carreturnPrice!=null">
                #{carreturnPrice},
            </if>
            <if test="ctripprice!=null">
                #{ctripprice},
            </if>
            <if test="ordertypes!=null">
                #{ordertypes},
            </if>
            <if test="discountCode!=null">
                #{discountCode},
            </if>
            <if test="cardnumber!=null">
                #{cardnumber},
            </if>
            <if test="termofvalidity!=null">
                #{termofvalidity},
            </if>
            <if test="cvvnumbber!=null">
                #{cvvnumbber},
            </if>
            <if test="exchangeRate!=null">
                #{exchangeRate},
            </if>
            <if test="discountDayprice!=null">
                #{discountDayprice},
            </if>
            <if test="unit!=null">
                #{unit},
            </if>
            <if test="isRate!=null">
                #{isRate},
            </if>
            <if test="rateCode!=null">
                #{rateCode},
            </if>
            <if test="createType!=null">
                #{createType},
            </if>
            <if test="vendorId!=null">
                #{vendorId},
            </if>
        </trim>
    </insert>
    <select id="selectById" resultType="com.ixtech.management.repo.entity.JipinzucheCarOrderPO">
        select
            <include refid="Base_Column_List" />
            from jipinzuche_car_order
        where id = #{id}
        and active = 1 and deleted = 0 and status != -2
    </select>
    <select id="selectBySourceOrderCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_order
        where source_ordercode = #{sourceOrdercode}
        and active = 1 and deleted = 0 and status != -2
    </select>
    <select id="selectCountBySourceOrderCode" resultType="java.lang.Long">
            select
            count(id)
            from jipinzuche_car_order
            where source_ordercode = #{sourceOrdercode}
            and active = 1 and deleted = 0 and status != -2
    </select>
    <select id="selectBySourceAndSourceOrderCode"
            resultType="com.ixtech.management.repo.entity.JipinzucheCarOrderPO">
        select
        <include refid="Base_Column_List" />
        from jipinzuche_car_order
        where source_ordercode = #{sourceOrderId} and source = #{source}
        and active = 1 and deleted = 0 and status != -2
    </select>
</mapper>
