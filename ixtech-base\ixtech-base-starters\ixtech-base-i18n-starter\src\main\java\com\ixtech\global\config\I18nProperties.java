package com.ixtech.global.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.Ordered;

/**
 * 国际化相关属性配置Properties
 *
 * <AUTHOR> hu
 * @date 2025/6/10 14:01
 */
@Data
@ConfigurationProperties(prefix = "i18n")
public class I18nProperties {

    /**
     * 本地缓存配置
     */
    private LocalCache localCache = new LocalCache();

    /**
     * 语言过滤器配置
     */
    private LanguageFilter languageFilter = new LanguageFilter();

    /**
     * 翻译feign调用配置
     */
    private Feign feign = new Feign();

    /**
     * 对响应数据进行拦截的翻译拦截器执行顺序，默认在最后执行
     */
    private Integer i18nResponseBodyAdviceOrder = Ordered.LOWEST_PRECEDENCE;

    @Data
    public static class LocalCache {

        /**
         * 是否启用本地缓存
         */
        private Boolean enable = true;

        /**
         * 本地缓存最大缓存数量
         */
        private Long maximumSize = 1000L;

        /**
         * 本地缓存访问后多久过期（单位秒）
         */
        private Integer expireAfterAccess = 300;

    }

    @Data
    public static class LanguageFilter {

        /**
         * 过滤器顺序，默认在最前
         */
        private Integer order = Ordered.HIGHEST_PRECEDENCE;

        /**
         * 拦截的url
         */
        private String urlPatterns = "/*";

    }

    @Data
    public static class Feign {

        /**
         * 连接超时（单位毫秒）
         */
        private int connectTimeout = 2000;

        /**
         * 读取超时（单位毫秒）
         */
        private int readTimeout = 2000;

    }

}
