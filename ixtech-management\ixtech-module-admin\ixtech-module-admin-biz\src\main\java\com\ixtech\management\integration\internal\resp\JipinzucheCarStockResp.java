package com.ixtech.management.integration.internal.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 车辆库存表
 * @TableName jipinzuche_car_stock
 */
@Data
public class JipinzucheCarStockResp implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * #__car_model 的id
     */
    private Integer modelid;

    /**
     * 门店id
     */
    private Integer storeid;

    /**
     * 车辆数量
     */
    private Integer stock;

    /**
     * 同组名
     */
    private String groupName;

    /**
     * 起赔额
     */
    private Double accident;

    /**
     * 起赔额单位(如：RMB，THB)
     */
    private String accidentUnit;

    /**
     * 添加人
     */
    private String time;

    /**
     * ip
     */
    private String ip;

    /**
     * 添加人
     */
    private Integer mid;

    /**
     * 1->有效；-1->删除
     */
    private Integer status;

    private static final long serialVersionUID = 1L;
}