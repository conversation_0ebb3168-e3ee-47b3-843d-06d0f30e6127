package com.ixtech.management.common.enums;

import lombok.Getter;

/**
 * 结算货币类型枚举
 *
 * <AUTHOR> hu
 * @date 2025/4/4 11:24
 */
@Getter
public enum SettlementCurrencyEnum implements BaseEnum<String> {

    AED("AED", "阿联酋"),
    AUD("AUD", "澳大利亚"),
    BDT("BDT", "孟加拉国"),
    BRL("BRL", "巴西"),
    CAD("CAD", "加拿大"),
    CHF("CHF", "瑞士"),
    CNY("CNY", "中国"),
    DKK("DKK", "丹麦"),
    EUR("EUR", "欧元区"),
    GBP("GBP", "英国"),
    HKD("HKD", "中国香港特别行政区"),
    IDR("IDR", "印度尼西亚"),
    INR("INR", "印度"),
    JPY("JPY", "日本"),
    KRW("KRW", "韩国"),
    MXN("MXN", "墨西哥"),
    MYR("MYR", "马来西亚"),
    NOK("NOK", "挪威"),
    NZD("NZD", "新西兰"),
    PHP("PHP", "菲律宾"),
    PKR("PKR", "巴基斯坦"),
    RUB("RUB", "俄罗斯"),
    SAR("SAR", "沙特阿拉伯"),
    SEK("SEK", "瑞典"),
    SGD("SGD", "新加坡"),
    THB("THB", "泰国"),
    TWD("TWD", "台湾地区"),
    USD("USD", "美国"),
    VND("VND", "越南"),
    ZAR("ZAR", "南非"),
    ;

    private final String code;
    private final String name;

    SettlementCurrencyEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return asString();
    }

}
