package com.ixtech.management.repo.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName jipinzuche_car_stock_insurance
 */
@Data
public class JipinzucheCarStockInsurance implements Serializable {
    /**
     * 
     */
    private Integer id;

    /**
     * active
     */
    private Boolean active;

    /**
     * deleted
     */
    private Boolean deleted;

    /**
     * create_time in UTC
     */
    private Date createTime;

    /**
     * update_time in UTC
     */
    private Date updateTime;

    /**
     * car_stock的id
     */
    private Integer stockid;

    /**
     * insurance的id
     */
    private Integer insid;

    /**
     * 简介
     */
    private String info;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 创建ip
     */
    private String ip;

    /**
     * 创建人
     */
    private Integer mid;

    /**
     * 最后修改时间
     */
    private Date lastedittime;

    /**
     * 最后修改人
     */
    private Integer lasteditmid;

    /**
     * 最后修改ip
     */
    private String lasteditip;

    /**
     * 0->未删除；1->已删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;
}