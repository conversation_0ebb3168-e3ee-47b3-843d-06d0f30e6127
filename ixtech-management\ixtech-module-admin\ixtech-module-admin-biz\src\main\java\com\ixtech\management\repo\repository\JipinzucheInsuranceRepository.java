package com.ixtech.management.repo.repository;

import com.ixtech.management.repo.entity.JipinzucheCarModel;
import com.ixtech.management.repo.entity.JipinzucheInsurance;
import com.ixtech.management.repo.mapper.JipinzucheCarModelMapper;
import com.ixtech.management.repo.mapper.JipinzucheInsuranceMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> hu
 * @date 2025/4/12 17:39
 */
@Repository
public class JipinzucheInsuranceRepository {

    @Resource
    private JipinzucheInsuranceMapper jipinzucheInsuranceMapper;

    public JipinzucheInsurance selectById(Long id){
        return jipinzucheInsuranceMapper.selectById(id);
    }
}
