<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

    <context id="krtd-user" targetRuntime="MyBatis3">
        <!-- 反引号包裹字段 -->
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- 每次执行插件生成的 xml 时通用的方法会覆盖的 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>

        <!-- 生成的pojo，将implements Serializable -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="********************************************"
                        userId="root" password="Cz#nyson/3Qp">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.ixtech.global.repo.entity"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mybatis/mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.ixtech.global.repo.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <table tableName="ix_channel" domainObjectName="IxChannelPO" mapperName="IxChannelMapper">
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
            <columnOverride column="id" javaType="java.lang.Long" />
            <columnOverride column="active" javaType="java.lang.Boolean" />
            <columnOverride column="deleted" javaType="java.lang.Boolean" />
            <columnOverride column="create_time" javaType="java.time.LocalDateTime" />
            <columnOverride column="update_time" javaType="java.time.LocalDateTime" />
            <columnOverride column="title" javaType="java.lang.String" />
            <columnOverride column="stage" javaType="java.lang.String" />
            <columnOverride column="is_delete" javaType="java.lang.Integer" />
            <columnOverride column="company_full_name" javaType="java.lang.String" />
            <columnOverride column="quote_currency" javaType="java.lang.String" />
            <columnOverride column="settlement_currency" javaType="java.lang.String" />
            <columnOverride column="settlement_mode" javaType="java.lang.Integer" />
            <columnOverride column="rate" javaType="java.math.BigDecimal" />
        </table>
    </context>
</generatorConfiguration>
