package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 位置详细信息
 *
 * @author: Phili
 * @date： 2025/3/31
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AddressResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 城市名称
  */
 private String cityName;

 /**
  * 国家代码
  */
 private String countryCode;

 /**
  * 国家名称
  */
 private String countryName;

 /**
  * 位置坐标
  */
 private Position position;

 /**
  * 邮政编码
  */
 private String postalCode;

 /**
  * 州/省代码
  */
 private String stateProvCode;

 /**
  * 州/省名称
  */
 private String stateProvName;

 /**
  * 街道信息
  */
 private String address;

 @Data
 @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
 public static class Position {

  /**
   * 纬度
   */
  private BigDecimal latitude;

  /**
   * 经度
   */
  private BigDecimal longitude;
 }
}
