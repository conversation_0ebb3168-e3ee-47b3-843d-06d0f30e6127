package com.ixtech.global.integration.internal.resp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 费率限定(优惠活动) resp
 *
 * @author: Phili
 * @date： 2025/3/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RateQualifierResp implements Serializable {

 private static final long serialVersionUID = 1L;

 /**
  * 合同代码
  */
 private String contractCode;

 /**
  * 公司折扣编号
  */
 private String corpDiscountNmbr;

 /**
  * 促销代码
  */
 private String promotionCode;

 /**
  * 费率类别
  */
 private String rateCategory;

 /**
  * 费率限定符
  */
 private String rateQualifier;
}
