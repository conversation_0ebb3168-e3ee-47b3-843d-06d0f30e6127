package com.ixtech.management.integration.internal.client;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.integration.internal.req.CarStockInsuranceReq;
import com.ixtech.management.integration.internal.req.ReferenceGenerateReq;
import com.ixtech.management.integration.internal.req.VendorClearCacheReq;
import com.ixtech.management.integration.internal.resp.CarStockInsuranceManagementModel;
import com.ixtech.management.repo.entity.CarModelPO;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 车辆相关服务 Feign 客户端接口
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Component
@FeignClient(name = "productsrv")
public interface ProductsrvFeignClient {

    @RequestMapping("/v1/productsrv/internal/vehicle/ref_generate")
    ApiResponse<String> refGenerate(@RequestBody @Valid ReferenceGenerateReq request);

    @PostMapping("/v1/productsrv/internal/vehicle/veh_model_name_query")
    ApiResponse<CarModelPO> velModelNameQuery(@RequestParam("modelName") @Valid String modelName);

    @PostMapping("/v1/productsrv/internal/vehicle/veh_car_stock_insurance_query")
    ApiResponse<CarStockInsuranceManagementModel> velCarStockInsuranceTitleQuery(@RequestBody CarStockInsuranceReq request);

    @PostMapping("/v1/productsrv/internal/vehicle/clear_vendor_cache")
    ApiResponse clearVendorCache(@RequestBody VendorClearCacheReq req);
}