package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 接送机订单类型
 *
 * <AUTHOR> hu
 * @date 2025/7/20 18:35
 */
@Getter
public enum TransferOrderTypeEnum {

    PICK_IP(1, "接机"),
    DROP_OFF(2, "送机"),
    ;

    TransferOrderTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 订单类型code
     */
    private final Integer code;

    /**
     * 订单类型描述
     */
    private final String value;

    public static TransferOrderTypeEnum valueOfCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferOrderTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
