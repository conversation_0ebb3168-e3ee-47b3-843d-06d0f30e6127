package com.ixtech.global.common.config;

import com.github.yitter.idgen.YitIdHelper;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ID 生成器配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "yit.id-generator")
@ConditionalOnClass(YitIdHelper.class)
public class IdGeneratorProperties {

    /**
     * 工作节点ID (WorkerId)。
     * 如果不配置此项，系统将尝试根据 MAC/IP 地址自动生成。
     * 如果配置，必须在 [0, maxWorkerId] 范围内。maxWorkerId 可通过 workerIdBitLength 计算。
     */
    private Short workerId;

    /**
     * WorkerId 的位数，默认 6。
     * 范围 [1, 21]，推荐 6-10。
     * 如果设置为10，则 WorkerId 最大值为 2^10 - 1 = 1023。
     */
    private byte workerIdBitLength = 6;

    /**
     * 序列号（自增量）的位数，默认 6。
     * 范围 [2, 21]，推荐 6-10。
     * 如果设置为10，则每毫秒可生成 2^10 = 1024 个ID。
     */
    private byte seqBitLength = 6;

    /**
     * 基础时间（时间戳基准），格式 "yyyy-MM-dd"。
     * 如果不设置，默认为 "2020-02-20"。
     */
    private String baseTime = "2020-02-20";

}
