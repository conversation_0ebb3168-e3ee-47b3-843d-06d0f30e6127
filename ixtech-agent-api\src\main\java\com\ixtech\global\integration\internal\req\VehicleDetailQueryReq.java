package com.ixtech.global.integration.internal.req;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车辆详情req
 *
 * <AUTHOR>
 * @date  2025/3/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class VehicleDetailQueryReq extends VehicleAvailListQueryReq {

 private static final long serialVersionUID = 7985504938627085943L;

 /**
  * 搜索唯一的维度：供应商code:门店code:SIPP:车辆modelId:保险code
  */
 @NotNull(message = "referenceId不能为空")
 private String referenceId;

}
