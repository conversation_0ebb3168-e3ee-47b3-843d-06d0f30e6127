<?xml version="1.0" encoding="UTF-8"?>

<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">


    <pluginGroups>

    </pluginGroups>

    <proxies>

    </proxies>


    <servers>
        <server>
            <id>rdc-snapshots</id>
            <username>677b965925be8f24244f2a4c</username>
            <password>SSom3P)AOV7D</password>
        </server>
    </servers>
    <mirrors>
        <mirror>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://repo.maven.apache.org/maven2/</url>
            <mirrorOf>central,jcenter,spring-plugin,!snapshot</mirrorOf>
        </mirror>
    </mirrors>


    <profiles>
        <profile>
            <id>jdk-21</id>
            <activation>
                <jdk>21</jdk>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <maven.compiler.source>21</maven.compiler.source>
                <maven.compiler.target>21</maven.compiler.target>
                <maven.compiler.compilerVersion>21</maven.compiler.compilerVersion>
            </properties>
        </profile>

        <profile>
            <id>rdc</id>
            <repositories>
                <repository>
                    <id>rdc-snapshots</id>
                    <url>https://packages.aliyun.com/677b967610def31b486d77d9/maven/2513107-snapshot-nrql5x</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
        </profile>


        <profile>
            <id>sn-deploy</id>
            <properties>
                <altSnapshotDeploymentRepository>
                    rdc-snapshots::default::https://packages.aliyun.com/677b967610def31b486d77d9/maven/2513107-snapshot-nrql5x
                </altSnapshotDeploymentRepository>
            </properties>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>rdc</activeProfile>
    </activeProfiles>
</settings>
