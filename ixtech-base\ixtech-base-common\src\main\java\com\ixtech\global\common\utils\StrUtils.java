package com.ixtech.global.common.utils;

import java.util.*;
import java.util.stream.Collectors;

public class StrUtils {
    /**
     * 将字符串按分隔符分割为列表
     *
     * @param input     输入字符串
     * @param delimiter 分隔符
     * @param trim      是否去除每个元素的首尾空白
     * @return 分割后的列表
     */
    public static List<String> splitToList(String input, String delimiter, boolean trim) {
        if (input == null || delimiter == null || input.isEmpty()) {
            return Collections.emptyList();
        }
        String[] splitArray = input.split(delimiter);
        List<String> result = new ArrayList<>(splitArray.length);
        for (String item : splitArray) {
            String value = trim ? item.trim() : item;
            if (!value.isEmpty()) { // 跳过空字符串，可选
                result.add(value);
            }
        }
        return result;
    }

    // 重载方法，默认不 trim
    public static List<String> splitToList(String input, String delimiter) {
        return splitToList(input, delimiter, false);
    }

    /**
     * 对可排序对象集合排序并拼接
     *
     * @param collection 输入的集合，元素需实现 Comparable 接口
     * @param delimiter  拼接分隔符
     * @param <T>        元素类型，需实现 Comparable
     * @return 排序后的拼接字符串，若集合为空或null，返回空字符串
     */
    public static <T extends Comparable<T>> String sortAndJoin(Collection<T> collection, String delimiter) {
        if (collection == null || collection.isEmpty()) {
            return "";
        }
        return collection.stream()
                .filter(Objects::nonNull) // 过滤 null
                .sorted() // 使用 Comparable 的自然排序
                .map(Object::toString) // 转换为字符串
                .filter(str -> !str.isEmpty()) // 过滤空字符串
                .collect(Collectors.joining(delimiter));
    }

    /**
     * 对可排序对象集合排序并拼接，使用默认分隔符"-"
     *
     * @param collection 输入的集合，元素需实现 Comparable 接口
     * @param <T>        元素类型，需实现 Comparable
     * @return 排序后的拼接字符串，若集合为空或null，返回空字符串
     */
    public static <T extends Comparable<T>> String sortAndJoin(Collection<T> collection) {
        return sortAndJoin(collection, "-");
    }

    /**
     * 将多个对象转换为字符串并按指定分隔符拼接。
     * 如果对象为 null，则使用空字符串 "" 代替。
     *
     * @param delimiter 拼接分隔符
     * @param objects   要拼接的对象数组
     * @return 拼接后的字符串，若 objects 为空或null，返回空字符串
     */
    public static String join(String delimiter, Object... objects) {
        if (objects == null || objects.length == 0) {
            return "";
        }

        return Arrays.stream(objects)
                .map(obj -> obj == null ? "" : String.valueOf(obj)) // Map null to ""
                .collect(Collectors.joining(delimiter));
    }
}
