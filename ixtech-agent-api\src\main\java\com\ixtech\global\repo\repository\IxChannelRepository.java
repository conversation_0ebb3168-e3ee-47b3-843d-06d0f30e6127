package com.ixtech.global.repo.repository;

import com.ixtech.global.repo.entity.IxChannelPO;
import com.ixtech.global.repo.entity.IxChannelExample;
import com.ixtech.global.repo.mapper.IxChannelMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单来源Repository
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Repository
public class IxChannelRepository {

    @Resource
    private IxChannelMapper ixChannelMapper;

    /**
     * 查询所有订单来源信息
     */
    public List<IxChannelPO> queryAllCarOrderSources() {
        IxChannelExample example = new IxChannelExample();
        IxChannelExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedEqualTo(false);
        criteria.andIsDeleteEqualTo(NumberUtils.INTEGER_ZERO);
        return ixChannelMapper.selectByExample(example);
    }
}
