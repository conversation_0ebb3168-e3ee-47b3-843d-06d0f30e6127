package com.ixtech.management.domain.service;

import com.ixtech.management.common.dto.ApiResponse;
import com.ixtech.management.common.dto.PageResponse;
import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.integration.internal.resp.VendorAddResp;
import com.ixtech.management.integration.internal.resp.VendorListInfoResp;

import java.util.List;

/**
 * 供应商service
 *
 * <AUTHOR> hu
 * @date 2025/4/4 13:15
 */
public interface VendorService {

    /**
     * 查询供应商列表
     *
     * @param vendorListQueryReq
     * @return
     */
    PageResponse<VendorListInfoResp> list(VendorListQueryReq vendorListQueryReq);

    /**
     * 新增供应商
     *
     * @param vendorAddReq
     * @return
     */
    VendorAddResp add(VendorAddReq vendorAddReq);

    /**
     * 更新供应商
     *
     * @param vendorUpdateReq
     */
    void update(VendorUpdateReq vendorUpdateReq);

    /**
     * 供应商上下线
     *
     * @param vendorSetActiveReq
     */
    void setActive(VendorSetActiveReq vendorSetActiveReq);

    /**
     * 供应商下拉列表
     *
     * @param vendorDropdownReq
     * @return
     */
    List<SelectOptionResponse<Long>> dropdownList(VendorDropdownReq vendorDropdownReq);

    ApiResponse<com.ixtech.merchant.resp.IxVendorResp> getVendorById(MerchantVendorQueryReq req);
}
