package com.ixtech.global.common.enums;

import com.ixtech.global.common.enums.inf.DictInf;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 门店状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StoreStatusEnum implements DictInf {

    /**
     * 待上线
     */
    ENABLED("0", "待上线"),
    
    /**
     * 已上线
     */
    ONLINE("1", "已上线"),
    
    /**
     * 已禁用
     */
    DISABLED("2", "已禁用");

    /**
     * 状态值
     */
    private final String value;
    
    /**
     * 状态名称
     */
    private final String label;

    public static String getLabelByValue(String value) {
        for (StoreStatusEnum e : values()) {
            if (String.valueOf(e.value).equals(value)) {
                return e.label;
            }
        }
        return "";
    }

}