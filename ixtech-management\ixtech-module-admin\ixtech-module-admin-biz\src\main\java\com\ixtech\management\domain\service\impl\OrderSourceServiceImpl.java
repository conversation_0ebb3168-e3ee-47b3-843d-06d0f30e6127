package com.ixtech.management.domain.service.impl;

import com.ixtech.management.common.dto.SelectOptionResponse;
import com.ixtech.management.domain.service.OrderSourceService;
import com.ixtech.management.integration.internal.req.*;
import com.ixtech.management.repo.entity.CarOrderSource;
import com.ixtech.management.repo.repository.CarOrderSourceRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商service
 *
 * @date 2025/4/4 13:14
 */
@Slf4j
@Service
public class OrderSourceServiceImpl implements OrderSourceService {

    @Resource
    private CarOrderSourceRepository jipinzucheCarOrderSourceRepository;

    @Override
    public List<SelectOptionResponse<Long>> dropdownList(VendorDropdownReq vendorDropdownReq) {
        List<CarOrderSource> list = jipinzucheCarOrderSourceRepository.queryAllCarOrderSources();
        return CollectionUtils.emptyIfNull(list).stream()
                .map(v -> new SelectOptionResponse<>(v.getId(), v.getTitle())).toList();
    }

}
