package com.ixtech.global.interceptor;

import com.ixtech.global.I18nTranslationExecutor;
import com.ixtech.global.util.RequestUtils;
import jakarta.validation.MessageInterpolator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用于对validate注解中的message属性值进行翻译
 *
 * <AUTHOR> hu
 * @date 2025/6/17 13:02
 */
@Slf4j
public class I18nValidateMessageInterpolator implements MessageInterpolator {

    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{\\{(.*?)}}");

    private MessageInterpolator delegate;
    private I18nTranslationExecutor i18nTranslationExecutor;

    public I18nValidateMessageInterpolator(MessageInterpolator delegate, I18nTranslationExecutor i18nTranslationExecutor) {
        this.delegate = delegate;
        this.i18nTranslationExecutor = i18nTranslationExecutor;
    }

    @Override
    public String interpolate(String message, Context context) {
        // 内容替换，将 {{xxx}} 格式的消息替换为翻译后的内容
        String replacedMessage = replaceMessage(message);
        // 使用默认插值器处理替换后的消息
        return delegate.interpolate(replacedMessage, context);
    }

    @Override
    public String interpolate(String message, Context context, Locale locale) {
        String replacedMessage = replaceMessage(message);
        return delegate.interpolate(replacedMessage, context, locale);
    }

    /**
     * 内容替换，将 {{xxx}} 格式的消息替换为翻译后的内容
     *
     * @param message validate注解中的message属性值
     * @return
     */
    private String replaceMessage(String message) {

        if (message == null || message.isEmpty()) {
            return message;
        }

        try {

            // 获取语言code
            String languageCode = Optional.ofNullable(LanguageContextHolder.getPrimaryLanguage())
                    .map(RequestUtils.LanguagePriority::languageCode).orElse(null);
            if (StringUtils.isBlank(languageCode)) {
                return message;
            }

            StringBuilder sb = new StringBuilder();

            // 遍历所有匹配结果
            Matcher matcher = PLACEHOLDER_PATTERN.matcher(message);
            while (matcher.find()) {
                // 获取占位符内的内容，例如 {{name}} 中的 name
                String placeholder = matcher.group(1);
                // 进行翻译
                String translatedText = i18nTranslationExecutor.getTranslation(placeholder, languageCode);
                // 处理翻译结果中的特殊字符，避免正则表达式问题
                String escapedReplacement = Matcher.quoteReplacement(translatedText);
                // 替换占位符
                matcher.appendReplacement(sb, escapedReplacement);
            }

            // 添加剩余的文本
            matcher.appendTail(sb);
            return sb.toString();
        } catch (Exception e) {
            // 记录异常信息，实际应用中建议使用日志记录
            log.error(e.getMessage(), e);
            // 发生异常时返回原始消息
            return message;
        }

    }

}
