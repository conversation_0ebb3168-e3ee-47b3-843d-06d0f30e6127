package com.ixtech.management.repo.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ixtech.management.repo.entity.Store;
import com.ixtech.management.repo.model.CountModel;
import com.ixtech.management.repo.model.VendorAreaCountModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【jipinzuche_store(门店表)】的数据库操作Mapper
* @createDate 2025-04-23 12:39:26
* @Entity com.ixtech.management.repo.entity.JipinzucheStore
*/
@DS("ix")
@Mapper
public interface StoreMapper {

    int insert(Store record);

    int insertSelective(Store record);

    Store selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Store record);

    int updateByPrimaryKey(Store record);

    int updateBatch(List<Store> list);

    int updateBatchSelective(List<Store> list);

    int batchInsert(@Param("list") List<Store> list);

    /**
     * 获取指定供应商下门店覆盖国家/城市数量
     *
     * @param vendorIds 供应商id列表
     * @return
     */
    List<VendorAreaCountModel> getStoreCoveredAreaCountByVendorIds(@Param(value = "vendorIds") Collection<Long> vendorIds);

    /**
     * 查询指定供应商的门店数量
     *
     * @param vendorIds
     * @return
     */
    List<CountModel> getStoreCountByVendorIds(@Param("vendorIds") Collection<Long> vendorIds);

    /**
     * 查询指定供应商下门店的车辆数量
     *
     * @param vendorIds 供应商id列表
     * @return
     */
    List<CountModel> getStoreCarCountByVendorIds(@Param("vendorIds") Collection<Long> vendorIds);

    /**
     * 查询指定门店的车辆数量
     *
     * @param storeIds 门店id列表
     * @return
     */
    List<CountModel> getStoreCarCountByStoreIds(@Param(value = "storeIds") Collection<Long> storeIds);

    /**
     * 条件查询门店信息
     *
     * @param name          门店名称
     * @param countryId     国家id
     * @param provinceId    省份id
     * @param cityId        城市id
     * @param vendorId      供应商id
     * @param types         类型列表
     * @param displayStatus 上下线状态
     * @return
     */
    List<Store> selectByCondition(@Param("name") String name,
                                  @Param("countryId") Long countryId,
                                  @Param("provinceId") Long provinceId,
                                  @Param("cityId") Long cityId,
                                  @Param("vendorId") Long vendorId,
                                  @Param("types") List<Integer> types,
                                  @Param("displayStatus") Integer displayStatus);

}




