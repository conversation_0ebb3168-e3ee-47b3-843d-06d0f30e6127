package com.ixtech.global.common.enums.transfer;

import lombok.Getter;

/**
 * 支付方式
 *
 * <AUTHOR> hu
 * @date 2025/7/22 21:30
 */
@Getter
public enum TransferPaymentTypeEnum {

    OFFLINE(1, "线下支付"),
    ONLINE(2, "线上支付"),
    ;

    private final Integer code;
    private final String label;

    TransferPaymentTypeEnum(Integer code, String label) {
        this.code = code;
        this.label = label;
    }

}
