package com.ixtech.management.integration.internal.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 车辆排班请求对象
 */
@Data
public class StockRangeReq {

    /**
     * 车型唯一标识
     */
    @NotNull(message = "车型ID不能为空")
    @JsonProperty("model_id")
    private Long modelId;

    /**
     * 门店唯一标识
     */
    @NotNull(message = "门店ID不能为空")
    @JsonProperty("store_id")
    private Long storeId;

    /**
     * 取车时间
     */
    @NotNull(message = "取车时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonProperty("get_car_time")
    private LocalDateTime getCarTime;

    /**
     * 还车时间
     */
    @NotNull(message = "还车时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonProperty("return_car_time")
    private LocalDateTime returnCarTime;

    /**
     * 还车延迟时间
     */
    @NotNull(message = "还车延迟时间不能为空")
    @JsonProperty("time_delay")
    private Integer timeDelay;
}
